msgid ""
msgstr ""
"Project-Id-Version: Cookiebot | GDPR/CCPA Compliant <PERSON><PERSON> and "
"Control\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/cookiebot\n"
"POT-Creation-Date: 2024-12-18T18:13:49+00:00\n"
"PO-Revision-Date: 2025-01-13 11:27+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: cookiebot.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"

#. Plugin Name of the plugin
#: cookiebot.php
msgid "Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: cookiebot.php
msgid "https://www.cookiebot.com/"
msgstr ""

#. Description of the plugin
#: cookiebot.php
msgid ""
"The Cookiebot CMP WordPress cookie banner and cookie policy help you comply "
"with the major data protection laws (GDPR, ePrivacy, CCPA, LGPD, etc.) in a "
"simple and fully automated way. Secure your website and get peace of mind."
msgstr ""

#. Author of the plugin
#: cookiebot.php
msgid "Usercentrics A/S"
msgstr ""

#: src/addons/config/Settings_Config.php:89
#: src/addons/config/Settings_Config.php:90
#: src/view/admin/common/prior-consent/page.php:24
#: src/view/admin/common/templates/main-tabs.php:39
msgid "Plugins"
msgstr "Plugin"

#: src/addons/config/Settings_Config.php:130
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:25
msgid "Remove language"
msgstr "Rimuovi lingua"

#: src/addons/config/Settings_Config.php:602
msgid "The plugin is not installed."
msgstr "Il plugin non è installato."

#: src/addons/config/Settings_Config.php:605
msgid "The theme is not installed."
msgstr "Il tema non è installato."

#: src/addons/config/Settings_Config.php:609
msgid "The plugin is not activated."
msgstr "Il plugin non è attivato."

#: src/addons/config/Settings_Config.php:612
msgid "The theme is not activated."
msgstr "Il tema non è attivato."

#: src/addons/config/Settings_Config.php:672
#: src/lib/Cookiebot_Admin_Links.php:54 src/lib/Cookiebot_Admin_Links.php:56
#: src/lib/Cookiebot_Admin_Links.php:124
msgid "%s"
msgstr "%s"

#: src/addons/config/Settings_Config.php:708
#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:18
#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:19
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:40
msgid "Info"
msgstr "Informazioni"

#: src/addons/config/Settings_Config.php:715
msgid "Available Add-ons"
msgstr "Componenti aggiuntivi disponibili"

#: src/addons/config/Settings_Config.php:721
msgid "Unavailable Add-ons"
msgstr "Componenti aggiuntivi non disponibili"

#: src/addons/config/Settings_Config.php:738
msgid "Jetpack"
msgstr "Jetpack"

#: src/addons/config/Settings_Config.php:749
msgid "WP Consent API"
msgstr "API di consenso WP"

#: src/addons/controller/addons/add_to_any/Add_To_Any.php:86
#: src/addons/controller/addons/embed_autocorrect/Embed_Autocorrect.php:443
msgid "Blocks embedded videos from Youtube, Twitter, Vimeo and Facebook."
msgstr "Blocca i video incorporati da Youtube, Twitter, Vimeo e Facebook."

#: src/addons/controller/addons/enfold/Enfold.php:29
msgid "Blocks cookies created by Enfold theme's Google Services."
msgstr "Blocca i cookie creati dai servizi Google del tema Enfold."

#: src/addons/controller/addons/enhanced_ecommerce_for_woocommerce_store/Enhanced_Ecommerce_For_WooCommerce_Store.php:43
msgid "Blocks enhanced e-commerce for WooCommerce store"
msgstr "Blocca l’e-commerce avanzato per il negozio online WooCommerce"

#: src/addons/controller/addons/google_analytics/Google_Analytics.php:36
msgid ""
"Google Analytics is used to track how visitor interact with website content."
msgstr ""
"Google Analytics viene utilizzato per monitorare le modalità di interazione "
"dei visitatori con i contenuti del sito web."

#: src/addons/controller/addons/google_analytics_plus/Google_Analytics_Plus.php:32
msgid ""
"Google Analytics is a simple, easy-to-use tool that helps website owners "
"measure how users interact with website content"
msgstr ""
"Google Analytics è uno strumento semplice e facile da utilizzare che aiuta i "
"proprietari di siti web ad analizzare come gli utenti interagiscono con i "
"contenuti del sito web"

#: src/addons/controller/addons/google_site_kit/Google_Site_Kit.php:33
msgid "Blocks Google Analytics scripts"
msgstr "Blocca gli script di Google Analytics"

#: src/addons/controller/addons/jetpack/widget/Facebook_Jetpack_Widget.php:71
msgid "Facebook widget."
msgstr "Facebook widget."

#: src/addons/controller/addons/litespeed_cache/Litespeed_Cache.php:51
msgid ""
"Excludes cookiebot javascript files when the Litespeed Cache deter option is "
"enabled."
msgstr ""
"Esclude i file javascript di cookiebot quando l’opzione Litespeed Cache "
"deter è attivata."

#: src/addons/controller/addons/official_facebook_pixel/Official_Facebook_Pixel.php:247
msgid "Blocks Official Meta Pixel scripts"
msgstr "Blocca gli script ufficiali Meta Pixel"

#: src/addons/controller/addons/optinmonster/Optinmonster.php:32
msgid ""
"OptinMonster API plugin to connect your WordPress site to your OptinMonster "
"account."
msgstr ""
"Plugin API di OptinMonster per collegare il tuo sito WordPress al tuo "
"account OptinMonster."

#: src/addons/controller/addons/simple_share_buttons_adder/Simple_Share_Buttons_Adder.php:32
msgid "Blocks Simple Share Buttons Adder."
msgstr "Blocca Simple Share Buttons Adder."

#: src/addons/controller/addons/wpforms/Wpforms.php:61
msgid ""
"If the user gives correct consent, IP and Unique User ID will be saved on "
"form submissions, otherwise not."
msgstr ""
"Se l’utente fornisce il consenso corretto, l’IP e l’ID utente unico verranno "
"salvati all’invio del modulo, altrimenti no."

#: src/addons/controller/addons/wpforms/Wpforms.php:62
msgid "Increases opt-in rate compared to WPForms \"GDPR mode\"."
msgstr "Aumenta il tasso di opt-in rispetto alla “modalità GDPR” di WPForms."

#: src/addons/controller/addons/wp_google_analytics_events/Wp_Google_Analytics_Events.php:46
msgid ""
"The plugin allows you to fire events whenever someone interacts or views "
"elements on your website."
msgstr ""
"Il plugin consente di attivare eventi ogni volta che qualcuno interagisce o "
"visualizza elementi sul tuo sito web."

#: src/addons/controller/addons/wp_rocket/Wp_Rocket.php:48
msgid ""
"Excludes cookiebot javascript files when the WP-Rocket deter option is "
"enabled."
msgstr ""
"Esclude i file javascript di Cookiebot quando l’opzione “WP-Rocket defer” è "
"abilitata."

#: src/addons/controller/addons/wp_seopress/Wp_Seopress.php:41
msgid "Blocks cookies from WP SEOPress' Google Analytics integration."
msgstr "Blocca i cookie dell’integrazione Google Analytics di WP SEOPress."

#: src/addons/controller/Plugin_Controller.php:50
msgid "You enabled Cookiebot™ auto blocking mode but still using addons"
msgstr ""
"Hai attivato la modalità di blocco automatico di Cookiebot™ ma continui a "
"utilizzare i componenti aggiuntivi"

#: src/addons/controller/Plugin_Controller.php:55
msgid ""
"In some occasions this may cause client side errors. If you notice any "
"errors please try to disable Cookiebot™ addons or contact Cookiebot™ support."
msgstr ""
"In alcune occasioni, questo potrebbe causare errori lato client. Se noti "
"degli errori, prova a disattivare i componenti aggiuntivi di Cookiebot™ o a "
"contattare il supporto di Cookiebot™."

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:84
msgid "Share your experience"
msgstr "Condividi la tua esperienza"

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:88
msgid ""
"Hi there! We are thrilled you love the Cookiebot CMP plugin. Could you do us "
"a huge favor and leave a 5-star rating on WordPress? Your support will help "
"us spread the word and empower more WordPress websites to meet GDPR and CCPA "
"compliance standards effortlessly. Thank you for your support!"
msgstr ""
"Ciao! Siamo contenti che ti piaccia il plugin di Cookiebot CMP. Potresti "
"farci un grande favore e lasciare una valutazione a 5 stelle su WordPress? "
"Il tuo supporto ci aiuterà a far conoscere il nostro lavoro e a rendere "
"possibile per più siti WordPress rispettare facilmente gli standard di "
"conformità GDPR e CCPA. Grazie per il tuo sostegno!"

#: src/admin_notices/Cookiebot_Temp_Notice.php:43
msgid ""
"Cookiebot CMP Plugin will soon no longer support PHP 5. If your website "
"still runs on this version we recommend upgrading so you can continue "
"enjoying the features Cookiebot CMP offers."
msgstr ""
"Il plugin Cookiebot CMP presto non supporterà più PHP 5. Se il tuo sito web "
"utilizza questa versione, ti consigliamo di effettuare l’aggiornamento in "
"modo da poter continuare a usufruire delle funzionalità di Cookiebot CMP."

#: src/lib/Cookiebot_Review.php:82
msgid "Sorry you are not allowed to do this."
msgstr "Azione non autorizzata."

#: src/lib/Cookiebot_Review.php:85
#: src/view/admin/common/templates/extra/review-form.php:94
msgid "Please select one option"
msgstr "Per favore, seleziona un’opzione"

#. translators: The placeholder is for the COOKIEBOT_MIN_PHP_VERSION constant
#: src/lib/Cookiebot_WP.php:66
msgid "The Cookiebot plugin requires PHP version %s or greater."
msgstr "Il plug-in Cookiebot richiede la versione PHP %s o superiore."

#: src/lib/Cookiebot_WP.php:242 src/settings/pages/Dashboard_Page.php:35
#: src/view/admin/common/templates/main-tabs.php:21
msgid "Dashboard"
msgstr "Pannello"

#: src/lib/helper.php:245 src/lib/helper.php:343
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:91
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:167
#: src/view/admin/cb_frame/settings/gcm-page.php:126
#: src/view/admin/cb_frame/settings/gtm-page.php:113
#: src/view/admin/common/prior-consent/available-addons/tab.php:55
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:88
msgid "marketing"
msgstr "marketing"

#: src/lib/helper.php:248 src/lib/helper.php:342
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:88
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:134
#: src/view/admin/cb_frame/settings/gcm-page.php:118
#: src/view/admin/cb_frame/settings/gtm-page.php:105
#: src/view/admin/common/prior-consent/available-addons/tab.php:46
msgid "statistics"
msgstr "statistiche"

#: src/lib/helper.php:251 src/lib/helper.php:341
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:85
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:118
#: src/view/admin/cb_frame/settings/gcm-page.php:110
#: src/view/admin/cb_frame/settings/gtm-page.php:97
#: src/view/admin/common/prior-consent/available-addons/tab.php:37
msgid "preferences"
msgstr "preferenze"

#: src/lib/helper.php:254
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:82
msgid "necessary"
msgstr "necessario"

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:367
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies."
msgstr "Accetta i cookie [renew_consent]%cookie_types[/renew_consent]."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:372
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"tracking."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per consentire "
"il tracciamento."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:377
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Social Share buttons."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per abilitare "
"i pulsanti di condivisione social."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:382
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to view "
"this element."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per "
"visualizzare questo elemento."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:387
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to watch "
"this video."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per "
"visualizzare questo video."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:392
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Services."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per abilitare "
"i servizi di Google."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:397
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"facebook shopping feature."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per abilitare "
"la funzione shopping di Facebook."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:402
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to track "
"for google analytics."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per abilitare "
"il tracciamento per Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:407
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Analytics."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per abilitare "
"Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:412
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"instagram feed."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per abilitare "
"il feed di Instagram."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:417
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Facebook Pixel."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per abilitare "
"Facebook Pixel."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:422
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to Social "
"Share buttons."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per i pulsanti "
"di condivisione social."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:427
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to allow "
"Matomo statistics."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per consentire "
"le statistiche di Matomo."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:432
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"saving user information."
msgstr ""
"Accetta i cookie [renew_consent]%cookie_types[/renew_consent] per consentire "
"il salvataggio delle informazioni dell'utente."

#: src/lib/Supported_Languages.php:8
msgid "Norwegian Bokmål"
msgstr "Norvegese Bokmål"

#: src/lib/Supported_Languages.php:9
msgid "Turkish"
msgstr "Turco"

#: src/lib/Supported_Languages.php:10
msgid "German"
msgstr "Tedesco"

#: src/lib/Supported_Languages.php:11
msgid "Czech"
msgstr "Ceco"

#: src/lib/Supported_Languages.php:12
msgid "Danish"
msgstr "Danese"

#: src/lib/Supported_Languages.php:13
msgid "Albanian"
msgstr "Albanese"

#: src/lib/Supported_Languages.php:14
msgid "Hebrew"
msgstr "Ebraico"

#: src/lib/Supported_Languages.php:15
msgid "Korean"
msgstr "Coreano"

#: src/lib/Supported_Languages.php:16
msgid "Italian"
msgstr "Italiano"

#: src/lib/Supported_Languages.php:17
msgid "Dutch"
msgstr "Olandese"

#: src/lib/Supported_Languages.php:18
msgid "Vietnamese"
msgstr "Vietnamita"

#: src/lib/Supported_Languages.php:19
msgid "Tamil"
msgstr "Tamil"

#: src/lib/Supported_Languages.php:20
msgid "Icelandic"
msgstr "Islandese"

#: src/lib/Supported_Languages.php:21
msgid "Romanian"
msgstr "Rumeno"

#: src/lib/Supported_Languages.php:22
msgid "Sinhala"
msgstr "Singalese"

#: src/lib/Supported_Languages.php:23
msgid "Catalan"
msgstr "Catalano"

#: src/lib/Supported_Languages.php:24
msgid "Bulgarian"
msgstr "Bulgaro"

#: src/lib/Supported_Languages.php:25
msgid "Ukrainian"
msgstr "Ucraino"

#: src/lib/Supported_Languages.php:26
msgid "Chinese"
msgstr "Cinese"

#: src/lib/Supported_Languages.php:27
msgid "English"
msgstr "Inglese"

#: src/lib/Supported_Languages.php:28
msgid "Arabic"
msgstr "Arabo"

#: src/lib/Supported_Languages.php:29
msgid "Croatian"
msgstr "Croato"

#: src/lib/Supported_Languages.php:30
msgid "Thai"
msgstr "Tailandese"

#: src/lib/Supported_Languages.php:31
msgid "Greek"
msgstr "Greco"

#: src/lib/Supported_Languages.php:32
msgid "Lithuanian"
msgstr "Lituano"

#: src/lib/Supported_Languages.php:33
msgid "Polish"
msgstr "Polacco"

#: src/lib/Supported_Languages.php:34
msgid "Latvian"
msgstr "Lettone"

#: src/lib/Supported_Languages.php:35
msgid "French"
msgstr "Francese"

#: src/lib/Supported_Languages.php:36
msgid "Indonesian"
msgstr "Indonesiano"

#: src/lib/Supported_Languages.php:37
msgid "Macedonian"
msgstr "Macedone"

#: src/lib/Supported_Languages.php:38
msgid "Estonian"
msgstr "Estone"

#: src/lib/Supported_Languages.php:39
msgid "Portuguese"
msgstr "Portoghese"

#: src/lib/Supported_Languages.php:40
msgid "Irish"
msgstr "Irlandese"

#: src/lib/Supported_Languages.php:41
msgid "Malay"
msgstr "Malese"

#: src/lib/Supported_Languages.php:42
msgid "Slovenian"
msgstr "Sloveno"

#: src/lib/Supported_Languages.php:43
msgid "Russian"
msgstr "Russo"

#: src/lib/Supported_Languages.php:44
msgid "Japanese"
msgstr "Giapponese"

#: src/lib/Supported_Languages.php:45
msgid "Hindi"
msgstr "Hindi"

#: src/lib/Supported_Languages.php:46
msgid "Slovak"
msgstr "Slovacco"

#: src/lib/Supported_Languages.php:47
msgid "Spanish"
msgstr "Spagnolo"

#: src/lib/Supported_Languages.php:48
msgid "Swedish"
msgstr "Svedese"

#: src/lib/Supported_Languages.php:49
msgid "Serbian"
msgstr "Serbo"

#: src/lib/Supported_Languages.php:50
msgid "Finnish"
msgstr "Finlandese"

#: src/lib/Supported_Languages.php:51
msgid "Basque"
msgstr "Basco"

#: src/lib/Supported_Languages.php:52
msgid "Hungarian"
msgstr "Ungherese"

#: src/lib/Supported_Regions.php:7
msgid "Afghanistan"
msgstr "Afghanistan"

#: src/lib/Supported_Regions.php:8
msgid "Albania"
msgstr "Albania"

#: src/lib/Supported_Regions.php:9
msgid "Algeria"
msgstr "Algeria"

#: src/lib/Supported_Regions.php:10
msgid "American Samoa"
msgstr "Samoa Americana"

#: src/lib/Supported_Regions.php:11
msgid "Andorra"
msgstr "Andorra"

#: src/lib/Supported_Regions.php:12
msgid "Angola"
msgstr "Angola"

#: src/lib/Supported_Regions.php:13
msgid "Anguilla"
msgstr "Anguilla"

#: src/lib/Supported_Regions.php:14
msgid "Antarctica"
msgstr "Antartide"

#: src/lib/Supported_Regions.php:15
msgid "Antigua and Barbuda"
msgstr "Antigua e Barbuda"

#: src/lib/Supported_Regions.php:16
msgid "Argentina"
msgstr "Argentina"

#: src/lib/Supported_Regions.php:17
msgid "Armenia"
msgstr "Armenia"

#: src/lib/Supported_Regions.php:18
msgid "Aruba"
msgstr "Aruba"

#: src/lib/Supported_Regions.php:19
msgid "Australia"
msgstr "Australia"

#: src/lib/Supported_Regions.php:20
msgid "Austria"
msgstr "Austria"

#: src/lib/Supported_Regions.php:21
msgid "Azerbaijan"
msgstr "Azerbaigian"

#: src/lib/Supported_Regions.php:22
msgid "Bahamas"
msgstr "Bahamas"

#: src/lib/Supported_Regions.php:23
msgid "Bahrain"
msgstr "Bahrein"

#: src/lib/Supported_Regions.php:24
msgid "Bangladesh"
msgstr "Bangladesh"

#: src/lib/Supported_Regions.php:25
msgid "Barbados"
msgstr "Barbados"

#: src/lib/Supported_Regions.php:26
msgid "Belarus"
msgstr "Bielorussia"

#: src/lib/Supported_Regions.php:27
msgid "Belgium"
msgstr "Belgio"

#: src/lib/Supported_Regions.php:28
msgid "Belize"
msgstr "Belize"

#: src/lib/Supported_Regions.php:29
msgid "Benin"
msgstr "Benin"

#: src/lib/Supported_Regions.php:30
msgid "Bermuda"
msgstr "Bermuda"

#: src/lib/Supported_Regions.php:31
msgid "Bhutan"
msgstr "Bhutan"

#: src/lib/Supported_Regions.php:32
msgid "Bolivia"
msgstr "Bolivia"

#: src/lib/Supported_Regions.php:33
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius e Saba"

#: src/lib/Supported_Regions.php:34
msgid "Bosnia and Herzegovina"
msgstr "Bosnia ed Erzegovina"

#: src/lib/Supported_Regions.php:35
msgid "Botswana"
msgstr "Botswana"

#: src/lib/Supported_Regions.php:36
msgid "Bouvet Island"
msgstr "Isola Bouvet"

#: src/lib/Supported_Regions.php:37
msgid "Brazil"
msgstr "Brasile"

#: src/lib/Supported_Regions.php:38
msgid "British Indian Ocean Territory"
msgstr "Territorio britannico dell'Oceano Indiano"

#: src/lib/Supported_Regions.php:39
msgid "Brunei "
msgstr "Brunei"

#: src/lib/Supported_Regions.php:40
msgid "Bulgaria"
msgstr "Bulgaria"

#: src/lib/Supported_Regions.php:41
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: src/lib/Supported_Regions.php:42
msgid "Burundi"
msgstr "Burundi"

#: src/lib/Supported_Regions.php:43
msgid "Cambodia"
msgstr "Cambogia"

#: src/lib/Supported_Regions.php:44
msgid "Cameroon"
msgstr "Camerun"

#: src/lib/Supported_Regions.php:45
msgid "Canada"
msgstr "Canada"

#: src/lib/Supported_Regions.php:46
msgid "Cape Verde"
msgstr "Capo Verde"

#: src/lib/Supported_Regions.php:47
msgid "Cayman Islands"
msgstr "Isole Cayman"

#: src/lib/Supported_Regions.php:48
msgid "Central African Republic"
msgstr "Repubblica Centrafricana"

#: src/lib/Supported_Regions.php:49
msgid "Chad"
msgstr "Ciad"

#: src/lib/Supported_Regions.php:50
msgid "Chile"
msgstr "Cile"

#: src/lib/Supported_Regions.php:51
msgid "China"
msgstr "Cina"

#: src/lib/Supported_Regions.php:52
msgid "Christmas Island"
msgstr "Isola di Natale"

#: src/lib/Supported_Regions.php:53
msgid "Cocos (Keeling) Islands"
msgstr "Isole Cocos (Keeling)"

#: src/lib/Supported_Regions.php:54
msgid "Colombia"
msgstr "Colombia"

#: src/lib/Supported_Regions.php:55
msgid "Comoros"
msgstr "Comore"

#: src/lib/Supported_Regions.php:56
msgid "Congo"
msgstr "Congo"

#: src/lib/Supported_Regions.php:57
msgid "Congo, the Democratic Republic of the"
msgstr "Repubblica Democratica del Congo"

#: src/lib/Supported_Regions.php:58
msgid "Cook Islands"
msgstr "Isole Cook"

#: src/lib/Supported_Regions.php:59
msgid "Costa Rica"
msgstr "Costa Rica"

#: src/lib/Supported_Regions.php:60
msgid "Croatia"
msgstr "Croazia"

#: src/lib/Supported_Regions.php:61
msgid "Cuba"
msgstr "Cuba"

#: src/lib/Supported_Regions.php:62
msgid "Curaçao"
msgstr "Curaçao"

#: src/lib/Supported_Regions.php:63
msgid "Cyprus"
msgstr "Cipro"

#: src/lib/Supported_Regions.php:64
msgid "Czech Republic"
msgstr "Repubblica Ceca"

#: src/lib/Supported_Regions.php:65
msgid "Côte d'Ivoire"
msgstr "Costa d'Avorio"

#: src/lib/Supported_Regions.php:66
msgid "Denmark"
msgstr "Danimarca"

#: src/lib/Supported_Regions.php:67
msgid "Djibouti"
msgstr "Gibuti"

#: src/lib/Supported_Regions.php:68
msgid "Dominica"
msgstr "Dominica"

#: src/lib/Supported_Regions.php:69
msgid "Dominican Republic"
msgstr "Repubblica Dominicana"

#: src/lib/Supported_Regions.php:70
msgid "Ecuador"
msgstr "Ecuador"

#: src/lib/Supported_Regions.php:71
msgid "Egypt"
msgstr "Egitto"

#: src/lib/Supported_Regions.php:72
msgid "El Salvador"
msgstr "El Salvador"

#: src/lib/Supported_Regions.php:73
msgid "Equatorial Guinea"
msgstr "Guinea Equatoriale"

#: src/lib/Supported_Regions.php:74
msgid "Eritrea"
msgstr "Eritrea"

#: src/lib/Supported_Regions.php:75
msgid "Estonia"
msgstr "Estonia"

#: src/lib/Supported_Regions.php:76
msgid "Ethiopia"
msgstr "Etiopia"

#: src/lib/Supported_Regions.php:77
msgid "Falkland Islands (Malvinas)"
msgstr "Isole Falkland (Malvine)"

#: src/lib/Supported_Regions.php:78
msgid "Faroe Islands"
msgstr "Isole Faroe"

#: src/lib/Supported_Regions.php:79
msgid "Fiji"
msgstr "Figi"

#: src/lib/Supported_Regions.php:80
msgid "Finland"
msgstr "Finlandia"

#: src/lib/Supported_Regions.php:81
msgid "France"
msgstr "Francia"

#: src/lib/Supported_Regions.php:82
msgid "French Guiana"
msgstr "Guyana francese"

#: src/lib/Supported_Regions.php:83
msgid "French Polynesia"
msgstr "Polinesia Francese"

#: src/lib/Supported_Regions.php:84
msgid "French Southern and Antarctic Lands"
msgstr "Terre australi e antartiche francesi"

#: src/lib/Supported_Regions.php:85
msgid "Gabon"
msgstr "Gabon"

#: src/lib/Supported_Regions.php:86
msgid "Gambia"
msgstr "Gambia"

#: src/lib/Supported_Regions.php:87
msgid "Georgia"
msgstr "Georgia"

#: src/lib/Supported_Regions.php:88
msgid "Germany"
msgstr "Germania"

#: src/lib/Supported_Regions.php:89
msgid "Ghana"
msgstr "Ghana"

#: src/lib/Supported_Regions.php:90
msgid "Gibraltar"
msgstr "Gibilterra"

#: src/lib/Supported_Regions.php:91
msgid "Greece"
msgstr "Grecia"

#: src/lib/Supported_Regions.php:92
msgid "Greenland"
msgstr "Groenlandia"

#: src/lib/Supported_Regions.php:93
msgid "Grenada"
msgstr "Grenada"

#: src/lib/Supported_Regions.php:94
msgid "Guadeloupe"
msgstr "Guadalupa"

#: src/lib/Supported_Regions.php:95
msgid "Guam"
msgstr "Guam"

#: src/lib/Supported_Regions.php:96
msgid "Guatemala"
msgstr "Guatemala"

#: src/lib/Supported_Regions.php:97
msgid "Guernsey"
msgstr "Guernsey"

#: src/lib/Supported_Regions.php:98
msgid "Guinea"
msgstr "Guinea"

#: src/lib/Supported_Regions.php:99
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: src/lib/Supported_Regions.php:100
msgid "Guyana"
msgstr "Guyana"

#: src/lib/Supported_Regions.php:101
msgid "Haiti"
msgstr "Haiti"

#: src/lib/Supported_Regions.php:102
msgid "Heard Island and McDonald Islands"
msgstr "Isole Heard e McDonald"

#: src/lib/Supported_Regions.php:103
msgid "Holy See (Vatican City State)"
msgstr "Città del Vaticano"

#: src/lib/Supported_Regions.php:104
msgid "Honduras"
msgstr "Honduras"

#: src/lib/Supported_Regions.php:105
msgid "Hong Kong"
msgstr "Hong Kong"

#: src/lib/Supported_Regions.php:106
msgid "Hungary"
msgstr "Ungheria"

#: src/lib/Supported_Regions.php:107
msgid "Iceland"
msgstr "Islanda"

#: src/lib/Supported_Regions.php:108
msgid "India"
msgstr "India"

#: src/lib/Supported_Regions.php:109
msgid "Indonesia"
msgstr "Indonesia"

#: src/lib/Supported_Regions.php:110
msgid "Iran"
msgstr "Iran"

#: src/lib/Supported_Regions.php:111
msgid "Iraq"
msgstr "Iraq"

#: src/lib/Supported_Regions.php:112
msgid "Ireland"
msgstr "Irlanda"

#: src/lib/Supported_Regions.php:113
msgid "Isle of Man"
msgstr "Isola di Man"

#: src/lib/Supported_Regions.php:114
msgid "Israel"
msgstr "Israele"

#: src/lib/Supported_Regions.php:115
msgid "Italy"
msgstr "Italia"

#: src/lib/Supported_Regions.php:116
msgid "Jamaica"
msgstr "Giamaica"

#: src/lib/Supported_Regions.php:117
msgid "Japan"
msgstr "Giappone"

#: src/lib/Supported_Regions.php:118
msgid "Jersey"
msgstr "Jersey"

#: src/lib/Supported_Regions.php:119
msgid "Jordan"
msgstr "Giordania"

#: src/lib/Supported_Regions.php:120
msgid "Kazakhstan"
msgstr "Kazakistan"

#: src/lib/Supported_Regions.php:121
msgid "Kenya"
msgstr "Kenya"

#: src/lib/Supported_Regions.php:122
msgid "Kiribati"
msgstr "Kiribati"

#: src/lib/Supported_Regions.php:123
msgid "Kuwait"
msgstr "Kuwait"

#: src/lib/Supported_Regions.php:124
msgid "Kyrgyzstan"
msgstr "Kirghizistan"

#: src/lib/Supported_Regions.php:125
msgid "Laos"
msgstr "Laos"

#: src/lib/Supported_Regions.php:126
msgid "Latvia"
msgstr "Lettonia"

#: src/lib/Supported_Regions.php:127
msgid "Lebanon"
msgstr "Libano"

#: src/lib/Supported_Regions.php:128
msgid "Lesotho"
msgstr "Lesotho"

#: src/lib/Supported_Regions.php:129
msgid "Liberia"
msgstr "Liberia"

#: src/lib/Supported_Regions.php:130
msgid "Libya"
msgstr "Libia"

#: src/lib/Supported_Regions.php:131
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: src/lib/Supported_Regions.php:132
msgid "Lithuania"
msgstr "Lituania"

#: src/lib/Supported_Regions.php:133
msgid "Luxembourg"
msgstr "Lussemburgo"

#: src/lib/Supported_Regions.php:134
msgid "Macao"
msgstr "Macao"

#: src/lib/Supported_Regions.php:135
msgid "North Macedonia"
msgstr "Macedonia del Nord"

#: src/lib/Supported_Regions.php:136
msgid "Madagascar"
msgstr "Madagascar"

#: src/lib/Supported_Regions.php:137
msgid "Malawi"
msgstr "Malawi"

#: src/lib/Supported_Regions.php:138
msgid "Malaysia"
msgstr "Malaysia"

#: src/lib/Supported_Regions.php:139
msgid "Maldives"
msgstr "Maldive"

#: src/lib/Supported_Regions.php:140
msgid "Mali"
msgstr "Mali"

#: src/lib/Supported_Regions.php:141
msgid "Malta"
msgstr "Malta"

#: src/lib/Supported_Regions.php:142
msgid "Marshall Islands"
msgstr "Isole Marshall"

#: src/lib/Supported_Regions.php:143
msgid "Martinique"
msgstr "Martinica"

#: src/lib/Supported_Regions.php:144
msgid "Mauritania"
msgstr "Mauritania"

#: src/lib/Supported_Regions.php:145
msgid "Mauritius"
msgstr "Mauritius"

#: src/lib/Supported_Regions.php:146
msgid "Mayotte"
msgstr "Mayotte"

#: src/lib/Supported_Regions.php:147
msgid "Mexico"
msgstr "Messico"

#: src/lib/Supported_Regions.php:148
msgid "Micronesia, Federated States of"
msgstr "Micronesia, Stati Federati di"

#: src/lib/Supported_Regions.php:149
msgid "Moldova"
msgstr "Moldavia"

#: src/lib/Supported_Regions.php:150
msgid "Monaco"
msgstr "Principato di Monaco"

#: src/lib/Supported_Regions.php:151
msgid "Mongolia"
msgstr "Mongolia"

#: src/lib/Supported_Regions.php:152
msgid "Montenegro"
msgstr "Montenegro"

#: src/lib/Supported_Regions.php:153
msgid "Montserrat"
msgstr "Montserrat"

#: src/lib/Supported_Regions.php:154
msgid "Morocco"
msgstr "Marocco"

#: src/lib/Supported_Regions.php:155
msgid "Mozambique"
msgstr "Mozambico"

#: src/lib/Supported_Regions.php:156
msgid "Myanmar"
msgstr "Myanmar"

#: src/lib/Supported_Regions.php:157
msgid "Namibia"
msgstr "Namibia"

#: src/lib/Supported_Regions.php:158
msgid "Nauru"
msgstr "Nauru"

#: src/lib/Supported_Regions.php:159
msgid "Nepal"
msgstr "Nepal"

#: src/lib/Supported_Regions.php:160
msgid "Netherlands"
msgstr "Paesi Bassi"

#: src/lib/Supported_Regions.php:161
msgid "New Caledonia"
msgstr "Nuova Caledonia"

#: src/lib/Supported_Regions.php:162
msgid "New Zealand"
msgstr "Nuova Zelanda"

#: src/lib/Supported_Regions.php:163
msgid "Nicaragua"
msgstr "Nicaragua"

#: src/lib/Supported_Regions.php:164
msgid "Niger"
msgstr "Niger"

#: src/lib/Supported_Regions.php:165
msgid "Nigeria"
msgstr "Nigeria"

#: src/lib/Supported_Regions.php:166
msgid "Niue"
msgstr "Niue"

#: src/lib/Supported_Regions.php:167
msgid "Norfolk Island"
msgstr "Isola Norfolk"

#: src/lib/Supported_Regions.php:168
msgid "North Korea"
msgstr "Corea del Nord"

#: src/lib/Supported_Regions.php:169
msgid "Northern Mariana Islands"
msgstr "Isole Marianne settentrionali"

#: src/lib/Supported_Regions.php:170
msgid "Norway"
msgstr "Norvegia"

#: src/lib/Supported_Regions.php:171
msgid "Oman"
msgstr "Oman"

#: src/lib/Supported_Regions.php:172
msgid "Pakistan"
msgstr "Pakistan"

#: src/lib/Supported_Regions.php:173
msgid "Palau"
msgstr "Palau"

#: src/lib/Supported_Regions.php:174
msgid "Palestinian Territory"
msgstr "Territori palestinesi"

#: src/lib/Supported_Regions.php:175
msgid "Panama"
msgstr "Panama"

#: src/lib/Supported_Regions.php:176
msgid "Papua New Guinea"
msgstr "Papua Nuova Guinea"

#: src/lib/Supported_Regions.php:177
msgid "Paraguay"
msgstr "Paraguay"

#: src/lib/Supported_Regions.php:178
msgid "Peru"
msgstr "Perù"

#: src/lib/Supported_Regions.php:179
msgid "Philippines"
msgstr "Filippine"

#: src/lib/Supported_Regions.php:180
msgid "Pitcairn"
msgstr "Isole Pitcairn"

#: src/lib/Supported_Regions.php:181
msgid "Poland"
msgstr "Polonia"

#: src/lib/Supported_Regions.php:182
msgid "Portugal"
msgstr "Portogallo"

#: src/lib/Supported_Regions.php:183
msgid "Puerto Rico"
msgstr "Porto Rico"

#: src/lib/Supported_Regions.php:184
msgid "Qatar"
msgstr "Qatar"

#: src/lib/Supported_Regions.php:185
msgid "Romania"
msgstr "Romania"

#: src/lib/Supported_Regions.php:186
msgid "Russia"
msgstr "Russia"

#: src/lib/Supported_Regions.php:187
msgid "Rwanda"
msgstr "Ruanda"

#: src/lib/Supported_Regions.php:188
msgid "Réunion"
msgstr "La Riunione"

#: src/lib/Supported_Regions.php:189
msgid "Saint Barthélemy"
msgstr "Saint-Barthélemy"

#: src/lib/Supported_Regions.php:190
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr "Sant'Elena"

#: src/lib/Supported_Regions.php:191
msgid "Saint Kitts and Nevis"
msgstr "Saint Kitts e Nevis"

#: src/lib/Supported_Regions.php:192
msgid "Saint Lucia"
msgstr "Santa Lucia"

#: src/lib/Supported_Regions.php:193
msgid "Saint Martin (French part)"
msgstr "Saint Martin (parte francese)"

#: src/lib/Supported_Regions.php:194
msgid "Saint Pierre and Miquelon"
msgstr "Saint-Pierre e Miquelon"

#: src/lib/Supported_Regions.php:195
msgid "Saint Vincent and the Grenadines"
msgstr "Saint Vincent e Grenadine"

#: src/lib/Supported_Regions.php:196
msgid "Samoa"
msgstr "Samoa"

#: src/lib/Supported_Regions.php:197
msgid "San Marino"
msgstr "San Marino"

#: src/lib/Supported_Regions.php:198
msgid "Sao Tome and Principe"
msgstr "São Tomé e Príncipe"

#: src/lib/Supported_Regions.php:199
msgid "Saudi Arabia"
msgstr "Arabia Saudita"

#: src/lib/Supported_Regions.php:200
msgid "Senegal"
msgstr "Senegal"

#: src/lib/Supported_Regions.php:201
msgid "Serbia"
msgstr "Serbia"

#: src/lib/Supported_Regions.php:202
msgid "Seychelles"
msgstr "Seychelles"

#: src/lib/Supported_Regions.php:203
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: src/lib/Supported_Regions.php:204
msgid "Singapore"
msgstr "Singapore"

#: src/lib/Supported_Regions.php:205
msgid "Sint Maarten (Dutch part)"
msgstr "Sint Maarten (parte olandese)"

#: src/lib/Supported_Regions.php:206
msgid "Slovakia"
msgstr "Slovacchia"

#: src/lib/Supported_Regions.php:207
msgid "Slovenia"
msgstr "Slovenia"

#: src/lib/Supported_Regions.php:208
msgid "Solomon Islands"
msgstr "Isole Salomone"

#: src/lib/Supported_Regions.php:209
msgid "Somalia"
msgstr "Somalia"

#: src/lib/Supported_Regions.php:210
msgid "South Africa"
msgstr "Sudafrica"

#: src/lib/Supported_Regions.php:211
msgid "South Georgia and the South Sandwich Islands"
msgstr "Georgia del Sud e Isole Sandwich Australi"

#: src/lib/Supported_Regions.php:212
msgid "South Korea"
msgstr "Corea del Sud"

#: src/lib/Supported_Regions.php:213
msgid "South Sudan"
msgstr "Sudan del Sud"

#: src/lib/Supported_Regions.php:214
msgid "Spain"
msgstr "Spagna"

#: src/lib/Supported_Regions.php:215
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: src/lib/Supported_Regions.php:216
msgid "Sudan"
msgstr "Sudan"

#: src/lib/Supported_Regions.php:217
msgid "Suriname"
msgstr "Suriname"

#: src/lib/Supported_Regions.php:218
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard e Jan Mayen"

#: src/lib/Supported_Regions.php:219
msgid "Eswatini"
msgstr "eSwatini"

#: src/lib/Supported_Regions.php:220
msgid "Sweden"
msgstr "Svezia"

#: src/lib/Supported_Regions.php:221
msgid "Switzerland"
msgstr "Svizzera"

#: src/lib/Supported_Regions.php:222
msgid "Syria"
msgstr "Siria"

#: src/lib/Supported_Regions.php:223
msgid "Taiwan"
msgstr "Taiwan"

#: src/lib/Supported_Regions.php:224
msgid "Tajikistan"
msgstr "Tagikistan"

#: src/lib/Supported_Regions.php:225
msgid "Tanzania"
msgstr "Tanzania"

#: src/lib/Supported_Regions.php:226
msgid "Thailand"
msgstr "Tailandia"

#: src/lib/Supported_Regions.php:227
msgid "Timor-Leste"
msgstr "Timor Est"

#: src/lib/Supported_Regions.php:228
msgid "Togo"
msgstr "Togo"

#: src/lib/Supported_Regions.php:229
msgid "Tokelau"
msgstr "Tokelau"

#: src/lib/Supported_Regions.php:230
msgid "Tonga"
msgstr "Tonga"

#: src/lib/Supported_Regions.php:231
msgid "Trinidad and Tobago"
msgstr "Trinidad e Tobago"

#: src/lib/Supported_Regions.php:232
msgid "Tunisia"
msgstr "Tunisia"

#: src/lib/Supported_Regions.php:233
msgid "Türkiye"
msgstr "Turchia"

#: src/lib/Supported_Regions.php:234
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: src/lib/Supported_Regions.php:235
msgid "Turks and Caicos Islands"
msgstr "Turks e Caicos"

#: src/lib/Supported_Regions.php:236
msgid "Tuvalu"
msgstr "Tuvalu"

#: src/lib/Supported_Regions.php:237
msgid "Uganda"
msgstr "Uganda"

#: src/lib/Supported_Regions.php:238
msgid "Ukraine"
msgstr "Ucraina"

#: src/lib/Supported_Regions.php:239
msgid "United Arab Emirates"
msgstr "Emirati Arabi Uniti"

#: src/lib/Supported_Regions.php:240
msgid "United Kingdom"
msgstr "Regno Unito"

#: src/lib/Supported_Regions.php:241
msgid "United States"
msgstr "Stati Uniti"

#: src/lib/Supported_Regions.php:242
msgid "United States - State of California"
msgstr "Stati Uniti - Stato della California"

#: src/lib/Supported_Regions.php:243
msgid "United States - State of Colorado"
msgstr "Stati Uniti - Colorado"

#: src/lib/Supported_Regions.php:244
msgid "United States - State of Connecticut"
msgstr "Stati Uniti - Connecticut"

#: src/lib/Supported_Regions.php:245
msgid "United States - State of Utah"
msgstr "Stati Uniti - Utah"

#: src/lib/Supported_Regions.php:246
msgid "United States - State of Virginia"
msgstr "Stati Uniti - Stato della Virginia"

#: src/lib/Supported_Regions.php:247
msgid "United States Minor Outlying Islands"
msgstr "Isole Minori Esterne degli Stati Uniti d'America"

#: src/lib/Supported_Regions.php:248
msgid "Uruguay"
msgstr "Uruguay"

#: src/lib/Supported_Regions.php:249
msgid "Uzbekistan"
msgstr "Uzbekistan"

#: src/lib/Supported_Regions.php:250
msgid "Vanuatu"
msgstr "Vanuatu"

#: src/lib/Supported_Regions.php:251
msgid "Venezuela"
msgstr "Venezuela"

#: src/lib/Supported_Regions.php:252
msgid "Viet Nam"
msgstr "Vietnam"

#: src/lib/Supported_Regions.php:253
msgid "Virgin Islands, British"
msgstr "Isole Vergini Britanniche"

#: src/lib/Supported_Regions.php:254
msgid "Virgin Islands, U.S."
msgstr "Isole Vergini Americane"

#: src/lib/Supported_Regions.php:255
msgid "Wallis and Futuna"
msgstr "Wallis and Futuna"

#: src/lib/Supported_Regions.php:256
msgid "Western Sahara"
msgstr "Sahara Occidentale"

#: src/lib/Supported_Regions.php:257
msgid "Yemen"
msgstr "Yemen"

#: src/lib/Supported_Regions.php:258
msgid "Zambia"
msgstr "Zambia"

#: src/lib/Supported_Regions.php:259
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: src/lib/Supported_Regions.php:260
msgid "Åland Islands"
msgstr "Isole Åland"

#: src/settings/Network_Menu_Settings.php:36
#: src/settings/pages/Dashboard_Page.php:21
msgid "Cookiebot"
msgstr "Cookiebot"

#: src/settings/Network_Menu_Settings.php:44
#: src/settings/pages/Settings_Page.php:23
msgid "Cookiebot Settings"
msgstr "Impostazioni dei Cookiebot"

#: src/settings/Network_Menu_Settings.php:45
#: src/settings/pages/Settings_Page.php:24
#: src/view/admin/common/settings-page.php:30
#: src/view/admin/common/templates/main-tabs.php:32
#: src/view/admin/uc_frame/settings-page.php:47
#: src/view/admin/cb_frame/settings-page.php:50
msgid "Settings"
msgstr "Impostazioni"

#: src/settings/Network_Menu_Settings.php:52
#: src/settings/pages/Support_Page.php:18
msgid "Cookiebot Support"
msgstr "Assistenza Cookiebot"

#: src/settings/Network_Menu_Settings.php:53
#: src/settings/pages/Support_Page.php:19
#: src/view/admin/cb_frame/support-page.php:19
#: src/view/admin/common/support-page.php:19
#: src/view/admin/common/templates/main-tabs.php:50
#: src/view/admin/uc_frame/support-page.php:19
msgid "Support"
msgstr "Assistenza"

#: src/settings/pages/Dashboard_Page.php:34
msgid "Cookiebot Dashboard"
msgstr "Pannello Cookiebot"

#: src/settings/pages/Debug_Page.php:27 src/settings/pages/Debug_Page.php:28
#: src/view/admin/cb_frame/debug-page.php:18
#: src/view/admin/common/templates/main-tabs.php:57
#: src/view/admin/uc_frame/debug-page.php:18
msgid "Debug info"
msgstr "Informazioni di debug"

#: src/settings/pages/Iab_Page.php:20 src/settings/pages/Iab_Page.php:21
msgid "IAB"
msgstr "IAB"

#: src/settings/pages/Iab_Page.php:130
msgid "Purposes of data use"
msgstr "Finalità dell’utilizzo dei dati"

#: src/settings/pages/Iab_Page.php:131
msgid ""
"Inform your users how you’ll use their data. We’ll show this on the second "
"layer of your consent banner, where users interested in more granular detail "
"about data processing can view it."
msgstr ""
"Informa i tuoi utenti su come utilizzerai i loro dati. Lo mostreremo nel "
"secondo livello del banner di consenso, dove gli utenti interessati a "
"dettagli più granulari sul trattamento dei dati potranno visualizzarlo."

#: src/settings/pages/Iab_Page.php:138
msgid "Special purposes of data use"
msgstr "Finalità speciali di utilizzo dei dati"

#: src/settings/pages/Iab_Page.php:139
msgid ""
"Inform your users about special purposes of using their data. We’ll show "
"this on the second layer of your consent banner."
msgstr ""
"Informa i tuoi utenti sulle finalità speciali dell’utilizzo dei loro dati. "
"Lo mostreremo nel secondo livello del banner di consenso."

#: src/settings/pages/Iab_Page.php:146
msgid "Features required for data processing"
msgstr "Funzionalità necessarie per il trattamento dei dati"

#: src/settings/pages/Iab_Page.php:147
msgid ""
"Inform users about the features necessary for processing their personal "
"data. We’ll list the selected features on the second layer of your consent "
"banner."
msgstr ""
"Informa gli utenti sulle funzionalità necessarie per il trattamento dei loro "
"dati personali. Le funzionalità selezionate saranno elencate nel secondo "
"livello del banner di consenso."

#: src/settings/pages/Iab_Page.php:154
msgid "Special features required for data processing"
msgstr "Caratteristiche speciali richieste per il trattamento dei dati"

#: src/settings/pages/Iab_Page.php:155
msgid ""
"Inform users about any specially categorized features required for "
"processing their personal data. We’ll list the selected features on the "
"second layer of your consent banner, offering options for users to enable or "
"disable them."
msgstr ""
"Informa gli utenti sulle caratteristiche speciali necessarie per il "
"trattamento dei loro dati personali. Elencheremo le funzionalità selezionate "
"nel secondo livello del banner di consenso, offrendo agli utenti la "
"possibilità di attivarle o disattivarle."

#: src/settings/pages/Iab_Page.php:162
msgid "TCF listed vendors"
msgstr "Fornitori elencati da TCF"

#: src/settings/pages/Iab_Page.php:183
msgid "Store and/or access information on a device"
msgstr "Memorizza e/o accedi a informazioni su un dispositivo"

#: src/settings/pages/Iab_Page.php:187
msgid "Use limited data to select advertising"
msgstr "Utilizza dati limitati per selezionare la pubblicità"

#: src/settings/pages/Iab_Page.php:191
msgid "Create profiles for personalised advertising"
msgstr "Crea profili per la pubblicità personalizzata"

#: src/settings/pages/Iab_Page.php:195
msgid "Use profiles to select personalised advertising"
msgstr "Utilizza profili per selezionare la pubblicità personalizzata"

#: src/settings/pages/Iab_Page.php:199
msgid "Create profiles to personalise content"
msgstr "Crea profili per personalizzare i contenuti"

#: src/settings/pages/Iab_Page.php:203
msgid "Use profiles to select personalised content"
msgstr "Utilizza i profili per selezionare i contenuti personalizzati"

#: src/settings/pages/Iab_Page.php:207
msgid "Measure advertising performance"
msgstr "Misura le prestazioni della pubblicità"

#: src/settings/pages/Iab_Page.php:211
msgid "Measure content performance"
msgstr "Misura le prestazioni dei contenuti"

#: src/settings/pages/Iab_Page.php:215
msgid ""
"Understand audiences through statistics or combinations of data from "
"different sources"
msgstr ""
"Comprendi il pubblico attraverso statistiche o combinazioni di dati "
"provenienti da diverse fonti"

#: src/settings/pages/Iab_Page.php:219
msgid "Develop and improve services"
msgstr "Sviluppa e migliora i servizi"

#: src/settings/pages/Iab_Page.php:223
msgid "Use limited data to select content"
msgstr "Utilizza dati limitati per selezionare i contenuti"

#: src/settings/pages/Iab_Page.php:229
msgid "Ensure security, prevent and detect fraud, and fix errors"
msgstr ""
"Garantisci la sicurezza, previeni e rileva le frodi e correggi gli errori"

#: src/settings/pages/Iab_Page.php:233
msgid "Deliver and present advertising and content"
msgstr "Fornisci e presenta pubblicità e contenuti"

#: src/settings/pages/Iab_Page.php:239
msgid "Match and combine data from other data sources"
msgstr "Abbina e combina dati provenienti da altre fonti"

#: src/settings/pages/Iab_Page.php:243
msgid "Link different devices"
msgstr "Collega dispositivi diversi"

#: src/settings/pages/Iab_Page.php:247
msgid "Identify devices based on information transmitted automatically"
msgstr ""
"Identifica i dispositivi in base alle informazioni trasmesse automaticamente"

#: src/settings/pages/Iab_Page.php:253
msgid "Use precise geolocation data"
msgstr "Utilizza dati di geolocalizzazione precisi"

#: src/settings/pages/Iab_Page.php:257
msgid "Actively scan device characteristics for identification"
msgstr ""
"Scansione attiva delle caratteristiche del dispositivo per l’identificazione"

#: src/shortcode/Cookiebot_Declaration_Shortcode.php:53
msgid "Please add your Cookiebot ID to show Cookie Declarations"
msgstr "Aggiungi il tuo ID Cookiebot per mostrare le dichiarazioni sui cookie"

#: src/shortcode/Cookiebot_Embedding_Shortcode.php:28
msgid "Please add a service ID into the shortcode \"service\" parameter."
msgstr "Aggiungi l’ID del servizio nel parametro “service” dello shortcode."

#: src/view/admin/cb_frame/dashboard-page.php:33
msgid "I already have a Cookiebot CMP account"
msgstr "Possiedo già un account Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:37
msgid "Connect my existing account"
msgstr "Collega il mio account esistente"

#: src/view/admin/cb_frame/dashboard-page.php:42
msgid "Your Cookiebot CMP for WordPress solution"
msgstr "La tua soluzione Cookiebot CMP per Wordpress"

#: src/view/admin/cb_frame/dashboard-page.php:47
#: src/view/admin/uc_frame/dashboard-page.php:35
msgid "Account added"
msgstr "Account aggiunto"

#: src/view/admin/cb_frame/dashboard-page.php:50
msgid "Congratulations!"
msgstr "Congratulazioni!"

#: src/view/admin/cb_frame/dashboard-page.php:51
msgid "You have added your Domain Group ID to WordPress. You are all set!"
msgstr ""
"Hai aggiunto il tuo ID del gruppo di dominio a Wordpress. Sei pronto ad "
"iniziare!"

#: src/view/admin/cb_frame/dashboard-page.php:62
#: src/view/admin/uc_frame/dashboard-page.php:48
msgid "Your opinion matters"
msgstr "La tua opinione conta!"

#: src/view/admin/cb_frame/dashboard-page.php:65
msgid ""
"Are you happy with our WordPress plugin? Your feedback will help us make our "
"product better for you."
msgstr ""
"Sei soddisfatto del nostro plugin Wordpress? Il tuo feedback ci aiuterà a "
"rendere il nostro prodotto più adatto alle tue esigenze."

#: src/view/admin/cb_frame/dashboard-page.php:69
msgid "Write a review"
msgstr "Scrivi una recensione"

#: src/view/admin/cb_frame/dashboard-page.php:80
#: src/view/admin/common/dashboard-page.php:45
msgid "Get started"
msgstr "Inizia ora"

#: src/view/admin/cb_frame/dashboard-page.php:83
msgid "Create a new Cookiebot CMP account"
msgstr "Crea un nuovo account Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:87
#: src/view/admin/common/dashboard-page.php:52
msgid "Create a new account"
msgstr "Crea un nuovo account"

#: src/view/admin/cb_frame/dashboard-page.php:92
msgid "Learn more about how to optimize your Cookiebot CMP setup?"
msgstr ""
"Vuoi saperne di più su come ottimizzare la tua configurazione di Cookiebot "
"CMP?"

#: src/view/admin/cb_frame/dashboard-page.php:96
msgid "Visit Help Center"
msgstr "Visita il nostro Centro Assistenza"

#: src/view/admin/cb_frame/dashboard-page.php:113
#: src/view/admin/cb_frame/support-page.php:43
#: src/view/admin/common/dashboard-page.php:63
#: src/view/admin/common/support-page.php:43
#: src/view/admin/uc_frame/dashboard-page.php:66
#: src/view/admin/uc_frame/support-page.php:43
msgid "Video guide"
msgstr "Videoguida"

#: src/view/admin/cb_frame/dashboard-page.php:115
msgid "How to get started with Cookiebot CMP"
msgstr "Iniziare ad usare Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:119
msgid "Learn more about Cookiebot CMP"
msgstr "Per saperne di più su Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:131
#: src/view/admin/common/dashboard-page.php:81
#: src/view/admin/uc_frame/dashboard-page.php:84
#: src/view/admin/uc_frame/settings/embeddings-page.php:32
msgid "GDPR"
msgstr "GDPR"

#: src/view/admin/cb_frame/dashboard-page.php:134
#: src/view/admin/common/dashboard-page.php:84
#: src/view/admin/uc_frame/dashboard-page.php:87
msgid "Europe"
msgstr "Europa"

#: src/view/admin/cb_frame/dashboard-page.php:138
#: src/view/admin/cb_frame/dashboard-page.php:140
#: src/view/admin/cb_frame/dashboard-page.php:155
#: src/view/admin/cb_frame/dashboard-page.php:157
#: src/view/admin/common/dashboard-page.php:88
#: src/view/admin/common/dashboard-page.php:90
#: src/view/admin/common/dashboard-page.php:105
#: src/view/admin/common/dashboard-page.php:107
#: src/view/admin/uc_frame/dashboard-page.php:91
#: src/view/admin/uc_frame/dashboard-page.php:93
#: src/view/admin/uc_frame/dashboard-page.php:108
#: src/view/admin/uc_frame/dashboard-page.php:110
msgid "Learn More"
msgstr "Per saperne di più"

#: src/view/admin/cb_frame/dashboard-page.php:148
#: src/view/admin/common/dashboard-page.php:98
#: src/view/admin/uc_frame/dashboard-page.php:101
msgid "CCPA"
msgstr "CCPA"

#: src/view/admin/cb_frame/dashboard-page.php:151
#: src/view/admin/common/dashboard-page.php:101
#: src/view/admin/uc_frame/dashboard-page.php:104
msgid "North America"
msgstr "Nord America"

#: src/view/admin/cb_frame/dashboard-page.php:162
#: src/view/admin/common/dashboard-page.php:112
msgid "See other legislations"
msgstr "Consulta altre normative"

#: src/view/admin/cb_frame/debug-page.php:22
msgid "Debug information"
msgstr "Informazioni di debug"

#: src/view/admin/cb_frame/debug-page.php:25
msgid ""
"The information below is for debugging purposes. If you have any issues with "
"your Cookiebot CMP integration, this information is the best place to start."
msgstr ""
"Le informazioni riportate di seguito servono per il debug. Se si riscontrano "
"problemi con l’integrazione di Cookiebot CMP, queste informazioni sono il "
"punto di partenza migliore."

#: src/view/admin/cb_frame/debug-page.php:33
#: src/view/admin/uc_frame/debug-page.php:33
msgid "Copy debug information to clipboard"
msgstr "Copia le informazioni di debug negli appunti"

#: src/view/admin/cb_frame/debug-page.php:42
msgid ""
"If you have any issues with the implemenation of Cookiebot CMP, please visit "
"our Support Center."
msgstr ""
"Se riscontri altri problemi con l’implementazione di Cookiebot CMP, ti "
"preghiamo di visitare il nostro Centro Assistenza."

#: src/view/admin/cb_frame/debug-page.php:45
#: src/view/admin/uc_frame/debug-page.php:44
msgid "Visit Support Center"
msgstr "Visita il Centro Assistenza"

#: src/view/admin/cb_frame/network-settings-page.php:22
#: src/view/admin/common/network-settings-page.php:23
#: src/view/admin/uc_frame/network-settings-page.php:23
msgid "Network Settings"
msgstr "Configurazione di rete"

#: src/view/admin/cb_frame/network-settings-page.php:33
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:3
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:3
#: src/view/admin/uc_frame/network-settings-page.php:34
msgid "Are you sure?"
msgstr "Sei sicuro?"

#: src/view/admin/cb_frame/network-settings-page.php:36
#: src/view/admin/uc_frame/network-settings-page.php:37
msgid ""
"You will need to add a new ID before updating other network settings. If any "
"subsite is using its own account disconnecting this account won’t affect it."
msgstr ""
"È necessario aggiungere un nuovo ID prima di aggiornare le altre "
"impostazioni di rete. Se un sottosito utilizza un proprio account, "
"disconnettere questo account non avrà alcun effetto."

#: src/view/admin/cb_frame/network-settings-page.php:40
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:10
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:10
#: src/view/admin/uc_frame/network-settings-page.php:41
msgid "Cancel"
msgstr "Annullare"

#: src/view/admin/cb_frame/network-settings-page.php:43
#: src/view/admin/cb_frame/network-settings-page.php:65
#: src/view/admin/cb_frame/settings/general-page.php:52
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:13
#: src/view/admin/uc_frame/network-settings-page.php:44
#: src/view/admin/uc_frame/network-settings-page.php:75
#: src/view/admin/uc_frame/settings/general-page.php:45
msgid "Disconnect account"
msgstr "Disconnetere l’account "

#: src/view/admin/cb_frame/network-settings-page.php:50
msgid "Network Domain Group ID"
msgstr "ID del gruppo di domini di rete "

#: src/view/admin/cb_frame/network-settings-page.php:52
msgid ""
"If added this will be the default Cookiebot ID for all subsites. Subsites "
"are able to override the Cookiebot ID."
msgstr ""
"Se aggiunto, questo sarà l’ID Cookiebot predefinito per tutti i siti "
"secondari. I siti secondari sono in grado di ignorare l’ID Cookiebot."

#: src/view/admin/cb_frame/network-settings-page.php:54
#: src/view/admin/cb_frame/settings/general-page.php:37
msgid "Read more on the Domain Group ID"
msgstr "Per saperne di più sull’ID del gruppo di dominio"

#: src/view/admin/cb_frame/network-settings-page.php:58
#: src/view/admin/cb_frame/settings/general-page.php:43
msgid "Add your Domain Group ID"
msgstr "Aggiungi il tuo ID del gruppo di dominio"

#: src/view/admin/cb_frame/network-settings-page.php:73
#: src/view/admin/cb_frame/settings/general-page.php:146
#: src/view/admin/uc_frame/network-settings-page.php:111
#: src/view/admin/uc_frame/settings/general-page.php:120
msgid "Cookie-blocking"
msgstr "Blocco dei cookie "

#: src/view/admin/cb_frame/network-settings-page.php:75
msgid ""
"Select your cookie-blocking mode here. Auto cookie-blocking mode will "
"automatically block all cookies (except for ‘strictly necessary’ cookies) "
"until a user has given consent. Manual cookie-blocking mode requests manual "
"adjustments to the cookie-setting scripts. Please find our implementation "
"guides below:"
msgstr ""
"Seleziona qui la modalità di blocco dei cookie. La modalità di blocco "
"automatico dei cookie blocca automaticamente tutti i cookie (ad eccezione di "
"quelli “strettamente necessari”) fino a quando l’utente non ha dato il suo "
"consenso. La modalità di blocco manuale dei cookie richiede la regolazione "
"manuale degli script di impostazione dei cookie. Di seguito sono riportate "
"le nostre guide all’implementazione:"

#: src/view/admin/cb_frame/network-settings-page.php:80
#: src/view/admin/cb_frame/settings/general-page.php:167
#: src/view/admin/uc_frame/network-settings-page.php:118
#: src/view/admin/uc_frame/settings/general-page.php:133
msgid "Select cookie-blocking mode"
msgstr "Seleziona la modalità di blocco dei cookie"

#: src/view/admin/cb_frame/network-settings-page.php:87
#: src/view/admin/cb_frame/settings/general-page.php:175
#: src/view/admin/uc_frame/network-settings-page.php:125
#: src/view/admin/uc_frame/settings/general-page.php:141
msgid "Automatic"
msgstr "Automatico"

#: src/view/admin/cb_frame/network-settings-page.php:88
#: src/view/admin/cb_frame/settings/general-page.php:176
#: src/view/admin/uc_frame/network-settings-page.php:126
#: src/view/admin/uc_frame/settings/general-page.php:142
msgid "Recommended"
msgstr "Raccomandato"

#: src/view/admin/cb_frame/network-settings-page.php:96
#: src/view/admin/cb_frame/network-settings-page.php:146
#: src/view/admin/cb_frame/network-settings-page.php:193
#: src/view/admin/uc_frame/network-settings-page.php:134
msgid "Choose per subsite"
msgstr "Scegli per sito secondario"

#: src/view/admin/cb_frame/network-settings-page.php:104
#: src/view/admin/cb_frame/settings/general-page.php:206
msgid "Cookiebot™ script tag"
msgstr "Tag script di Cookiebot™"

#: src/view/admin/cb_frame/network-settings-page.php:106
msgid ""
"Add async or defer attribute to Cookie banner script tag. Default: Choose "
"per subsite"
msgstr ""
"Aggiungi l’attributo async o defer al tag dello script banner cookie. "
"Predefinito: scegli per sito secondario"

#: src/view/admin/cb_frame/network-settings-page.php:109
msgid "This feature is only available when not using Auto Blocking"
msgstr ""
"Questa funzionalità è disponibile solo quando non si utilizza il Blocco "
"automatico"

#: src/view/admin/cb_frame/network-settings-page.php:112
#: src/view/admin/cb_frame/network-settings-page.php:156
#: src/view/admin/cb_frame/network-settings-page.php:228
#: src/view/admin/uc_frame/network-settings-page.php:169
msgid ""
"Setting will apply for all subsites. Subsites will not be able to override."
msgstr ""
"L’impostazione verrà applicata a tutti i siti secondari. I siti secondari "
"non potranno eseguire l’override"

#: src/view/admin/cb_frame/network-settings-page.php:125
#: src/view/admin/cb_frame/network-settings-page.php:172
#: src/view/admin/cb_frame/settings/additional-page.php:99
#: src/view/admin/cb_frame/settings/general-page.php:222
msgid "None"
msgstr "Nessuno"

#: src/view/admin/cb_frame/network-settings-page.php:154
msgid "Cookiebot declaration script tag"
msgstr "Tag script della dichiarazione dei cookie"

#: src/view/admin/cb_frame/network-settings-page.php:159
msgid ""
"Add async or defer attribute to Cookie declaration script tag. Default: "
"Choose per subsite"
msgstr ""
"Aggiungi l’attributo async o defer al tag dello script di dichiarazione dei "
"cookie. Predefinito: scegli per sito secondario"

#: src/view/admin/cb_frame/network-settings-page.php:201
#: src/view/admin/cb_frame/settings/additional-page.php:10
#: src/view/admin/uc_frame/network-settings-page.php:142
#: src/view/admin/uc_frame/settings/additional-page.php:10
msgid "Automatic updates"
msgstr "Aggiornamenti automatici"

#: src/view/admin/cb_frame/network-settings-page.php:203
#: src/view/admin/cb_frame/settings/additional-page.php:13
#: src/view/admin/uc_frame/network-settings-page.php:144
#: src/view/admin/uc_frame/settings/additional-page.php:13
msgid ""
"Enable automatic updates whenever we release a new version of the plugin."
msgstr ""
"Attiva gli aggiornamenti automatici ogni volta che viene lanciata una nuova "
"versione del plugin."

#: src/view/admin/cb_frame/network-settings-page.php:218
#: src/view/admin/cb_frame/settings/additional-page.php:28
#: src/view/admin/uc_frame/network-settings-page.php:159
#: src/view/admin/uc_frame/settings/additional-page.php:28
msgid "Automatically update to new version"
msgstr "Aggiornamento automatico alla nuova versione"

#: src/view/admin/cb_frame/network-settings-page.php:226
#: src/view/admin/cb_frame/settings/general-page.php:248
#: src/view/admin/uc_frame/network-settings-page.php:167
#: src/view/admin/uc_frame/settings/general-page.php:172
msgid "Hide cookie popup"
msgstr "Nascondi il popup dei cookie"

#: src/view/admin/cb_frame/network-settings-page.php:231
#: src/view/admin/cb_frame/settings/general-page.php:254
#: src/view/admin/uc_frame/network-settings-page.php:172
#: src/view/admin/uc_frame/settings/general-page.php:178
msgid ""
"This will remove the cookie consent banner from your website. The cookie "
"declaration shortcode will still be available if you are using Google Tag "
"Manager (or equal), you need to add the Cookiebot script in your Tag Manager."
msgstr ""
"Rimozione del banner di consenso per i cookie dal tuo sito web. Se si "
"utilizza Google Tag Manager (o equivalente) lo shortcode della dichiarazione "
"dei cookie rimarrà disponibile; è necessario aggiungere lo script Cookiebot "
"nel Tag Manager."

#: src/view/admin/cb_frame/network-settings-page.php:246
#: src/view/admin/cb_frame/settings/general-page.php:280
#: src/view/admin/uc_frame/network-settings-page.php:187
#: src/view/admin/uc_frame/settings/general-page.php:204
msgid "Hide the cookie popup banner"
msgstr "Nascondi il banner popup dei cookie"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:11
#: src/view/admin/common/prior-consent/available-addons/tab-header.php:4
#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:14
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:14
msgid "Remember to save your changes before switching tabs"
msgstr "Ricorda di salvare le modifiche prima di cambiare scheda"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:20
msgid "Consent Level API Settings"
msgstr "Impostazioni API per il livello di consenso"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:29
msgid ""
"WP Consent Level API and Cookiebot™ categorize cookies a bit differently. "
"The default settings should fit most needs, but if you need to change the "
"mapping you can do so below."
msgstr ""
"WP Consent Level API e Cookiebot™ classificano i cookie in modo leggermente "
"diverso. Le impostazioni predefinite dovrebbero essere adatte alla maggior "
"parte delle esigenze, ma se hai bisogno di modificare la mappatura puoi "
"farlo qui sotto."

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:79
msgid "Cookiebot™ cookie categories"
msgstr "Categorie dei cookie Cookiebot™"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:96
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:51
msgid "WP Consent API cookies categories equivalent"
msgstr "Equivalente alle categorie dei cookie WP Consent API"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:102
msgid "Functional"
msgstr "Funzionali"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:150
msgid "Statistics Anonymous"
msgstr "Statistici anonimi"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:179
msgid "Reset to default mapping"
msgstr "Ripristino della mappatura predefinita"

#: src/view/admin/cb_frame/settings-page.php:48
msgid "Select at least one vendor on TCF tab"
msgstr "Seleziona almeno un fornitore nella scheda TCF"

#: src/view/admin/cb_frame/settings-page.php:57
#: src/view/admin/uc_frame/settings-page.php:54
msgid "General Settings"
msgstr "Impostazioni generali"

#: src/view/admin/cb_frame/settings-page.php:61
#: src/view/admin/uc_frame/settings-page.php:58
msgid "Additional Settings"
msgstr "Impostazioni aggiuntive"

#: src/view/admin/cb_frame/settings-page.php:65
#: src/view/admin/cb_frame/settings/gtm-page.php:27
#: src/view/admin/uc_frame/settings-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:12
#: src/view/admin/uc_frame/settings/gtm-page.php:27
msgid "Google Tag Manager"
msgstr "Google Tag Manager"

#: src/view/admin/cb_frame/settings-page.php:69
#: src/view/admin/cb_frame/settings/gcm-page.php:50
#: src/view/admin/uc_frame/settings-page.php:66
#: src/view/admin/uc_frame/settings/gcm-page.php:28
#: src/view/admin/uc_frame/settings/gcm-page.php:43
msgid "Google Consent Mode"
msgstr "Modalità di consenso di Google"

#: src/view/admin/cb_frame/settings-page.php:73
#: src/view/admin/uc_frame/settings/embeddings-page.php:33
msgid "TCF"
msgstr "TCF"

#: src/view/admin/cb_frame/settings-page.php:77
msgid "Multiple Configurations"
msgstr "Configurazioni multiple"

#: src/view/admin/cb_frame/settings/additional-page.php:37
#: src/view/admin/uc_frame/settings/additional-page.php:37
msgid "Show the banner while logged in"
msgstr "Mostrare il banner quando si è connessi"

#: src/view/admin/cb_frame/settings/additional-page.php:40
#: src/view/admin/uc_frame/settings/additional-page.php:40
msgid ""
"You can choose to display the consent banner on your website while you’re "
"logged in and changing settings or customizing your banner."
msgstr ""
"Puoi scegliere di visualizzare il banner di consenso sul tuo sito web mentre "
"sei connesso e stai modificando le impostazioni o personalizzando il banner."

#: src/view/admin/cb_frame/settings/additional-page.php:66
#: src/view/admin/uc_frame/settings/additional-page.php:66
msgid "Show banner on website when logged in"
msgstr "Mostrare il banner sul sito web quando si è connessi"

#: src/view/admin/cb_frame/settings/additional-page.php:75
msgid "Cookie declaration script tag:"
msgstr "Tag script della dichiarazione dei cookie:"

#: src/view/admin/cb_frame/settings/additional-page.php:78
msgid ""
"If you implemented the declaration on your page through our widget in "
"WordPress, you can choose here how the script should be loaded."
msgstr ""
"Se hai implementato la dichiarazione sulla tua pagina usando il nostro "
"widget su WordPress, puoi scegliere qui le modalità di caricamento dello "
"script."

#: src/view/admin/cb_frame/settings/additional-page.php:84
msgid "Select the cookie declaration script loading setting"
msgstr ""
"Seleziona le impostazioni di caricamento dello script della dichiarazione "
"dei cookie"

#: src/view/admin/cb_frame/settings/additional-page.php:116
#: src/view/admin/cb_frame/settings/general-page.php:187
#: src/view/admin/cb_frame/settings/general-page.php:239
#: src/view/admin/cb_frame/settings/general-page.php:283
#: src/view/admin/uc_frame/settings/general-page.php:153
#: src/view/admin/uc_frame/settings/general-page.php:207
msgid "Disabled by active setting in Network Settings"
msgstr "Disabilitato dall'impostazione attiva in Impostazioni di rete"

#: src/view/admin/cb_frame/settings/additional-page.php:125
msgid "Ignore scripts in queue from Cookiebot CMP scan:"
msgstr "Ignora gli script in coda dalla scansione di Cookiebot CMP:"

#: src/view/admin/cb_frame/settings/additional-page.php:128
msgid ""
"List scripts source URL (one per line) from the queue to ignore Cookiebot "
"CMP scan. Partial source URL will also work, e.g. wp-content/plugins/"
"woocommerce will block every WooCommerce script."
msgstr ""
"Elenca gli URL di origine degli script (uno per riga) dalla coda per "
"ignorare la scansione CMP di Cookiebot. Anche un URL sorgente parziale "
"funzionerà, ad esempio wp-content/plugins/woocommerce bloccherà tutti gli "
"script WooCommerce."

#: src/view/admin/cb_frame/settings/additional-page.php:131
msgid ""
"This feature only works for scripts loaded via wp_enqueue_script. Manually "
"added scripts must be manually edited."
msgstr ""
"Questo strumento funziona solo per script caricati mediante "
"wp_enqueue_script. Gli script aggiunti manualmente devono essere modificati "
"manualmente."

#: src/view/admin/cb_frame/settings/additional-page.php:137
msgid "Script source URL:"
msgstr "URL di origine degli script:"

#: src/view/admin/cb_frame/settings/additional-page.php:143
msgid "Add script source URL, one per line"
msgstr "Aggiungi l’URL di origine dello script, uno per riga"

#: src/view/admin/cb_frame/settings/gcm-page.php:14
msgid "What is Google Consent Mode and why should you enable it?"
msgstr "Cos’è la modalità di consenso Google e perchè dovresti attivarla?"

#: src/view/admin/cb_frame/settings/gcm-page.php:17
msgid ""
"Google Consent Mode is a way for your website to measure conversions and get "
"analytics insights while being fully GDPR-compliant when using services like "
"Google Analytics, Google Tag Manager (GTM) and Google Ads."
msgstr ""
"La modalità di consenso di Google consente al tuo sito web di misurare le "
"conversioni ed ottenere approfondimenti ed analisi nel pieno rispetto del "
"GDPR quando utilizzi servizi come Google Analytics, Google Tag Manager (GTM) "
"e Google Ads."

#: src/view/admin/cb_frame/settings/gcm-page.php:20
msgid ""
"Cookiebot consent managment platform (CMP) and Google Consent Mode integrate "
"seamlessly to offer you plug-and-play compliance and streamlined use of all "
"Google's services in one easy solution."
msgstr ""
"La piattaforma di gestione del consenso (CMP) di Cookiebot™ e la modalità di "
"consenso Google si integrano perfettamente per offrirti una conformità plug-"
"and-play e un uso semplificato di tutti i servizi di Google in un’unica "
"soluzione."

#: src/view/admin/cb_frame/settings/gcm-page.php:24
msgid "Read more about Cookiebot CMP and Google Consent Mode"
msgstr "Per saperne di più su Cookiebot CMP e la modalità di consenso Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:31
msgid "Google Consent Mode:"
msgstr "Modalità di consenso Google:"

#: src/view/admin/cb_frame/settings/gcm-page.php:34
msgid ""
"Enable Google Consent Mode with default settings on your WordPress page."
msgstr ""
"Attiva la modalità di consenso Google con impostazioni predefinite sulla tua "
"pagina WordPress."

#: src/view/admin/cb_frame/settings/gcm-page.php:38
#: src/view/admin/cb_frame/settings/gcm-page.php:68
#: src/view/admin/cb_frame/settings/gtm-page.php:18
msgid "Read more"
msgstr "Per saperne di più"

#: src/view/admin/cb_frame/settings/gcm-page.php:61
msgid "URL passthrough:"
msgstr "Passaggio dell’URL:"

#: src/view/admin/cb_frame/settings/gcm-page.php:64
msgid ""
"This feature will allow you to pass data between pages when not able to use "
"cookies without/prior consent."
msgstr ""
"Questa funzione ti consentirà di trasferire dati tra le pagine quando non "
"sei in grado di utilizzare i cookie senza/previo consenso."

#: src/view/admin/cb_frame/settings/gcm-page.php:80
msgid "URL passthrough"
msgstr "Passaggio dell’URL"

#: src/view/admin/cb_frame/settings/gcm-page.php:89
msgid "Google Consent Mode cookies"
msgstr "Cookie della Modalità di consenso di Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:91
msgid ""
"Select the cookie types that need to be consented for the Google Consent "
"Mode script"
msgstr ""
"Seleziona le tipologie di cookie alle quali è necessario acconsentire per lo "
"script della Modalità di consenso di Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:93
#: src/view/admin/cb_frame/settings/general-page.php:212
#: src/view/admin/cb_frame/settings/general-page.php:251
#: src/view/admin/cb_frame/settings/gtm-page.php:80
#: src/view/admin/uc_frame/settings/general-page.php:175
msgid "This feature is only available when using Manual Blocking"
msgstr ""
"Questa funzionalità è disponibile solo quando si utilizza il blocco manuale"

#: src/view/admin/cb_frame/settings/gcm-page.php:95
#: src/view/admin/cb_frame/settings/gtm-page.php:82
msgid ""
"This option may affect the behaviour of your GTM Tags, as the script will "
"run on the selected cookies consent."
msgstr ""
"Questa opzione può influire sul comportamento dei tuoi tag GTM, poiché lo "
"script verrà eseguito in base al consenso ai cookie selezionati."

#: src/view/admin/cb_frame/settings/gcm-page.php:97
#: src/view/admin/cb_frame/settings/gtm-page.php:84
msgid ""
"Please make sure your Tags in Google Tag Manager are triggered correctly."
msgstr ""
"Assicurati che i tuoi tag in Google Tag Manager vengano attivati "
"correttamente."

#: src/view/admin/cb_frame/settings/gcm-page.php:102
#: src/view/admin/cb_frame/settings/gtm-page.php:89
#: src/view/admin/common/prior-consent/available-addons/tab.php:28
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:25
msgid "Check one or multiple cookie types:"
msgstr "Seleziona una o più tipologie di cookie:"

#: src/view/admin/cb_frame/settings/general-page.php:30
msgid "Connect your Domain Group"
msgstr "Connetti il tuo gruppo di domini"

#: src/view/admin/cb_frame/settings/general-page.php:33
msgid ""
"To connect your Domain Group, paste your Domain Group ID here. If you want "
"to connect a second ID for other regions, you can do this under the "
"\"Multiple Configurations\" tab."
msgstr ""
"Per collegare il tuo gruppo di dominio, incolla qui l’ID del gruppo di "
"dominio. Se desideri collegare un secondo ID per altre regioni, puoi farlo "
"dalla scheda “Configurazioni multiple”."

#: src/view/admin/cb_frame/settings/general-page.php:54
#: src/view/admin/uc_frame/settings/general-page.php:48
msgid "Using network account"
msgstr "Utilizzando l’account di rete"

#: src/view/admin/cb_frame/settings/general-page.php:64
#: src/view/admin/uc_frame/settings/general-page.php:59
msgid "Do not use Network Settings ID"
msgstr "Non utilizzare l’ID dell’account di rete"

#: src/view/admin/cb_frame/settings/general-page.php:75
msgid "Language:"
msgstr "Lingua:"

#: src/view/admin/cb_frame/settings/general-page.php:77
msgid "Select your main language here."
msgstr "Seleziona qui la tua lingua principale."

#: src/view/admin/cb_frame/settings/general-page.php:82
msgid "Select the language"
msgstr "Seleziona la lingua"

#: src/view/admin/cb_frame/settings/general-page.php:86
msgid "Default (Autodetect)"
msgstr "Predefinito (rilevato automaticamente)"

#: src/view/admin/cb_frame/settings/general-page.php:90
msgid "Use WordPress Language"
msgstr "Usa la lingua di WordPress"

#: src/view/admin/cb_frame/settings/general-page.php:111
msgid ""
"If enabled, Cookiebot™ will use the current location to set the banner and "
"cookie declaration language."
msgstr ""
"Se abilitato, Cookiebot™ utilizzerà la posizione attuale per impostare la "
"lingua del banner e della dichiarazione dei cookie."

#: src/view/admin/cb_frame/settings/general-page.php:114
msgid ""
"Please make sure that all languages in use have been added in the Cookiebot™ "
"Manager."
msgstr ""
"Assicurati che tutte le lingue utilizzate siano state aggiunte anche "
"nel’interfaccia Manager di Cookiebot™."

#: src/view/admin/cb_frame/settings/general-page.php:116
msgid "This feature disables the main language selector."
msgstr "Questa funzione disabilita il selettore della lingua principale."

#: src/view/admin/cb_frame/settings/general-page.php:117
msgid ""
"If you have already set a language in the cookie declaration shortcode, this "
"feature will not change it."
msgstr ""
"Se hai già impostato una lingua nello shortcode della dichiarazione dei "
"cookie, questa funzione non la modificherà."

#: src/view/admin/cb_frame/settings/general-page.php:120
msgid "Read more on how to add languages"
msgstr "Per saperne di più su come aggiungere lingue"

#: src/view/admin/cb_frame/settings/general-page.php:135
msgid "Use website location to set language"
msgstr "Utilizza la posizione del sito web per impostare la lingua."

#: src/view/admin/cb_frame/settings/general-page.php:149
#: src/view/admin/uc_frame/network-settings-page.php:113
#: src/view/admin/uc_frame/settings/general-page.php:123
msgid ""
"Choose the type of your cookie-blocking mode. Select automatic to "
"automatically block all cookies except those strictly necessary to use "
"before user gives consent. Manual mode lets you adjust your cookie settings "
"within your website’s HTML."
msgstr ""
"Seleziona il tipo di modalità di blocco dei cookie. Seleziona Automatico per "
"bloccare automaticamente tutti i cookie, eccetto quelli strettamente "
"necessari prima che l’utente dia il suo consenso. La modalità Manuale "
"consente di regolare le impostazioni dei cookie all’interno dell’HTML del "
"sito web."

#: src/view/admin/cb_frame/settings/general-page.php:154
msgid "Guide to auto cookie-blocking"
msgstr "Guida al blocco automatico dei cookie"

#: src/view/admin/cb_frame/settings/general-page.php:160
msgid "Guide to manual cookie-blocking"
msgstr "Guida al blocco manuale dei cookie"

#: src/view/admin/cb_frame/settings/general-page.php:184
#: src/view/admin/uc_frame/settings/general-page.php:150
msgid "Manual"
msgstr "Manuale"

#: src/view/admin/cb_frame/settings/general-page.php:209
msgid "Add async or defer attribute to cookie declaration script tag"
msgstr ""
"Aggiungi gli attributi “async” o “defer” ai tag script della dichiarazione "
"del cookie"

#: src/view/admin/cb_frame/settings/gtm-page.php:12
msgid "Google Tag Manager:"
msgstr "Google Tag Manager:"

#: src/view/admin/cb_frame/settings/gtm-page.php:14
msgid "For more details about Cookiebot CMP and Google Tag Manager."
msgstr "Per maggiori dettagli su Cookiebot CMP e Google Tag Manager."

#: src/view/admin/cb_frame/settings/gtm-page.php:35
#: src/view/admin/cb_frame/settings/gtm-page.php:46
#: src/view/admin/uc_frame/settings/gtm-page.php:35
#: src/view/admin/uc_frame/settings/gtm-page.php:46
msgid "Google Tag Manager ID"
msgstr "ID di Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:37
msgid "Paste your Tag Manager ID into the field on the right."
msgstr "Incolla il tuo ID di Google Tag Manager nel campo sulla destra."

#: src/view/admin/cb_frame/settings/gtm-page.php:41
#: src/view/admin/uc_frame/settings/gtm-page.php:41
msgid "How to find the GTM ID"
msgstr "Come trovare l’ID di GTM"

#: src/view/admin/cb_frame/settings/gtm-page.php:48
msgid "Enter GTM ID"
msgstr "Inserisci l’ID di GTM"

#: src/view/admin/cb_frame/settings/gtm-page.php:56
msgid "Data Layer Name (optional)"
msgstr "Nome del livello dati (opzionale)"

#: src/view/admin/cb_frame/settings/gtm-page.php:58
msgid ""
"You can also paste your Data Layer Name here. This is optional information."
msgstr ""
"Puoi incollare il nome del tuo livello dati qui. Questa informazione è "
"opzionale."

#: src/view/admin/cb_frame/settings/gtm-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:62
msgid "How to find the Data Layer Name"
msgstr "Come trovare il nome del livello dati"

#: src/view/admin/cb_frame/settings/gtm-page.php:67
#: src/view/admin/uc_frame/settings/gtm-page.php:67
msgid "Name of your Data Layer"
msgstr "Nome del tuo livello dati"

#: src/view/admin/cb_frame/settings/gtm-page.php:76
msgid "Google Tag Manager cookies"
msgstr "Cookie di Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:78
msgid ""
"Select the cookie types that need to be consented for the Google Tag Manager "
"script"
msgstr ""
"Seleziona le tipologie di cookie alle quali è necessario acconsentire per lo "
"script di Google Tag Manager"

#: src/view/admin/cb_frame/settings/iab-page.php:26
msgid "IAB Integration:"
msgstr "Integrazione IAB:"

#: src/view/admin/cb_frame/settings/iab-page.php:28
msgid ""
"If you want to use the IAB Framework TCF within your Consent Management "
"Platform (CMP) you can enable it on the right. Be aware that activating this "
"could override some of the configurations you made with the default setup "
"defined by the IAB."
msgstr ""
"Se desideri utilizzare lo IAB Framework TCF all’interno della tua "
"piattaforma di gestione del consenso (CMP), puoi attivarlo sulla destra. "
"Tieni presente che l’attivazione potrebbe annullare alcune delle "
"configurazioni effettuate mediante l’impostazione predefinita definita da "
"IAB."

#: src/view/admin/cb_frame/settings/iab-page.php:32
msgid "Read more on IAB with Cookiebot CMP here"
msgstr "Per saperne di più su IAB con Cookiebot CMP"

#: src/view/admin/cb_frame/settings/iab-page.php:41
msgid "IAB TCF V2.2 integration"
msgstr "Integrazione IAB TCF 2.2"

#: src/view/admin/cb_frame/settings/iab-page.php:83
msgid ""
"IAB vendor list is temporarily offline. Please try refreshing the page after "
"a couple of minutes."
msgstr ""
"L’elenco dei fornitori IAB è temporaneamente offline. Provare ad aggiornare "
"la pagina tra un paio di minuti."

#: src/view/admin/cb_frame/settings/iab-page.php:85
msgid ""
"If you had previously saved configurations, don’t worry, they will continue "
"to work."
msgstr ""
"Eventuali configurazioni salvate in precedenza continueranno a funzionare."

#: src/view/admin/cb_frame/settings/iab-page.php:108
msgid "Sharing data with third-party vendors"
msgstr "Condivisione dei dati con fornitori terzi"

#: src/view/admin/cb_frame/settings/iab-page.php:112
msgid ""
"Select vendors with whom you’ll share users’ data. We’ll include this "
"information on the second layer of your consent banner, where users "
"interested in more granular detail about who will access their data can view "
"it."
msgstr ""
"Seleziona i fornitori con cui condividerai i dati degli utenti. Includeremo "
"queste informazioni nel secondo livello del banner di consenso, dove gli "
"utenti interessati a dettagli più granulari su chi accederà ai loro dati "
"potranno consultarli."

#: src/view/admin/cb_frame/settings/iab-page.php:130
#: src/view/admin/cb_frame/settings/iab-page.php:162
#: src/view/admin/cb_frame/settings/iab-page.php:218
msgid "Search"
msgstr "Ricerca"

#: src/view/admin/cb_frame/settings/iab-page.php:131
#: src/view/admin/cb_frame/settings/iab-page.php:163
msgid "Select All"
msgstr "Selezionare tutto "

#: src/view/admin/cb_frame/settings/iab-page.php:132
#: src/view/admin/cb_frame/settings/iab-page.php:164
msgid "Deselect All"
msgstr "Deselezionare tutto"

#: src/view/admin/cb_frame/settings/iab-page.php:133
msgid "Select at least one vendor"
msgstr "Seleziona almeno un fornitore"

#: src/view/admin/cb_frame/settings/iab-page.php:157
msgid "Google Ads certified external vendors"
msgstr "Fornitori esterni certificati da Google Ads"

#: src/view/admin/cb_frame/settings/iab-page.php:188
msgid "Restrictions of data use purposes for vendors"
msgstr "Restrizioni sulle finalità di utilizzo dei dati per i fornitori"

#: src/view/admin/cb_frame/settings/iab-page.php:192
msgid ""
"Set restrictions on data use purposes for specific vendors. Add vendors and "
"the data use purposes that each vendor is allowed. We’ll share this "
"information with users within your consent banner."
msgstr ""
"Imposta restrizioni sulle finalità di utilizzo dei dati per fornitori "
"specifici. Aggiungi i fornitori e le finalità di utilizzo dei dati "
"consentite a ciascun fornitore. Condivideremo queste informazioni con gli "
"utenti all’interno del vostro banner di consenso."

#: src/view/admin/cb_frame/settings/iab-page.php:198
msgid "Add Vendor"
msgstr "Aggiungi fornitore"

#: src/view/admin/cb_frame/settings/iab-page.php:204
#: src/view/admin/cb_frame/settings/iab-page.php:215
msgid "Select Vendor"
msgstr "Seleziona fornitore"

#: src/view/admin/cb_frame/settings/iab-page.php:228
msgid "Set Purposes"
msgstr "Scopi del Set"

#: src/view/admin/cb_frame/settings/multiple-configuration/extra/region-item.php:18
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:75
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:101
msgid "Select region"
msgstr "Seleziona regione"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:16
msgid "Additional configurations:"
msgstr "Configurazioni aggiuntive:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:18
msgid ""
"You can add a second alternative banner or configuration to your website by "
"creating a second Domain Group and specify it on a region."
msgstr ""
"È possibile aggiungere un secondo banner o una configurazione alternativa al "
"tuo sito web creando un secondo gruppo di domini e specificando una regione."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:22
msgid "Read more about multiple configurations here"
msgstr "Per saperne di più sulle configurazioni multiple"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:34
msgid "Multiple configurations"
msgstr "Configurazioni multiple"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:44
msgid "Set up your additional banner configuration:"
msgstr "Imposta una configurazione aggiuntiva del tuo banner:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:47
msgid ""
"To enable a different configuration, create a separate DomainGroup without "
"adding the domain to it and paste the ID below. Then select the countries in "
"which you want to show this configuration. For example, if your main Domain "
"Group is defined as a banner matching GDPR requirements, you might want to "
"add another Domain Group for visitors from California. The number of "
"additional configurations is restricted to one at the moment."
msgstr ""
"Per abilitare una configurazione diversa, crea un gruppo di dominio separato "
"senza aggiungerci il dominio e incolla l’ID qui sotto. Seleziona poi i Paesi "
"in cui desideri mostrare questa configurazione. Ad esempio, se il gruppo di "
"domini principale è definito come un banner conforme ai requisiti GDPR, "
"potresti aggiungere un altro gruppo di domini per i visitatori provenienti "
"dalla California. Al momento il numero di configurazioni aggiuntive è "
"limitato a una."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:57
msgid "Domain Group ID"
msgstr "ID del gruppo di dominio"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:61
msgid "Region"
msgstr "Regione"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:71
msgid "Primary domain group"
msgstr "Gruppo di dominio principale"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:138
msgid "Add banner"
msgstr "Aggiungi banner"

#: src/view/admin/cb_frame/support-page.php:23
msgid "Need help with your configuration?"
msgstr "Hai bisogno di aiuto con la tua configurazione?"

#: src/view/admin/cb_frame/support-page.php:26
msgid ""
"In our Help Center you find all the answers to your questions. If you have "
"additional questions, create a support request and our Support Team will "
"help out as soon as possible."
msgstr ""
"Nel nostro Centro Assistenza troverai tutte le risposte alle tue domande. Se "
"hai altre domande, crea una richiesta di assistenza e il nostro team di "
"supporto ti aiuterà il prima possibile."

#: src/view/admin/cb_frame/support-page.php:34
msgid "Visit Cookiebot CMP Help Center"
msgstr "Visita il Centro Assistenza di Cookiebot CMP"

#: src/view/admin/cb_frame/support-page.php:58
msgid "How to find my Cookiebot™ ID"
msgstr "Come posso trovare il mio ID Cookiebot™?"

#. translators: the first placeholder string will be replaced with a html anchor open tag and the second placeholder string will be replaced by the html anchor closing tag
#: src/view/admin/cb_frame/support-page.php:66
msgid "Log in to your %1$sCookiebot CMP account%2$s."
msgstr "Accedi al tuo %1$saccount Cookiebot CMP%2$s."

#. translators: the placeholder strings denote the positions of <b>, </b>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:76
msgid "Go to %1$s\"Settings\"%2$s and setup your Cookiebot CMP"
msgstr "Vai su %1$s“Impostazioni”%2$s e configura Cookiebot CMP"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:86
msgid "Go to the %1$s\"Your scripts\"%2$s tab"
msgstr "Vai alla scheda %1$s“I tuoi script”%2$s"

#: src/view/admin/cb_frame/support-page.php:92
msgid ""
"Copy the value inside the data-cid parameter - eg.: abcdef12-3456-7890-abcd-"
"ef1234567890"
msgstr ""
"Copia il valore all’interno del parametro data-cid, ad esempio: "
"abcdef12-3456-7890-abcd-ef1234567890"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:97
msgid ""
"Add %1$s[cookie_declaration]%2$s shortcode to a page to show the declaration"
msgstr ""
"Aggiungi lo shortcode %1$s[cookie_declaration]%2$s a una pagina per mostrare "
"la dichiarazione"

#: src/view/admin/cb_frame/support-page.php:103
msgid "Remember to change your scripts as described below"
msgstr "Ricorda di cambiare i tuoi script come descritto sotto"

#: src/view/admin/cb_frame/support-page.php:110
msgid "Add the Cookie Declaration to your website"
msgstr "Aggiungi la dichiarazione dei cookie al tuo sito web"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:116
msgid ""
"Use the shortcode %1$s[cookie_declaration]%2$s to add the cookie declaration "
"to a page or post. The cookie declaration will always show the latest "
"version from Cookiebot CMP."
msgstr ""
"Usa lo shortcode %1$s[cookie_declaration]%2$s per aggiungere la "
"dichiarazione dei cookie a una pagina o a un post. La dichiarazione dei "
"cookie mostrerà sempre l’ultima versione di Cookiebot CMP."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:126
msgid ""
"If you want to show the cookie declaration in a specific language, you can "
"add the %1$s\"lang\"%2$s attribute, e.g. %3$s[cookie_declaration "
"lang=\"de\"]%4$s."
msgstr ""
"Se desideri mostrare la dichiarazione dei cookie in una lingua specifica, "
"puoi aggiungere l’attributo %1$s“lang”%2$s, ad esempio "
"%3$s[cookie_declaration lang=“it”]%4$s."

#: src/view/admin/cb_frame/support-page.php:138
msgid "Update your script tags"
msgstr "Aggiorna i tuoi tag script"

#: src/view/admin/cb_frame/support-page.php:142
msgid ""
"To enable prior consent, apply the attribute \"data-cookieconsent\" to "
"cookie-setting script tags on your website. Set the comma-separated value to "
"one or more of the cookie categories \"preferences\", \"statistics\" and/or "
"\"marketing\" in accordance with the types of cookies being set by each "
"script. Finally, change the attribute \"type\" from \"text/javascript\" to "
"\"text/plain\"."
msgstr ""
"Per abilitare il consenso preventivo, applica l’attributo “data-"
"cookieconsent” ai tag script di impostazione dei cookie sul tuo sito web. "
"Imposta il valore separato da virgole per una o più categorie di cookie tra "
"“preferenze”, “statistiche” e/o “marketing”, in base ai tipi di cookie "
"impostati da ciascuno script. Infine, modifica l’attributo “type” da “text/"
"javascript” a “text/plain”."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:152
msgid ""
"Example on modifying an existing Google Analytics Universal script tag can "
"be found %1$shere in step 4%2$s."
msgstr ""
"Un esempio di modifica di un tag script universale di Google Analytics "
"esistente si %1$strova al punto 4%2$s."

#: src/view/admin/cb_frame/support-page.php:172
msgid "Helper function to update your scripts"
msgstr "Funzione di aiuto per aggiornare i tuoi script"

#: src/view/admin/cb_frame/support-page.php:176
msgid ""
"You can update your scripts yourself. However, Cookiebot CMP also offers a "
"small helper function that can make the work easier."
msgstr ""
"Puoi aggiornare i tuoi script autonomamente. In ogni caso, Cookiebot CMP "
"offre una piccola funzione di aiuto che può facilitare questa operazione."

#: src/view/admin/cb_frame/support-page.php:183
msgid "Update your script tags this way:"
msgstr "Aggiorna i tuoi tag script in questo modo:"

#. translators: %1$s refers to the original script tag HTML, and %2$s refers to its replacement
#: src/view/admin/cb_frame/support-page.php:188
msgid "%1$s to %2$s"
msgstr "Da %1$s a %2$s"

#: src/view/admin/common/dashboard-page.php:31
msgid "I already have an account"
msgstr "Ho già un account"

#: src/view/admin/common/dashboard-page.php:35
msgid "Connect my account"
msgstr "Connetti il mio account"

#: src/view/admin/common/dashboard-page.php:48
#: src/view/admin/common/network-settings-page.php:33
#: src/view/admin/common/settings-page.php:37
msgid "New to our solutions? Create your account. "
msgstr "Non conosci le nostre soluzioni? Crea il tuo account."

#: src/view/admin/common/dashboard-page.php:65
msgid "How to get started"
msgstr "Come iniziare"

#: src/view/admin/common/dashboard-page.php:69
msgid "Learn more about your CMP"
msgstr "Ottieni informazioni sulla tua CMP"

#: src/view/admin/common/network-settings-page.php:35
#: src/view/admin/common/settings-page.php:39
msgid ""
"If you’re new to our solutions, create an account first to obtain your "
"settings ID."
msgstr ""
"Se non conosci le nostre soluzioni, crea un account per ottenere un numero "
"identificativo (ID)."

#: src/view/admin/common/network-settings-page.php:40
#: src/view/admin/common/settings-page.php:44
msgid "Create your account"
msgstr "Crea il tuo account"

#: src/view/admin/common/network-settings-page.php:49
#: src/view/admin/common/settings-page.php:52
#: src/view/admin/uc_frame/network-settings-page.php:51
msgid "Connect your account"
msgstr "Connetti il tuo account"

#: src/view/admin/common/network-settings-page.php:52
#: src/view/admin/common/settings-page.php:55
msgid "Enter the ID of your account to quickly connect it with the plugin."
msgstr "Inserisci l'ID del tuo account per connetterlo rapidamente al plugin."

#: src/view/admin/uc_frame/network-settings-page.php:54
msgid "Enter your settings ID to quickly link your account with the plugin."
msgstr "Inserisci l’ID del tuo account per connetterlo velocemente al plugin."

#: src/view/admin/common/network-settings-page.php:55
#: src/view/admin/uc_frame/network-settings-page.php:57
msgid ""
"If added this will be the default account for all subsites. Subsites are "
"able to override this and use their own account."
msgstr ""
"Una volta aggiunto, questo sarà l’account predefinito per tutti i sottositi. "
"I sottositi possono ignorare questo account e utilizzarne uno proprio."

#: src/view/admin/common/network-settings-page.php:59
#: src/view/admin/common/settings-page.php:59
#: src/view/admin/uc_frame/network-settings-page.php:61
#: src/view/admin/uc_frame/settings/general-page.php:29
msgid "Where to find settings ID"
msgstr "Dove trovare l’ID dell’account"

#: src/view/admin/common/network-settings-page.php:63
#: src/view/admin/common/settings-page.php:63
msgid "How to find your Usercentrics Settings ID"
msgstr "Come trovare il tuo ID impostazioni Usercentrics"

#: src/view/admin/common/network-settings-page.php:70
#: src/view/admin/common/settings-page.php:70
msgid "How to find your Cookiebot CMP Domain Group ID"
msgstr "Come trovare il tuo ID gruppo dominio CMP di Cookiebot"

#: src/view/admin/common/network-settings-page.php:65
#: src/view/admin/common/settings-page.php:65
msgid "Settings ID or Domain Group ID"
msgstr "ID delle impostazioni o ID del gruppo di dominio"

#: src/view/admin/uc_frame/network-settings-page.php:67
#: src/view/admin/uc_frame/settings/general-page.php:35
msgid "Your settings ID"
msgstr "ID del tuo account"

#: src/view/admin/common/network-settings-page.php:82
#: src/view/admin/common/settings-page.php:82
#: src/view/admin/uc_frame/network-settings-page.php:83
#: src/view/admin/uc_frame/settings/general-page.php:70
msgid ""
"Let us know if your account is set for compliance with a single privacy law "
"(e.g. GDPR) or multiple laws (e.g. GDPR and CCPA) based on user’s location. "
"The default is a single privacy law, so this is likely your setting unless "
"modified."
msgstr ""
"Comunicaci se il tuo account è configurato per rispettare un’unica normativa "
"sulla privacy (ad es. GDPR) o più normative (ad es. GDPR e CCPA) in base "
"alla posizione dell’utente. Per default, gli account vengono configurati per "
"rispettare un’unica normativa sulla privacy, quindi è probabile che questa "
"sia la tua impostazione, ma puoi modificarla."

#: src/view/admin/common/network-settings-page.php:88
#: src/view/admin/common/settings-page.php:88
#: src/view/admin/uc_frame/network-settings-page.php:89
#: src/view/admin/uc_frame/settings/general-page.php:76
msgid "Your current account setup:"
msgstr "La tua configurazione attuale:"

#: src/view/admin/common/network-settings-page.php:95
#: src/view/admin/common/settings-page.php:95
#: src/view/admin/uc_frame/network-settings-page.php:96
#: src/view/admin/uc_frame/settings/general-page.php:83
msgid "Compliance with one privacy law"
msgstr "Conformità ad una normativa sulla privacy"

#: src/view/admin/common/network-settings-page.php:102
#: src/view/admin/common/settings-page.php:102
#: src/view/admin/uc_frame/network-settings-page.php:103
#: src/view/admin/uc_frame/settings/general-page.php:90
msgid "Compliance with multiple privacy laws (geolocation)"
msgstr "Conformità a più normative sulla privacy (geolocalizzazione)"

#: src/view/admin/common/prior-consent/available-addons/tab.php:25
msgid "Plugin enabled"
msgstr "Plugin attivato"

#: src/view/admin/common/prior-consent/available-addons/tab.php:58
msgid "Placeholder text:"
msgstr "Testo segnaposto:"

#: src/view/admin/common/prior-consent/available-addons/tab.php:67
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:53
msgid "Display a placeholder"
msgstr "Visualizza un segnaposto"

#: src/view/admin/common/prior-consent/available-addons/tab.php:78
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:71
msgid "+ Add language"
msgstr "+ Aggiungi lingua"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:7
msgid "Jetpack settings"
msgstr "Impostazioni di Jetpack"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:8
msgid "Enable Jetpack on \"Available Addons\" to see this page options."
msgstr ""
"Attivare Jetpack in “Componenti aggiuntivi disponibili” per visualizzare le "
"opzioni di questa pagina."

#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:15
msgid "Enable"
msgstr "Attiva"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:11
msgid "Show advanced options"
msgstr "Mostra opzioni avanzate"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:17
msgid "This is for more advanced users."
msgstr "Questo è per gli utenti più avanzati."

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:23
msgid "Regex:"
msgstr "Regex:"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:35
msgid "Edit regex"
msgstr "Modifica regex"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:49
msgid "Reset to default regex"
msgstr "Ripristina l’regex predefinita"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:4
msgid "Information"
msgstr "Informazioni"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:5
msgid ""
"These add-ons are created by a dedicated open-source community to make it "
"easier for you to manage cookie and tracker consent on your WordPress site. "
"They’re designed to help you ensure ‘prior consent’ even for plugins that "
"don’t include this feature."
msgstr ""
"Questi componenti aggiuntivi sono stati creati da una comunità open-source "
"per semplificare la gestione del consenso ai cookie e ai tracker sui siti "
"WordPress. Essi sono progettati per aiutarti a garantire il “consenso "
"preliminare” anche per i plugin che non includono questa funzionalità."

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:6
msgid ""
"Right now, these add-ons are the best way for you to signal user consent to "
"other plugins. While we don’t know if or when WordPress Core will add this "
"functionality, these tools are here to support you and work seamlessly with "
"Usercentrics solution."
msgstr ""
"Al momento, questi componenti aggiuntivi rappresentano il modo migliore per "
"trasmettere il consenso degli utenti ad altri plugin. Forse in futuro "
"WordPress Core aggiungerà questa funzionalità, ma per ora questi strumenti "
"sono estremamente utili e funzionano perfettamente con la soluzione "
"Usercentrics."

#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:10
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:19
#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:26
msgid "Language"
msgstr "Lingua"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:4
msgid "Unavailable plugins"
msgstr "Plugin non disponibili"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:5
msgid ""
"The following addons are unavailable. This is because the corresponding "
"plugin is not installed or activated."
msgstr ""
"I seguenti componenti aggiuntivi non sono disponibili. Questo perché il "
"plugin corrispondente non è installato né attivato."

#: src/view/admin/common/support-page.php:23
#: src/view/admin/uc_frame/support-page.php:23
msgid "Need help?"
msgstr "Hai bisogno d’aiuto?"

#: src/view/admin/common/support-page.php:26
#: src/view/admin/uc_frame/support-page.php:26
msgid ""
"Visit our Support Center to find answers to your questions or get help with "
"configuration. If you need further assistance, use the Contact Support "
"button in the top navigation to create a support request. We’ll respond as "
"soon as possible."
msgstr ""
"Visita il nostro Centro di assistenza per trovare le risposte alle tue "
"domande o per ottenere aiuto nella configurazione. Se hai bisogno di "
"ulteriore assistenza, clicca su Contatta l’assistenza in alto per creare una "
"richiesta. Ti risponderemo il prima possibile."

#: src/view/admin/common/support-page.php:34
#: src/view/admin/uc_frame/support-page.php:34
msgid "Go to Support Center"
msgstr "Accedere al Centro di assistenza"

#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:6
msgid "You will need to add a new ID before updating other settings"
msgstr ""
"È necessario aggiungere un nuovo ID prima di aggiornare le altre "
"impostazioni."

#: src/view/admin/common/templates/extra/review-form.php:10
msgid "Cookiebot CMP Deactivation"
msgstr "Disattivazione di Cookiebot CMP"

#: src/view/admin/common/templates/extra/review-form.php:14
msgid "We are sad to lose you. Take a moment to help us improve?"
msgstr "Ci dispiace vederti andare via. Ci potresti aiutare a migliorare?"

#: src/view/admin/common/templates/extra/review-form.php:19
msgid "The installation is too complicated"
msgstr "L’installazione è troppo complicata"

#: src/view/admin/common/templates/extra/review-form.php:26
msgid "I found a plugin that better serves my needs"
msgstr "Ho trovato un plugin che risponde meglio alle mie esigenze"

#: src/view/admin/common/templates/extra/review-form.php:33
msgid "Missing features / did not meet my expectations"
msgstr "Funzionalità mancanti / non è all’altezza delle mie aspettative"

#: src/view/admin/common/templates/extra/review-form.php:40
msgid "I need more customization options"
msgstr "Ho bisogno di più opzioni di personalizzazione"

#: src/view/admin/common/templates/extra/review-form.php:47
msgid "The premium plan is too expensive"
msgstr "Il piano premium è troppo caro"

#: src/view/admin/common/templates/extra/review-form.php:54
msgid "I’m only deactivating the plugin temporarily"
msgstr "Sto disattivando il plugin temporaneamente"

#: src/view/admin/common/templates/extra/review-form.php:61
msgid "Other"
msgstr "Altro"

#: src/view/admin/common/templates/extra/review-form.php:65
msgid "Please specify here"
msgstr "Specifica qui"

#: src/lib/Cookiebot_Review.php:105
msgid "(Optional)"
msgstr "(Opzionale)"

#: src/lib/Cookiebot_Review.php:106
msgid " By checking this box, you agree to submit troubleshooting information and allow us to contact you regarding the problem if necessary."
msgstr " Selezionando questa casella, l’utente accetta di inviare le informazioni per la risoluzione del problema ed acconsente ad essere contattato a questo scopo, se necessario."

#: src/lib/Cookiebot_Review.php:110
msgid "The information will be kept for no longer than 90 days. You may revoke this consent at any time, e.g. by sending an email to "
msgstr "Le informazioni saranno conservate per un periodo non superiore a 90 giorni. È possibile revocare il consenso in qualsiasi momento, ad esempio inviando un’e-mail a "

#: src/lib/Cookiebot_Review.php:117
msgid "Skip and Deactivate"
msgstr "Salta e disattiva"

#: src/lib/Cookiebot_Review.php:118
msgid "Submit and Deactivate"
msgstr "Invia e disattiva"

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:6
msgid ""
"If there is a network settings ID connected it will be used for this subsite, "
"if not you will need to add a new ID before updating other settings"
msgstr ""
"Se esiste un ID account di rete collegato, verrà utilizzato per questo "
"sottosito; in caso contrario, sarà necessario aggiungere un nuovo ID prima "
"di aggiornare le altre impostazioni"

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:13
msgid "Disconnect subsite account"
msgstr "Disconnere l’account del sottosito"

#: src/view/admin/common/templates/header.php:10
msgid "Changes has been saved"
msgstr "La modifiche sono state salvate"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:13
msgid "Absolutely, you deserve it!"
msgstr "Assolutamente, ve lo meritate!"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:24
msgid "Maybe later?"
msgstr "Magari più tardi?"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:35
msgid "I`ve already left my feedback"
msgstr "Ho già lasciato il mio feedback"

#: src/view/admin/uc_frame/dashboard-page.php:30
msgid "Welcome to Usercentrics Cookiebot WordPress Plugin"
msgstr "Benvenuti al Plugin Usercentrics Cookiebot per WordPress"

#: src/view/admin/uc_frame/dashboard-page.php:38
msgid ""
"You’ve added your settings ID to your Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Hai aggiunto il tuo ID account al plugin Usercentrics Cookiebot per "
"WordPress."

#: src/view/admin/uc_frame/dashboard-page.php:51
msgid ""
"Are you happy with Usercentrics Cookiebot WordPress Plugin? Your feedback "
"helps us improve it."
msgstr ""
"Sei soddisfatto del Plugin Usercentrics Cookiebot per WordPress? Il tuo "
"feedback ci aiuta a migliorare."

#: src/view/admin/uc_frame/dashboard-page.php:55
msgid "Share feedback"
msgstr "Condividi il tuo feedback"

#: src/view/admin/uc_frame/dashboard-page.php:68
msgid "How to set up Usercentrics Cookiebot WordPress Plugin"
msgstr "Come configurare il plugin Usercentrics Cookiebot per WordPress"

#: src/view/admin/uc_frame/dashboard-page.php:72
#: src/view/admin/uc_frame/settings/gcm-page.php:21
#: src/view/admin/uc_frame/settings/general-page.php:127
#: src/view/admin/uc_frame/settings/gtm-page.php:18
msgid "Learn more"
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:115
msgid "Visit our blog to learn about other legislations"
msgstr "Visita il nostro blog per ottenere informazioni sulle altre normative"

#: src/view/admin/uc_frame/debug-page.php:22
msgid "Debug your plugin"
msgstr "Effettua il debug del plugin"

#: src/view/admin/uc_frame/debug-page.php:25
msgid ""
"If you encounter any issues with your Usercentrics Cookiebot WordPress "
"Plugin, provide the information below to help us assist you. Visit our "
"Support Center and send us a copy of what is displayed in the window below."
msgstr ""
"Se hai riscontrato dei problemi con il plugin Usercentrics Cookiebot per "
"WordPress e hai bisogno di assistenza inviaci le informazioni riportate di "
"seguito. Visita il nostro Centro di assistenza e inviaci una copia di quanto "
"visualizzato nella finestra sottostante."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:23
msgid "WP Consent API Settings"
msgstr "Impostazioni dell’API di consenso WP"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:32
msgid ""
"WP Consent API and Usercentrics Cookiebot WordPress Plugin categorize "
"cookies a bit differently. The default settings should fit most needs, but "
"if you need to change the mapping you can do so below."
msgstr ""
"L’API di consenso WP e il plugin Usercentrics Cookiebot per WordPress "
"classificano i cookie in modo leggermente diverso. Le impostazioni "
"predefinite dovrebbero rispondere alle esigenze della maggior parte degli "
"utenti, ma se hai bisogno di modificarle puoi farlo di seguito."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:48
msgid "Usercentrics Cookiebot cookie categories"
msgstr "Categorie di cookie Usercentrics Cookiebot"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:58
msgid "essential"
msgstr "Essenziali"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:72
msgid "functional"
msgstr "Funzionale"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:106
msgid "Reset to default categories"
msgstr "Ripristino delle categorie predefinite"

#: src/view/admin/uc_frame/settings-page.php:48
msgid "Save changes"
msgstr "Salvare le modifiche"

#: src/view/admin/uc_frame/settings-page.php:70
#: src/view/admin/uc_frame/settings/embeddings-page.php:4
msgid "Privacy Policy Sync"
msgstr "Sincronizzazione dell’Informativa sulla privacy"

#: src/view/admin/uc_frame/settings/embeddings-page.php:7
msgid ""
"Use our pre-defined, automatically generated embeddings to help you keep "
"your Privacy Policy page in sync with your consent banner settings. This "
"feature saves you time by automatically updating legally required "
"information, so you don’t need to manually copy data into your Privacy "
"Policy page. Once you’re done setting the options below, simply copy the "
"code and paste it into your Privacy Policy page."
msgstr ""
"Utilizza i nostri elementi predefiniti e generati automaticamente per "
"mantenere la pagina dell’Informativa sulla privacy sincronizzata con le "
"impostazioni del banner di consenso. Questa funzionalità consente di "
"risparmiare tempo aggiornando automaticamente le informazioni richieste "
"dalle normative, senza dover copiare manualmente i dati nella pagina "
"dell’Informativa sulla privacy. Una volta configurate le opzioni "
"sottostanti, è sufficiente copiare il codice e incollarlo nella pagina "
"dell’Informativa sulla privacy."

#: src/view/admin/uc_frame/settings/embeddings-page.php:12
msgid "Copy shortcode"
msgstr "Copiare lo shortcode"

#: src/view/admin/uc_frame/settings/embeddings-page.php:23
msgid "Sync options for privacy legislations"
msgstr "Opzioni di sincronizzazione in base alle normative sulla privacy"

#: src/view/admin/uc_frame/settings/embeddings-page.php:26
msgid ""
"Select the legislation you want to automatically sync with your Privacy "
"Policy page."
msgstr ""
"Seleziona la normativa da sincronizzare automaticamente con la pagina "
"dell’Informativa sulla Privacy"

#: src/view/admin/uc_frame/settings/embeddings-page.php:41
msgid "Sync options for data processing services (DPS) "
msgstr ""
"Opzioni di sincronizzazione per i Servizi di elaborazione dei dati (DPS)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:44
msgid ""
"Define what to include on your Privacy Policy page: DPS categories only, "
"categories with their services, a single service, or detailed information on "
"both categories and services. Choose based on the level of detail you want "
"to display."
msgstr ""
"Definisci cosa inserire nella pagina dell’Informativa sulla privacy: se solo "
"le categorie DPS, le categorie con i relativi servizi, i singoli servizi o "
"informazioni dettagliate sia sulle categorie che sui servizi. Scegli in base "
"al livello di dettaglio che desideri mostrare."

#: src/view/admin/uc_frame/settings/embeddings-page.php:50
msgid "Services (Default)"
msgstr "Servizi (default)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:51
msgid "Categories and services"
msgstr "Categorie e servizi"

#: src/view/admin/uc_frame/settings/embeddings-page.php:52
msgid "Categories only"
msgstr "Solo le categorie"

#: src/view/admin/uc_frame/settings/embeddings-page.php:53
msgid "Single service"
msgstr "Singoli servizi"

#: src/view/admin/uc_frame/settings/embeddings-page.php:54
msgid "Purposes"
msgstr "Obiettivi"

#: src/view/admin/uc_frame/settings/embeddings-page.php:55
msgid "Vendors"
msgstr "Fornitori"

#: src/view/admin/uc_frame/settings/embeddings-page.php:63
msgid "Single Service ID"
msgstr "ID del singolo servizio"

#: src/view/admin/uc_frame/settings/embeddings-page.php:66
msgid "Add the service ID that you want to display."
msgstr "Inserisci l’ID del servizio che vuoi mostrare."

#: src/view/admin/uc_frame/settings/embeddings-page.php:69
msgid "This feature is required."
msgstr "Questa funzionalità è obbligatoria."

#: src/view/admin/uc_frame/settings/embeddings-page.php:81
msgid "Privacy toggles"
msgstr "Toogle per la privacy"

#: src/view/admin/uc_frame/settings/embeddings-page.php:84
msgid ""
"Define whether you want the privacy toggles to be enabled and displayed on "
"your Privacy Policy page."
msgstr ""
"Decidi se vuoi che i toogle per la privacy vengano abilitati e visualizzati "
"nella pagina dell’Informativa sulla privacy."

#: src/view/admin/uc_frame/settings/embeddings-page.php:92
msgid "Enable privacy toggles"
msgstr "Attivare i toogle per la privacy"

#: src/view/admin/uc_frame/settings/gcm-page.php:14
msgid "Integration with Google Consent Mode"
msgstr "Integrazione con Google Consent Mode"

#: src/view/admin/uc_frame/settings/gcm-page.php:17
msgid ""
"The Usercentrics Cookiebot WordPress Plugin and Google Consent Mode "
"integrate seamlessly, providing plug-and-play privacy compliance and "
"effortless use of all Google services in one solution."
msgstr ""
"Il plugin Usercentrics Cookiebot per WordPress e Google Consent Mode si "
"integrano perfettamente, consentendo la conformità alla privacy e "
"permettendo l’utilizzo di tutti i servizi di Google in un’unica soluzione."

#: src/view/admin/uc_frame/settings/gcm-page.php:31
msgid ""
"Enable Google Consent Mode integration within your Usercentrics Cookiebot "
"WordPress Plugin."
msgstr ""
"Abilita l’integrazione di Google Consent Mode nel plugin Usercentrics "
"Cookiebot per WordPress."

#: src/view/admin/uc_frame/settings/general-page.php:22
msgid "Settings ID"
msgstr "ID dell’account"

#: src/view/admin/uc_frame/settings/general-page.php:25
msgid ""
"To disconnect your account, enter your settings ID into the field and confirm "
"with the button."
msgstr ""
"Per disconnettere il tuo account, inserisci l’ID dell’account nel campo e "
"clicca su Conferma."

#: src/view/admin/uc_frame/settings/general-page.php:99
msgid "TCF integration"
msgstr "Integrazione TCF"

#: src/view/admin/uc_frame/settings/general-page.php:102
msgid "Enable the integration with the latest version of IAB TCF."
msgstr "Attiva l’integrazione con l’ultima versione di IAB TCF."

#: src/view/admin/uc_frame/settings/general-page.php:111
msgid "IAB TCF integration"
msgstr "Integrazione IAB TCF"

#: src/view/admin/uc_frame/settings/gtm-page.php:14
msgid ""
"Enable Google Tag Manager integration to streamline tracking tags with your "
"Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Attiva l’integrazione con Google Tag Manager per semplificare il "
"tracciamento dei tag con il plugin Usercentrics Cookiebot per WordPress."

#: src/view/admin/uc_frame/settings/gtm-page.php:37
msgid "Enter your Google Tag Manager ID for seamless integration."
msgstr "Inserisci l’ID di Google Tag Manager per un’integrazione ottimale."

#: src/view/admin/uc_frame/settings/gtm-page.php:48
msgid "GTM-XXXXXXX"
msgstr "GTM-XXXXXXX"

#: src/view/admin/uc_frame/settings/gtm-page.php:56
msgid "Data layer name (only if changed)"
msgstr "Nome del data layer (livello dei dati, solo se modificato)"

#: src/view/admin/uc_frame/settings/gtm-page.php:58
msgid ""
"The default name for the data layer in Google Tag Manager is ‘dataLayer’. If "
"you renamed it, enter the new name. Otherwise, leave this field empty."
msgstr ""
"Il nome predefinito del livello dati in Google Tag Manager è ‘dataLayer’. Se "
"è stato rinominato, inserire il nuovo nome. Altrimenti, lasciare questo "
"campo vuoto."

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:14
msgid "Title"
msgstr "Titolo"

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:33
msgid "- Default -"
msgstr "- Predefinito -"

#: src/widgets/Cookiebot_Declaration_Widget.php:17
msgid "Cookiebot - Cookie Declaration"
msgstr "Cookiebot - Dichiarazione dei cookie"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:23
msgid "Cookiebot Status"
msgstr "Stato dei Cookiebot"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:39
msgid "You need to enter your Cookiebot ID."
msgstr "Devi inserire il tuo ID Cookiebot."

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:41
msgid "Update your Cookiebot ID"
msgstr "Aggiorna il tuo ID Cookiebot"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:44
msgid "Your Cookiebot is working!"
msgstr "Il tuo Cookiebot funziona!"

#~ msgid ""
#~ "We hope you enjoy using WordPress Cookiebot! Would you consider leaving "
#~ "us a review on WordPress.org?"
#~ msgstr ""
#~ "Ci auguriamo che usare WordPress Cookiebot™ ti sia piaciuto! Ti andrebbe "
#~ "di lasciarci una recensione su Wordpress.org?"

#~ msgid "Legislations"
#~ msgstr "Normative"

#~ msgid "Sure! I'd love to!"
#~ msgstr "Certo! Mi piacerebbe molto!"

#~ msgid "I've already left a review"
#~ msgstr "Ho già lasciato una recensione"

#~ msgid "Maybe Later"
#~ msgstr "Forse più tardi"

#~ msgid "Never show again"
#~ msgstr "Non mostrare più"

#~ msgid "TCF version:"
#~ msgstr "Versione TCF"

#~ msgid ""
#~ "In May 2023 The Interactive Advertising Bureau (IAB) announced the latest "
#~ "version of its Transparency and Consent Framework (TCF), or TCF v2.2, "
#~ "which must be implemented by all consent management platforms (CMPs) by "
#~ "November 20, 2023. We will migrate you automatically on November 20,2023, "
#~ "but we recommend to do it manually before. To manually switch the version "
#~ "before please select it on the right."
#~ msgstr ""
#~ "A maggio 2023 l'Interactive Advertising Bureau (IAB) ha annunciato "
#~ "l'ultima versione del suo Transparency and Consent Framework (TCF), o TCF "
#~ "v2.2, che dovrà essere implementato da tutte le piattaforme di gestione "
#~ "del consenso (CMP) entro il 20 novembre 2023. Effettueremo "
#~ "automaticamente la migrazione per te il 20 novembre 2023, ma ti "
#~ "consigliamo di effettuarla manualmente prima. Per passare manualmente "
#~ "alla nuova versione, selezionala sulla destra."

#~ msgid "Select the TCF Version below"
#~ msgstr "Seleziona la versione del TCF qui sotto"

#~ msgid "New"
#~ msgstr "Nuova"

#~ msgid "Create a new Account"
#~ msgstr "Crea un nuovo account"

#~ msgid "Get help with connecting your account"
#~ msgstr "Ottieni assistenza per connettere il tuo account"

#~ msgid "Select the Cookie-blocking mode"
#~ msgstr "Seleziona la modalità di blocco dei cookie"

#~ msgid "Automatic cookie-blocking mode"
#~ msgstr "Blocco automatico dei cookie"

#~ msgid "Manual cookie-blocking mode"
#~ msgstr "Blocco manuale dei cookie"

#~ msgid "Depending on Cookie-blocking mode"
#~ msgstr "In base alla modalità di blocco dei cookie"

#~ msgid "Auto-update Cookiebot™ Plugin:"
#~ msgstr "Aggiorna automaticamente il plugin Cookiebot™:"

#~ msgid ""
#~ "Automatically update your Cookiebot™ plugin when new releases becomes "
#~ "available."
#~ msgstr ""
#~ "Aggiorna automaticamente il plugin Cookiebot™ quando sono disponibili "
#~ "nuove versioni."

#~ msgid ""
#~ "These add-ons are produced by an open-source community of developers. "
#~ "This is done to help make it easier for WordPress users to implement "
#~ "‘prior consent’ for cookies and trackers set by plugins that do not offer "
#~ "this as a built-in function. The add-ons are currently the best "
#~ "alternative to a WordPress Core framework that can signal the user’s "
#~ "consent state to other plugins (if and when this will be implemented is "
#~ "unknown) and to those plugins that do not yet offer native support for "
#~ "Cookiebot CMP. "
#~ msgstr ""
#~ "Questi componenti aggiuntivi sono prodotti da una comunità di "
#~ "sviluppatori open-source. L’obiettivo è quello di facilitare agli utenti "
#~ "di WordPress l’implementazione del “consenso preventivo” per i cookie e i "
#~ "tracker impostati dai plugin che non lo offrono come funzione integrata. "
#~ "I componenti aggiuntivi sono attualmente la migliore alternativa a un "
#~ "framework WordPress Core in grado di segnalare lo stato di consenso "
#~ "dell’utente ad altri plugin (non si sa se e quando verrà implementato) e "
#~ "a quei plugin che non offrono ancora un supporto nativo per Cookiebot "
#~ "CMP. "

#~ msgid "Do you not have an account yet?"
#~ msgstr "Non possiedi ancora un account?"

#~ msgid ""
#~ "Before you can get started with Cookiebot CMP for WordPress, you need to "
#~ "create an account on our website by clicking on \"Create a new account\" "
#~ "below. After you have signed up, you can configure your banner in the "
#~ "Cookiebot Manager and then place the Cookiebot Domain Group ID in the "
#~ "designated field below. You can find your ID in the Cookiebot Manager by "
#~ "navigating to \"Settings\" and \"Your Scripts\"."
#~ msgstr ""
#~ "Prima di iniziare ad utilizzare Cookiebot CMP per WordPress, è necessario "
#~ "creare un account sul nostro sito web facendo clic su “Crea un nuovo "
#~ "account” qui sotto. Dopo aver effettuato l’iscrizione, puoi configurare "
#~ "il tuo banner nel Cookiebot Manager e inserire l’ID del gruppo di dominio "
#~ "Cookiebot nel campo designato. Puoi trovare il tuo ID nel Cookiebot "
#~ "Manager navigando su “Impostazioni” e “I tuoi script”."

#~ msgid "Depending on cookie-blocking mode"
#~ msgstr "In base alla modalità di blocco dei cookie"

#~ msgid "Cookiebot CMP in WP Admin:"
#~ msgstr "Cookiebot CMP nel WP Admin:"

#~ msgid ""
#~ "This checkbox will disable Cookiebot CMP to act within the WordPress "
#~ "Admin area"
#~ msgstr ""
#~ "Selezionare questa casella impedirà a Cookiebot CMP di agire all’interno "
#~ "dell’area amministrativa WordPress"

#~ msgid "Disable Cookiebot CMP in the WordPress Admin area"
#~ msgstr "Disattiva Cookiebot CMP nell’area amministrativa Wordpress"

#~ msgid "Cookiebot CMP on front-end while logged in:"
#~ msgstr "Cookiebot CMP sulla tua interfaccia mentre sei connesso:"

#~ msgid ""
#~ "This setting will enable Cookiebot CMP on the front-end while you're "
#~ "logged in."
#~ msgstr ""
#~ "Questa impostazione abiliterà Cookiebot CMP sull’interfaccia mentre sei "
#~ "connesso."

#~ msgid "Render Cookiebot CMP on front-end while logged in"
#~ msgstr "Attiva Cookiebot CMP sull’interfaccia mentre sei connesso"

#~ msgid "Watch video demonstration"
#~ msgstr "Guarda un video dimostrativo"
