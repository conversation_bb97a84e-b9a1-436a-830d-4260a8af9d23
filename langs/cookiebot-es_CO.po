msgid ""
msgstr ""
"Project-Id-Version: Cookiebot | GDPR/CCPA Compliant <PERSON><PERSON> and "
"Control\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/cookiebot\n"
"POT-Creation-Date: 2024-12-18T18:13:49+00:00\n"
"PO-Revision-Date: 2025-01-13 11:58+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: es_CO\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: cookiebot.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"

#. Plugin Name of the plugin
#: cookiebot.php
msgid "Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: cookiebot.php
msgid "https://www.cookiebot.com/"
msgstr ""

#. Description of the plugin
#: cookiebot.php
msgid ""
"The Cookiebot CMP WordPress cookie banner and cookie policy help you comply "
"with the major data protection laws (GDPR, ePrivacy, CCPA, LGPD, etc.) in a "
"simple and fully automated way. Secure your website and get peace of mind."
msgstr ""

#. Author of the plugin
#: cookiebot.php
msgid "Usercentrics A/S"
msgstr ""

#: src/addons/config/Settings_Config.php:89
#: src/addons/config/Settings_Config.php:90
#: src/view/admin/common/prior-consent/page.php:24
#: src/view/admin/common/templates/main-tabs.php:39
msgid "Plugins"
msgstr "Plugins"

#: src/addons/config/Settings_Config.php:130
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:25
msgid "Remove language"
msgstr "Eliminar idioma"

#: src/addons/config/Settings_Config.php:602
msgid "The plugin is not installed."
msgstr "El plugin no está instalado."

#: src/addons/config/Settings_Config.php:605
msgid "The theme is not installed."
msgstr "El tema no está instalado."

#: src/addons/config/Settings_Config.php:609
msgid "The plugin is not activated."
msgstr "El plugin no está activo."

#: src/addons/config/Settings_Config.php:612
msgid "The theme is not activated."
msgstr "El tema no está activo."

#: src/addons/config/Settings_Config.php:672
#: src/lib/Cookiebot_Admin_Links.php:54 src/lib/Cookiebot_Admin_Links.php:56
#: src/lib/Cookiebot_Admin_Links.php:124
msgid "%s"
msgstr "%s"

#: src/addons/config/Settings_Config.php:708
#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:18
#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:19
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:40
msgid "Info"
msgstr "Información"

#: src/addons/config/Settings_Config.php:715
msgid "Available Add-ons"
msgstr "Add-on disponbles"

#: src/addons/config/Settings_Config.php:721
msgid "Unavailable Add-ons"
msgstr "Add-on no disponibles"

#: src/addons/config/Settings_Config.php:738
msgid "Jetpack"
msgstr "Jetpack"

#: src/addons/config/Settings_Config.php:749
msgid "WP Consent API"
msgstr "API de consentimiento para WP"

#: src/addons/controller/addons/add_to_any/Add_To_Any.php:86
#: src/addons/controller/addons/embed_autocorrect/Embed_Autocorrect.php:443
msgid "Blocks embedded videos from Youtube, Twitter, Vimeo and Facebook."
msgstr "Bloquea los vídeos incrustados de YouTube, Twitter, Vimeo y Facebook."

#: src/addons/controller/addons/enfold/Enfold.php:29
msgid "Blocks cookies created by Enfold theme's Google Services."
msgstr ""
"Bloquea las cookies creadas por los Google Services del tema de Enfold."

#: src/addons/controller/addons/enhanced_ecommerce_for_woocommerce_store/Enhanced_Ecommerce_For_WooCommerce_Store.php:43
msgid "Blocks enhanced e-commerce for WooCommerce store"
msgstr "Bloquea el comercio electrónico mejorado para la tienda de WooCommerce"

#: src/addons/controller/addons/google_analytics/Google_Analytics.php:36
msgid ""
"Google Analytics is used to track how visitor interact with website content."
msgstr ""
"Google Analytics se usa para seguir como el visitante interacciona con el "
"contenido del sitio web."

#: src/addons/controller/addons/google_analytics_plus/Google_Analytics_Plus.php:32
msgid ""
"Google Analytics is a simple, easy-to-use tool that helps website owners "
"measure how users interact with website content"
msgstr ""
"Google Analytics es una herramienta simple y fácil de usar que ayuda a los "
"dueños de un sitio web a medir cómo los usuarios interactúan con el "
"contenido de la página web"

#: src/addons/controller/addons/google_site_kit/Google_Site_Kit.php:33
msgid "Blocks Google Analytics scripts"
msgstr "Bloquea los scripts de Google Analytics"

#: src/addons/controller/addons/jetpack/widget/Facebook_Jetpack_Widget.php:71
msgid "Facebook widget."
msgstr "Widget de Facebook."

#: src/addons/controller/addons/litespeed_cache/Litespeed_Cache.php:51
msgid ""
"Excludes cookiebot javascript files when the Litespeed Cache deter option is "
"enabled."
msgstr ""
"Excluye los archivos javascript de Cookiebot cuando la opción defer de "
"Litespeed Cache está habilitada."

#: src/addons/controller/addons/official_facebook_pixel/Official_Facebook_Pixel.php:247
msgid "Blocks Official Meta Pixel scripts"
msgstr "Bloquea los scripts oficiales de Meta Pixel"

#: src/addons/controller/addons/optinmonster/Optinmonster.php:32
msgid ""
"OptinMonster API plugin to connect your WordPress site to your OptinMonster "
"account."
msgstr ""
"El plugin API de OptinMonster para conectar tu sitio creado con WordPress a "
"tu cuenta de OptinMonster."

#: src/addons/controller/addons/simple_share_buttons_adder/Simple_Share_Buttons_Adder.php:32
msgid "Blocks Simple Share Buttons Adder."
msgstr "Bloquea Simple Share Buttons Adder."

#: src/addons/controller/addons/wpforms/Wpforms.php:61
msgid ""
"If the user gives correct consent, IP and Unique User ID will be saved on "
"form submissions, otherwise not."
msgstr ""
"En caso de que el usuario dé un consentimiento correcto, se almacenarán la "
"dirección IP y el ID del usuario en formularios de envío, si no, no."

#: src/addons/controller/addons/wpforms/Wpforms.php:62
msgid "Increases opt-in rate compared to WPForms \"GDPR mode\"."
msgstr "Mejora la tasa de aceptación en comparación al “GDPR mode” de WPForms."

#: src/addons/controller/addons/wp_google_analytics_events/Wp_Google_Analytics_Events.php:46
msgid ""
"The plugin allows you to fire events whenever someone interacts or views "
"elements on your website."
msgstr ""
"El plugin te permite ejecutar eventos cuando alguien visualiza o interactúa "
"con elementos en tu sitio web."

#: src/addons/controller/addons/wp_rocket/Wp_Rocket.php:48
msgid ""
"Excludes cookiebot javascript files when the WP-Rocket deter option is "
"enabled."
msgstr ""
"Excluye los archivos javascript de Cookiebot cuando la opción WP-Rocket "
"defer está activada."

#: src/addons/controller/addons/wp_seopress/Wp_Seopress.php:41
msgid "Blocks cookies from WP SEOPress' Google Analytics integration."
msgstr "Bloquea las cookies de la integración Google Analytics de WP SEOPress."

#: src/addons/controller/Plugin_Controller.php:50
msgid "You enabled Cookiebot™ auto blocking mode but still using addons"
msgstr ""
"Has activado el modo de bloqueo automático Cookiebot™, pero sigues usando "
"addons"

#: src/addons/controller/Plugin_Controller.php:55
msgid ""
"In some occasions this may cause client side errors. If you notice any "
"errors please try to disable Cookiebot™ addons or contact Cookiebot™ support."
msgstr ""
"A veces, esto puede causar errores por parte del cliente. Si notas algún "
"error, por favor, intenta desactivar los add-ons de Cookiebot™ o contacta "
"con Atención al Cliente de Cookiebot™."

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:84
msgid "Share your experience"
msgstr "Comparte tu experiencia"

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:88
msgid ""
"Hi there! We are thrilled you love the Cookiebot CMP plugin. Could you do us "
"a huge favor and leave a 5-star rating on WordPress? Your support will help "
"us spread the word and empower more WordPress websites to meet GDPR and CCPA "
"compliance standards effortlessly. Thank you for your support!"
msgstr ""
"¡Hola! Es increíble lo que has conseguido con el plugin de Cookiebot CMP. "
"¿Nos podrías echar una mano y dejarnos una reseña de 5 estrellas en "
"WordPress? Tu apoyo nos ayuadará a seguir promocionando la herramienta y "
"permitir a más webs en WordPress a cumplir con el RGPD y la CCPA sin "
"esfuerzo. ¡Gracias por tu apoyo!"

#: src/admin_notices/Cookiebot_Temp_Notice.php:43
msgid ""
"Cookiebot CMP Plugin will soon no longer support PHP 5. If your website "
"still runs on this version we recommend upgrading so you can continue "
"enjoying the features Cookiebot CMP offers."
msgstr ""
"El plugin de Cookiebot CMP pronto dejará de ser compatible con PHP 5. Si tu "
"sitio web todavía opera con esta versión, te recomendamos que lo actualices "
"para que puedas seguir disfrutando de las características que ofrece "
"Cookiebot CMP."

#: src/lib/Cookiebot_Review.php:82
msgid "Sorry you are not allowed to do this."
msgstr "Lo sentimos, esta acción no está permitida."

#: src/lib/Cookiebot_Review.php:85
#: src/view/admin/common/templates/extra/review-form.php:94
msgid "Please select one option"
msgstr "Selecciona una opción"

#. translators: The placeholder is for the COOKIEBOT_MIN_PHP_VERSION constant
#: src/lib/Cookiebot_WP.php:66
msgid "The Cookiebot plugin requires PHP version %s or greater."
msgstr "El plugin Cookiebot requiere la versión de PHP %s o superior."

#: src/lib/Cookiebot_WP.php:242 src/settings/pages/Dashboard_Page.php:35
#: src/view/admin/common/templates/main-tabs.php:21
msgid "Dashboard"
msgstr "Escritorio"

#: src/lib/helper.php:245 src/lib/helper.php:343
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:91
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:167
#: src/view/admin/cb_frame/settings/gcm-page.php:126
#: src/view/admin/cb_frame/settings/gtm-page.php:113
#: src/view/admin/common/prior-consent/available-addons/tab.php:55
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:88
msgid "marketing"
msgstr "marketing"

#: src/lib/helper.php:248 src/lib/helper.php:342
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:88
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:134
#: src/view/admin/cb_frame/settings/gcm-page.php:118
#: src/view/admin/cb_frame/settings/gtm-page.php:105
#: src/view/admin/common/prior-consent/available-addons/tab.php:46
msgid "statistics"
msgstr "estadísticas"

#: src/lib/helper.php:251 src/lib/helper.php:341
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:85
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:118
#: src/view/admin/cb_frame/settings/gcm-page.php:110
#: src/view/admin/cb_frame/settings/gtm-page.php:97
#: src/view/admin/common/prior-consent/available-addons/tab.php:37
msgid "preferences"
msgstr "preferencias"

#: src/lib/helper.php:254
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:82
msgid "necessary"
msgstr "necesarias"

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:367
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent]."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:372
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"tracking."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar el seguimiento."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:377
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Social Share buttons."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar los botones de compartir en redes sociales."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:382
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to view "
"this element."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para ver este elemento."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:387
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to watch "
"this video."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para ver este vídeo."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:392
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Services."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar los Servicios de Google."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:397
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"facebook shopping feature."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar la herramienta de compras de Facebook."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:402
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to track "
"for google analytics."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para rastrear con en Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:407
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Analytics."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:412
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"instagram feed."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar el feed de Instagram."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:417
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Facebook Pixel."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar Facebook Pixel."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:422
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to Social "
"Share buttons."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para los botones de compartir en redes sociales."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:427
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to allow "
"Matomo statistics."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para permitir estadísticas de Matomo."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:432
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"saving user information."
msgstr ""
"Por favor, acepta las cookies de [renew_consent]%cookie_types[/"
"renew_consent] para habilitar el guardar la información de usuario."

#: src/lib/Supported_Languages.php:8
msgid "Norwegian Bokmål"
msgstr "Noruego Bokmål"

#: src/lib/Supported_Languages.php:9
msgid "Turkish"
msgstr "Turco"

#: src/lib/Supported_Languages.php:10
msgid "German"
msgstr "Alemán"

#: src/lib/Supported_Languages.php:11
msgid "Czech"
msgstr "Checo"

#: src/lib/Supported_Languages.php:12
msgid "Danish"
msgstr "Danés"

#: src/lib/Supported_Languages.php:13
msgid "Albanian"
msgstr "Albano"

#: src/lib/Supported_Languages.php:14
msgid "Hebrew"
msgstr "Hebreo"

#: src/lib/Supported_Languages.php:15
msgid "Korean"
msgstr "Coreano"

#: src/lib/Supported_Languages.php:16
msgid "Italian"
msgstr "Italiano"

#: src/lib/Supported_Languages.php:17
msgid "Dutch"
msgstr "Neerlandés"

#: src/lib/Supported_Languages.php:18
msgid "Vietnamese"
msgstr "Vietnamita"

#: src/lib/Supported_Languages.php:19
msgid "Tamil"
msgstr "Tamil"

#: src/lib/Supported_Languages.php:20
msgid "Icelandic"
msgstr "Islandés"

#: src/lib/Supported_Languages.php:21
msgid "Romanian"
msgstr "Rumano"

#: src/lib/Supported_Languages.php:22
msgid "Sinhala"
msgstr "Cingalés"

#: src/lib/Supported_Languages.php:23
msgid "Catalan"
msgstr "Catalán"

#: src/lib/Supported_Languages.php:24
msgid "Bulgarian"
msgstr "Búlgaro"

#: src/lib/Supported_Languages.php:25
msgid "Ukrainian"
msgstr "Ucraniano"

#: src/lib/Supported_Languages.php:26
msgid "Chinese"
msgstr "Chino"

#: src/lib/Supported_Languages.php:27
msgid "English"
msgstr "Inglés"

#: src/lib/Supported_Languages.php:28
msgid "Arabic"
msgstr "Árabe"

#: src/lib/Supported_Languages.php:29
msgid "Croatian"
msgstr "Croata"

#: src/lib/Supported_Languages.php:30
msgid "Thai"
msgstr "Tailandés"

#: src/lib/Supported_Languages.php:31
msgid "Greek"
msgstr "Griego"

#: src/lib/Supported_Languages.php:32
msgid "Lithuanian"
msgstr "Lituano"

#: src/lib/Supported_Languages.php:33
msgid "Polish"
msgstr "Polaco"

#: src/lib/Supported_Languages.php:34
msgid "Latvian"
msgstr "Letón"

#: src/lib/Supported_Languages.php:35
msgid "French"
msgstr "Francés"

#: src/lib/Supported_Languages.php:36
msgid "Indonesian"
msgstr "Indonesio"

#: src/lib/Supported_Languages.php:37
msgid "Macedonian"
msgstr "Macedonio"

#: src/lib/Supported_Languages.php:38
msgid "Estonian"
msgstr "Estonio"

#: src/lib/Supported_Languages.php:39
msgid "Portuguese"
msgstr "Portugués"

#: src/lib/Supported_Languages.php:40
msgid "Irish"
msgstr "Irlandés"

#: src/lib/Supported_Languages.php:41
msgid "Malay"
msgstr "Malayo"

#: src/lib/Supported_Languages.php:42
msgid "Slovenian"
msgstr "Esloveno"

#: src/lib/Supported_Languages.php:43
msgid "Russian"
msgstr "Ruso"

#: src/lib/Supported_Languages.php:44
msgid "Japanese"
msgstr "Japonés"

#: src/lib/Supported_Languages.php:45
msgid "Hindi"
msgstr "Hindi"

#: src/lib/Supported_Languages.php:46
msgid "Slovak"
msgstr "Eslovaco"

#: src/lib/Supported_Languages.php:47
msgid "Spanish"
msgstr "Español"

#: src/lib/Supported_Languages.php:48
msgid "Swedish"
msgstr "Sueco"

#: src/lib/Supported_Languages.php:49
msgid "Serbian"
msgstr "Serbio"

#: src/lib/Supported_Languages.php:50
msgid "Finnish"
msgstr "Finés"

#: src/lib/Supported_Languages.php:51
msgid "Basque"
msgstr "Euskera"

#: src/lib/Supported_Languages.php:52
msgid "Hungarian"
msgstr "Húngaro"

#: src/lib/Supported_Regions.php:7
msgid "Afghanistan"
msgstr "Afganistán"

#: src/lib/Supported_Regions.php:8
msgid "Albania"
msgstr "Albania"

#: src/lib/Supported_Regions.php:9
msgid "Algeria"
msgstr "Argelia"

#: src/lib/Supported_Regions.php:10
msgid "American Samoa"
msgstr "Samoa Americana"

#: src/lib/Supported_Regions.php:11
msgid "Andorra"
msgstr "Andorra"

#: src/lib/Supported_Regions.php:12
msgid "Angola"
msgstr "Angola"

#: src/lib/Supported_Regions.php:13
msgid "Anguilla"
msgstr "Anguila"

#: src/lib/Supported_Regions.php:14
msgid "Antarctica"
msgstr "Antártida"

#: src/lib/Supported_Regions.php:15
msgid "Antigua and Barbuda"
msgstr "Antigua y Barbuda"

#: src/lib/Supported_Regions.php:16
msgid "Argentina"
msgstr "Argentina"

#: src/lib/Supported_Regions.php:17
msgid "Armenia"
msgstr "Armenia"

#: src/lib/Supported_Regions.php:18
msgid "Aruba"
msgstr "Aruba"

#: src/lib/Supported_Regions.php:19
msgid "Australia"
msgstr "Australia"

#: src/lib/Supported_Regions.php:20
msgid "Austria"
msgstr "Austria"

#: src/lib/Supported_Regions.php:21
msgid "Azerbaijan"
msgstr "Azerbaiyán"

#: src/lib/Supported_Regions.php:22
msgid "Bahamas"
msgstr "Bahamas"

#: src/lib/Supported_Regions.php:23
msgid "Bahrain"
msgstr "Baréin"

#: src/lib/Supported_Regions.php:24
msgid "Bangladesh"
msgstr "Bangladés"

#: src/lib/Supported_Regions.php:25
msgid "Barbados"
msgstr "Barbados"

#: src/lib/Supported_Regions.php:26
msgid "Belarus"
msgstr "Bielorrusia"

#: src/lib/Supported_Regions.php:27
msgid "Belgium"
msgstr "Bélgica"

#: src/lib/Supported_Regions.php:28
msgid "Belize"
msgstr "Belice"

#: src/lib/Supported_Regions.php:29
msgid "Benin"
msgstr "Benín"

#: src/lib/Supported_Regions.php:30
msgid "Bermuda"
msgstr "Bermudas"

#: src/lib/Supported_Regions.php:31
msgid "Bhutan"
msgstr "Bután"

#: src/lib/Supported_Regions.php:32
msgid "Bolivia"
msgstr "Bolivia"

#: src/lib/Supported_Regions.php:33
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, San Eustaquio y Saba"

#: src/lib/Supported_Regions.php:34
msgid "Bosnia and Herzegovina"
msgstr "Bosnia y Herzegovina"

#: src/lib/Supported_Regions.php:35
msgid "Botswana"
msgstr "Botsuana"

#: src/lib/Supported_Regions.php:36
msgid "Bouvet Island"
msgstr "Isla Bouvet"

#: src/lib/Supported_Regions.php:37
msgid "Brazil"
msgstr "Brasil"

#: src/lib/Supported_Regions.php:38
msgid "British Indian Ocean Territory"
msgstr "Territorio Británico del Océano Índico"

#: src/lib/Supported_Regions.php:39
msgid "Brunei "
msgstr "Brunéi"

#: src/lib/Supported_Regions.php:40
msgid "Bulgaria"
msgstr "Bulgaria"

#: src/lib/Supported_Regions.php:41
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: src/lib/Supported_Regions.php:42
msgid "Burundi"
msgstr "Burundi"

#: src/lib/Supported_Regions.php:43
msgid "Cambodia"
msgstr "Camboya"

#: src/lib/Supported_Regions.php:44
msgid "Cameroon"
msgstr "Camerún"

#: src/lib/Supported_Regions.php:45
msgid "Canada"
msgstr "Canadá"

#: src/lib/Supported_Regions.php:46
msgid "Cape Verde"
msgstr "Cabo Verde"

#: src/lib/Supported_Regions.php:47
msgid "Cayman Islands"
msgstr "Islas Caimán"

#: src/lib/Supported_Regions.php:48
msgid "Central African Republic"
msgstr "República Centroafricana"

#: src/lib/Supported_Regions.php:49
msgid "Chad"
msgstr "Chad"

#: src/lib/Supported_Regions.php:50
msgid "Chile"
msgstr "Chile"

#: src/lib/Supported_Regions.php:51
msgid "China"
msgstr "China"

#: src/lib/Supported_Regions.php:52
msgid "Christmas Island"
msgstr "Isla de Navidad"

#: src/lib/Supported_Regions.php:53
msgid "Cocos (Keeling) Islands"
msgstr "Isla del Coco"

#: src/lib/Supported_Regions.php:54
msgid "Colombia"
msgstr "Colombia"

#: src/lib/Supported_Regions.php:55
msgid "Comoros"
msgstr "Comoras"

#: src/lib/Supported_Regions.php:56
msgid "Congo"
msgstr "Congo"

#: src/lib/Supported_Regions.php:57
msgid "Congo, the Democratic Republic of the"
msgstr "República Democrática del Congo"

#: src/lib/Supported_Regions.php:58
msgid "Cook Islands"
msgstr "Islas Cook"

#: src/lib/Supported_Regions.php:59
msgid "Costa Rica"
msgstr "Costa Rica"

#: src/lib/Supported_Regions.php:60
msgid "Croatia"
msgstr "Croacia"

#: src/lib/Supported_Regions.php:61
msgid "Cuba"
msgstr "Cuba"

#: src/lib/Supported_Regions.php:62
msgid "Curaçao"
msgstr "Curazao"

#: src/lib/Supported_Regions.php:63
msgid "Cyprus"
msgstr "Chipre"

#: src/lib/Supported_Regions.php:64
msgid "Czech Republic"
msgstr "República Checa"

#: src/lib/Supported_Regions.php:65
msgid "Côte d'Ivoire"
msgstr "Costa de Marfil"

#: src/lib/Supported_Regions.php:66
msgid "Denmark"
msgstr "Dinamarca"

#: src/lib/Supported_Regions.php:67
msgid "Djibouti"
msgstr "Yibuti"

#: src/lib/Supported_Regions.php:68
msgid "Dominica"
msgstr "Dominica"

#: src/lib/Supported_Regions.php:69
msgid "Dominican Republic"
msgstr "República Dominicana"

#: src/lib/Supported_Regions.php:70
msgid "Ecuador"
msgstr "Ecuador"

#: src/lib/Supported_Regions.php:71
msgid "Egypt"
msgstr "Egipto"

#: src/lib/Supported_Regions.php:72
msgid "El Salvador"
msgstr "El Salvador"

#: src/lib/Supported_Regions.php:73
msgid "Equatorial Guinea"
msgstr "Guinea Ecuatorial"

#: src/lib/Supported_Regions.php:74
msgid "Eritrea"
msgstr "Eritrea"

#: src/lib/Supported_Regions.php:75
msgid "Estonia"
msgstr "Estonia"

#: src/lib/Supported_Regions.php:76
msgid "Ethiopia"
msgstr "Etiopía"

#: src/lib/Supported_Regions.php:77
msgid "Falkland Islands (Malvinas)"
msgstr "Islas Malvinas"

#: src/lib/Supported_Regions.php:78
msgid "Faroe Islands"
msgstr "Islas Feroe"

#: src/lib/Supported_Regions.php:79
msgid "Fiji"
msgstr "Fiyi"

#: src/lib/Supported_Regions.php:80
msgid "Finland"
msgstr "Finlandia"

#: src/lib/Supported_Regions.php:81
msgid "France"
msgstr "Francia"

#: src/lib/Supported_Regions.php:82
msgid "French Guiana"
msgstr "Guayana Francesa"

#: src/lib/Supported_Regions.php:83
msgid "French Polynesia"
msgstr "Polinesia Francesa"

#: src/lib/Supported_Regions.php:84
msgid "French Southern and Antarctic Lands"
msgstr "Tierras Australes y Antárticas Francesas"

#: src/lib/Supported_Regions.php:85
msgid "Gabon"
msgstr "Gabón"

#: src/lib/Supported_Regions.php:86
msgid "Gambia"
msgstr "Gambia"

#: src/lib/Supported_Regions.php:87
msgid "Georgia"
msgstr "Georgia"

#: src/lib/Supported_Regions.php:88
msgid "Germany"
msgstr "Alemania"

#: src/lib/Supported_Regions.php:89
msgid "Ghana"
msgstr "Ghana"

#: src/lib/Supported_Regions.php:90
msgid "Gibraltar"
msgstr "Gibraltar"

#: src/lib/Supported_Regions.php:91
msgid "Greece"
msgstr "Grecia"

#: src/lib/Supported_Regions.php:92
msgid "Greenland"
msgstr "Groenlandia"

#: src/lib/Supported_Regions.php:93
msgid "Grenada"
msgstr "Granada"

#: src/lib/Supported_Regions.php:94
msgid "Guadeloupe"
msgstr "Guadalupe"

#: src/lib/Supported_Regions.php:95
msgid "Guam"
msgstr "Guam"

#: src/lib/Supported_Regions.php:96
msgid "Guatemala"
msgstr "Guatemala"

#: src/lib/Supported_Regions.php:97
msgid "Guernsey"
msgstr "Guernsey"

#: src/lib/Supported_Regions.php:98
msgid "Guinea"
msgstr "Guinea"

#: src/lib/Supported_Regions.php:99
msgid "Guinea-Bissau"
msgstr "Guinea-Bisáu"

#: src/lib/Supported_Regions.php:100
msgid "Guyana"
msgstr "Guyana"

#: src/lib/Supported_Regions.php:101
msgid "Haiti"
msgstr "Haití"

#: src/lib/Supported_Regions.php:102
msgid "Heard Island and McDonald Islands"
msgstr "Territorio de las Islas Heard y McDonald"

#: src/lib/Supported_Regions.php:103
msgid "Holy See (Vatican City State)"
msgstr "Ciudad del Vaticano"

#: src/lib/Supported_Regions.php:104
msgid "Honduras"
msgstr "Honduras"

#: src/lib/Supported_Regions.php:105
msgid "Hong Kong"
msgstr "Hong Kong"

#: src/lib/Supported_Regions.php:106
msgid "Hungary"
msgstr "Hungría"

#: src/lib/Supported_Regions.php:107
msgid "Iceland"
msgstr "Islandia"

#: src/lib/Supported_Regions.php:108
msgid "India"
msgstr "India"

#: src/lib/Supported_Regions.php:109
msgid "Indonesia"
msgstr "Indonesia"

#: src/lib/Supported_Regions.php:110
msgid "Iran"
msgstr "Irán"

#: src/lib/Supported_Regions.php:111
msgid "Iraq"
msgstr "Irak"

#: src/lib/Supported_Regions.php:112
msgid "Ireland"
msgstr "Irlanda"

#: src/lib/Supported_Regions.php:113
msgid "Isle of Man"
msgstr "Isla de Man"

#: src/lib/Supported_Regions.php:114
msgid "Israel"
msgstr "Israel"

#: src/lib/Supported_Regions.php:115
msgid "Italy"
msgstr "Italia"

#: src/lib/Supported_Regions.php:116
msgid "Jamaica"
msgstr "Jamaica"

#: src/lib/Supported_Regions.php:117
msgid "Japan"
msgstr "Japón"

#: src/lib/Supported_Regions.php:118
msgid "Jersey"
msgstr "Jersey"

#: src/lib/Supported_Regions.php:119
msgid "Jordan"
msgstr "Jordania"

#: src/lib/Supported_Regions.php:120
msgid "Kazakhstan"
msgstr "Kazajistán"

#: src/lib/Supported_Regions.php:121
msgid "Kenya"
msgstr "Kenia"

#: src/lib/Supported_Regions.php:122
msgid "Kiribati"
msgstr "Kiribati"

#: src/lib/Supported_Regions.php:123
msgid "Kuwait"
msgstr "Kuwait"

#: src/lib/Supported_Regions.php:124
msgid "Kyrgyzstan"
msgstr "Kirguistán"

#: src/lib/Supported_Regions.php:125
msgid "Laos"
msgstr "Laos"

#: src/lib/Supported_Regions.php:126
msgid "Latvia"
msgstr "Letonia"

#: src/lib/Supported_Regions.php:127
msgid "Lebanon"
msgstr "Líbano"

#: src/lib/Supported_Regions.php:128
msgid "Lesotho"
msgstr "Lesoto"

#: src/lib/Supported_Regions.php:129
msgid "Liberia"
msgstr "Liberia"

#: src/lib/Supported_Regions.php:130
msgid "Libya"
msgstr "Libia"

#: src/lib/Supported_Regions.php:131
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: src/lib/Supported_Regions.php:132
msgid "Lithuania"
msgstr "Lituania"

#: src/lib/Supported_Regions.php:133
msgid "Luxembourg"
msgstr "Luxemburgo"

#: src/lib/Supported_Regions.php:134
msgid "Macao"
msgstr "Macao"

#: src/lib/Supported_Regions.php:135
msgid "North Macedonia"
msgstr "Macedonia del Norte"

#: src/lib/Supported_Regions.php:136
msgid "Madagascar"
msgstr "Madagascar"

#: src/lib/Supported_Regions.php:137
msgid "Malawi"
msgstr "Malaui"

#: src/lib/Supported_Regions.php:138
msgid "Malaysia"
msgstr "Malasia"

#: src/lib/Supported_Regions.php:139
msgid "Maldives"
msgstr "Maldivas"

#: src/lib/Supported_Regions.php:140
msgid "Mali"
msgstr "Mali"

#: src/lib/Supported_Regions.php:141
msgid "Malta"
msgstr "Malta"

#: src/lib/Supported_Regions.php:142
msgid "Marshall Islands"
msgstr "Islas Marshall"

#: src/lib/Supported_Regions.php:143
msgid "Martinique"
msgstr "Martinica"

#: src/lib/Supported_Regions.php:144
msgid "Mauritania"
msgstr "Mauritania"

#: src/lib/Supported_Regions.php:145
msgid "Mauritius"
msgstr "Mauricio"

#: src/lib/Supported_Regions.php:146
msgid "Mayotte"
msgstr "Mayotte"

#: src/lib/Supported_Regions.php:147
msgid "Mexico"
msgstr "México"

#: src/lib/Supported_Regions.php:148
msgid "Micronesia, Federated States of"
msgstr "Micronesia, Estados Federados de"

#: src/lib/Supported_Regions.php:149
msgid "Moldova"
msgstr "Moldavia"

#: src/lib/Supported_Regions.php:150
msgid "Monaco"
msgstr "Mónaco"

#: src/lib/Supported_Regions.php:151
msgid "Mongolia"
msgstr "Mongolia"

#: src/lib/Supported_Regions.php:152
msgid "Montenegro"
msgstr "Montenegro"

#: src/lib/Supported_Regions.php:153
msgid "Montserrat"
msgstr "Montserrat"

#: src/lib/Supported_Regions.php:154
msgid "Morocco"
msgstr "Marruecos"

#: src/lib/Supported_Regions.php:155
msgid "Mozambique"
msgstr "Mozambique"

#: src/lib/Supported_Regions.php:156
msgid "Myanmar"
msgstr "Birmania (Myanmar)"

#: src/lib/Supported_Regions.php:157
msgid "Namibia"
msgstr "Namibia"

#: src/lib/Supported_Regions.php:158
msgid "Nauru"
msgstr "Nauru"

#: src/lib/Supported_Regions.php:159
msgid "Nepal"
msgstr "Nepal"

#: src/lib/Supported_Regions.php:160
msgid "Netherlands"
msgstr "Países Bajos"

#: src/lib/Supported_Regions.php:161
msgid "New Caledonia"
msgstr "Nueva Caledonia"

#: src/lib/Supported_Regions.php:162
msgid "New Zealand"
msgstr "Nueva Zelanda"

#: src/lib/Supported_Regions.php:163
msgid "Nicaragua"
msgstr "Nicaragua"

#: src/lib/Supported_Regions.php:164
msgid "Niger"
msgstr "Níger"

#: src/lib/Supported_Regions.php:165
msgid "Nigeria"
msgstr "Nigeria"

#: src/lib/Supported_Regions.php:166
msgid "Niue"
msgstr "Niue"

#: src/lib/Supported_Regions.php:167
msgid "Norfolk Island"
msgstr "Isla Norfolk"

#: src/lib/Supported_Regions.php:168
msgid "North Korea"
msgstr "Corea del Norte"

#: src/lib/Supported_Regions.php:169
msgid "Northern Mariana Islands"
msgstr "Islas Marianas del Norte"

#: src/lib/Supported_Regions.php:170
msgid "Norway"
msgstr "Noruega"

#: src/lib/Supported_Regions.php:171
msgid "Oman"
msgstr "Omán"

#: src/lib/Supported_Regions.php:172
msgid "Pakistan"
msgstr "Pakistán"

#: src/lib/Supported_Regions.php:173
msgid "Palau"
msgstr "Palaos"

#: src/lib/Supported_Regions.php:174
msgid "Palestinian Territory"
msgstr "Territorios Palestinos"

#: src/lib/Supported_Regions.php:175
msgid "Panama"
msgstr "Panamá"

#: src/lib/Supported_Regions.php:176
msgid "Papua New Guinea"
msgstr "Papúa Nueva Guinea"

#: src/lib/Supported_Regions.php:177
msgid "Paraguay"
msgstr "Paraguay"

#: src/lib/Supported_Regions.php:178
msgid "Peru"
msgstr "Perú"

#: src/lib/Supported_Regions.php:179
msgid "Philippines"
msgstr "Filipinas"

#: src/lib/Supported_Regions.php:180
msgid "Pitcairn"
msgstr "Islas Pitcairn"

#: src/lib/Supported_Regions.php:181
msgid "Poland"
msgstr "Polonia"

#: src/lib/Supported_Regions.php:182
msgid "Portugal"
msgstr "Portugal"

#: src/lib/Supported_Regions.php:183
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: src/lib/Supported_Regions.php:184
msgid "Qatar"
msgstr "Qatar"

#: src/lib/Supported_Regions.php:185
msgid "Romania"
msgstr "Rumania"

#: src/lib/Supported_Regions.php:186
msgid "Russia"
msgstr "Rusia"

#: src/lib/Supported_Regions.php:187
msgid "Rwanda"
msgstr "Ruanda"

#: src/lib/Supported_Regions.php:188
msgid "Réunion"
msgstr "Reunión"

#: src/lib/Supported_Regions.php:189
msgid "Saint Barthélemy"
msgstr "San Bartolomé"

#: src/lib/Supported_Regions.php:190
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr "Santa Elena, Ascensión y Tristán de Acuña"

#: src/lib/Supported_Regions.php:191
msgid "Saint Kitts and Nevis"
msgstr "San Cristóbal y Nieves"

#: src/lib/Supported_Regions.php:192
msgid "Saint Lucia"
msgstr "Santa Lucía"

#: src/lib/Supported_Regions.php:193
msgid "Saint Martin (French part)"
msgstr "San Martín (Francia)"

#: src/lib/Supported_Regions.php:194
msgid "Saint Pierre and Miquelon"
msgstr "San Pedro y Miquelón"

#: src/lib/Supported_Regions.php:195
msgid "Saint Vincent and the Grenadines"
msgstr "San Vicente y las Granadinas"

#: src/lib/Supported_Regions.php:196
msgid "Samoa"
msgstr "Samoa"

#: src/lib/Supported_Regions.php:197
msgid "San Marino"
msgstr "San Marino"

#: src/lib/Supported_Regions.php:198
msgid "Sao Tome and Principe"
msgstr "Santo Tomé y Príncipe"

#: src/lib/Supported_Regions.php:199
msgid "Saudi Arabia"
msgstr "Arabia Saudita"

#: src/lib/Supported_Regions.php:200
msgid "Senegal"
msgstr "Senegal"

#: src/lib/Supported_Regions.php:201
msgid "Serbia"
msgstr "Serbia"

#: src/lib/Supported_Regions.php:202
msgid "Seychelles"
msgstr "Seychelles"

#: src/lib/Supported_Regions.php:203
msgid "Sierra Leone"
msgstr "Sierra Leona"

#: src/lib/Supported_Regions.php:204
msgid "Singapore"
msgstr "Singapur"

#: src/lib/Supported_Regions.php:205
msgid "Sint Maarten (Dutch part)"
msgstr "San Martín (Países Bajos)"

#: src/lib/Supported_Regions.php:206
msgid "Slovakia"
msgstr "Eslovaquia"

#: src/lib/Supported_Regions.php:207
msgid "Slovenia"
msgstr "Eslovenia"

#: src/lib/Supported_Regions.php:208
msgid "Solomon Islands"
msgstr "Islas Salomón"

#: src/lib/Supported_Regions.php:209
msgid "Somalia"
msgstr "Somalia"

#: src/lib/Supported_Regions.php:210
msgid "South Africa"
msgstr "Sudáfrica"

#: src/lib/Supported_Regions.php:211
msgid "South Georgia and the South Sandwich Islands"
msgstr "Islas Georgias del Sur y Sandwich del Sur"

#: src/lib/Supported_Regions.php:212
msgid "South Korea"
msgstr "Corea del Sur"

#: src/lib/Supported_Regions.php:213
msgid "South Sudan"
msgstr "Sudán del Sur"

#: src/lib/Supported_Regions.php:214
msgid "Spain"
msgstr "España"

#: src/lib/Supported_Regions.php:215
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: src/lib/Supported_Regions.php:216
msgid "Sudan"
msgstr "Sudán"

#: src/lib/Supported_Regions.php:217
msgid "Suriname"
msgstr "Surinam"

#: src/lib/Supported_Regions.php:218
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard y Jan Mayen"

#: src/lib/Supported_Regions.php:219
msgid "Eswatini"
msgstr "Suazilandia"

#: src/lib/Supported_Regions.php:220
msgid "Sweden"
msgstr "Suecia"

#: src/lib/Supported_Regions.php:221
msgid "Switzerland"
msgstr "Suiza"

#: src/lib/Supported_Regions.php:222
msgid "Syria"
msgstr "Siria"

#: src/lib/Supported_Regions.php:223
msgid "Taiwan"
msgstr "Taiwán"

#: src/lib/Supported_Regions.php:224
msgid "Tajikistan"
msgstr "Tayikistán"

#: src/lib/Supported_Regions.php:225
msgid "Tanzania"
msgstr "Tanzania"

#: src/lib/Supported_Regions.php:226
msgid "Thailand"
msgstr "Tailandia"

#: src/lib/Supported_Regions.php:227
msgid "Timor-Leste"
msgstr "Timor Oriental"

#: src/lib/Supported_Regions.php:228
msgid "Togo"
msgstr "Togo"

#: src/lib/Supported_Regions.php:229
msgid "Tokelau"
msgstr "Tokelau"

#: src/lib/Supported_Regions.php:230
msgid "Tonga"
msgstr "Tonga"

#: src/lib/Supported_Regions.php:231
msgid "Trinidad and Tobago"
msgstr "Trinidad y Tobago"

#: src/lib/Supported_Regions.php:232
msgid "Tunisia"
msgstr "Túnez"

#: src/lib/Supported_Regions.php:233
msgid "Türkiye"
msgstr "Turquía"

#: src/lib/Supported_Regions.php:234
msgid "Turkmenistan"
msgstr "Turkmenistán"

#: src/lib/Supported_Regions.php:235
msgid "Turks and Caicos Islands"
msgstr "Islas Turcas y Caicos"

#: src/lib/Supported_Regions.php:236
msgid "Tuvalu"
msgstr "Tuvalu"

#: src/lib/Supported_Regions.php:237
msgid "Uganda"
msgstr "Uganda"

#: src/lib/Supported_Regions.php:238
msgid "Ukraine"
msgstr "Ucrania"

#: src/lib/Supported_Regions.php:239
msgid "United Arab Emirates"
msgstr "Emiratos Árabes Unidos"

#: src/lib/Supported_Regions.php:240
msgid "United Kingdom"
msgstr "Reuino Unido"

#: src/lib/Supported_Regions.php:241
msgid "United States"
msgstr "Estados Unidos"

#: src/lib/Supported_Regions.php:242
msgid "United States - State of California"
msgstr "Estados Unidos - California"

#: src/lib/Supported_Regions.php:243
msgid "United States - State of Colorado"
msgstr "Estados Unidos, Colorado"

#: src/lib/Supported_Regions.php:244
msgid "United States - State of Connecticut"
msgstr "Estados Unidos, Connecticut"

#: src/lib/Supported_Regions.php:245
msgid "United States - State of Utah"
msgstr "Estados Unidos, Utah"

#: src/lib/Supported_Regions.php:246
msgid "United States - State of Virginia"
msgstr "Estados Unidos - Virginia"

#: src/lib/Supported_Regions.php:247
msgid "United States Minor Outlying Islands"
msgstr "Islas Ultramarinas Menores de Estados Unidos"

#: src/lib/Supported_Regions.php:248
msgid "Uruguay"
msgstr "Uruguay"

#: src/lib/Supported_Regions.php:249
msgid "Uzbekistan"
msgstr "Uzbekistán"

#: src/lib/Supported_Regions.php:250
msgid "Vanuatu"
msgstr "Vanuatu"

#: src/lib/Supported_Regions.php:251
msgid "Venezuela"
msgstr "Venezuela"

#: src/lib/Supported_Regions.php:252
msgid "Viet Nam"
msgstr "Vietnam"

#: src/lib/Supported_Regions.php:253
msgid "Virgin Islands, British"
msgstr "Islas Vírgenes Británicas"

#: src/lib/Supported_Regions.php:254
msgid "Virgin Islands, U.S."
msgstr "Islas Vírgenes de los Estados Unidos"

#: src/lib/Supported_Regions.php:255
msgid "Wallis and Futuna"
msgstr "Wallis y Futuna"

#: src/lib/Supported_Regions.php:256
msgid "Western Sahara"
msgstr "Sahara Occidental"

#: src/lib/Supported_Regions.php:257
msgid "Yemen"
msgstr "Yemen"

#: src/lib/Supported_Regions.php:258
msgid "Zambia"
msgstr "Zambia"

#: src/lib/Supported_Regions.php:259
msgid "Zimbabwe"
msgstr "Zimbabue"

#: src/lib/Supported_Regions.php:260
msgid "Åland Islands"
msgstr "Islas Åland"

#: src/settings/Network_Menu_Settings.php:36
#: src/settings/pages/Dashboard_Page.php:21
msgid "Cookiebot"
msgstr "Cookiebot"

#: src/settings/Network_Menu_Settings.php:44
#: src/settings/pages/Settings_Page.php:23
msgid "Cookiebot Settings"
msgstr "Ajustes de Cookiebot"

#: src/settings/Network_Menu_Settings.php:45
#: src/settings/pages/Settings_Page.php:24
#: src/view/admin/common/settings-page.php:30
#: src/view/admin/common/templates/main-tabs.php:32
#: src/view/admin/uc_frame/settings-page.php:47
#: src/view/admin/cb_frame/settings-page.php:50
msgid "Settings"
msgstr "Ajustes"

#: src/settings/Network_Menu_Settings.php:52
#: src/settings/pages/Support_Page.php:18
msgid "Cookiebot Support"
msgstr "Cookiebot Soporte"

#: src/settings/Network_Menu_Settings.php:53
#: src/settings/pages/Support_Page.php:19
#: src/view/admin/cb_frame/support-page.php:19
#: src/view/admin/common/support-page.php:19
#: src/view/admin/common/templates/main-tabs.php:50
#: src/view/admin/uc_frame/support-page.php:19
msgid "Support"
msgstr "Soporte"

#: src/settings/pages/Dashboard_Page.php:34
msgid "Cookiebot Dashboard"
msgstr "Escritorio de Cookiebot"

#: src/settings/pages/Debug_Page.php:27 src/settings/pages/Debug_Page.php:28
#: src/view/admin/cb_frame/debug-page.php:18
#: src/view/admin/common/templates/main-tabs.php:57
#: src/view/admin/uc_frame/debug-page.php:18
msgid "Debug info"
msgstr "Información de debug"

#: src/settings/pages/Iab_Page.php:20 src/settings/pages/Iab_Page.php:21
msgid "IAB"
msgstr "IAB"

#: src/settings/pages/Iab_Page.php:130
msgid "Purposes of data use"
msgstr "Propósitos del uso de datos"

#: src/settings/pages/Iab_Page.php:131
msgid ""
"Inform your users how you’ll use their data. We’ll show this on the second "
"layer of your consent banner, where users interested in more granular detail "
"about data processing can view it."
msgstr ""
"Informa a tus usuarios de cómo usarás sus datos. Se mostrará en la segunda "
"capa de tu banner de consentimiento, donde los usuarios interesados en "
"información más granular sobre el tratamiento de datos podrán verlo."

#: src/settings/pages/Iab_Page.php:138
msgid "Special purposes of data use"
msgstr "Propósitos especiales del uso de datos"

#: src/settings/pages/Iab_Page.php:139
msgid ""
"Inform your users about special purposes of using their data. We’ll show "
"this on the second layer of your consent banner."
msgstr ""
"Informa a tus usuarios sobre propósitos especiales del uso de sus datos. Se "
"mostrará en la segunda capa de tu banner de consentimiento."

#: src/settings/pages/Iab_Page.php:146
msgid "Features required for data processing"
msgstr "Funciones necesarias para el tratamiento de datos"

#: src/settings/pages/Iab_Page.php:147
msgid ""
"Inform users about the features necessary for processing their personal "
"data. We’ll list the selected features on the second layer of your consent "
"banner."
msgstr ""
"Informa a los usuarios de las funciones necesarias para el tratamiento de "
"sus datos personales. Las funciones seleccionadas se listarán en la segunda "
"capa de tu banner de consentimiento."

#: src/settings/pages/Iab_Page.php:154
msgid "Special features required for data processing"
msgstr "Funciones especiales necesarias para el tratamiento de datos"

#: src/settings/pages/Iab_Page.php:155
msgid ""
"Inform users about any specially categorized features required for "
"processing their personal data. We’ll list the selected features on the "
"second layer of your consent banner, offering options for users to enable or "
"disable them."
msgstr ""
"Informa a los usuarios sobre cualquier función categorizada como especial "
"necesaria para el tratamiento de sus datos personales. Las funciones "
"seleccionadas se listarán en la segunda capa de tu banner de consentimiento, "
"ofreciendo opciones para activar o desactivar a todos los usuarios."

#: src/settings/pages/Iab_Page.php:162
msgid "TCF listed vendors"
msgstr "Vendedores listados en el TCF"

#: src/settings/pages/Iab_Page.php:183
msgid "Store and/or access information on a device"
msgstr "Almacenar y/o acceder a información en un dispositivo"

#: src/settings/pages/Iab_Page.php:187
msgid "Use limited data to select advertising"
msgstr "Uso limitado de datos para seleccionar publicidad"

#: src/settings/pages/Iab_Page.php:191
msgid "Create profiles for personalised advertising"
msgstr "Elaborar perfiles para publicidad personalizada"

#: src/settings/pages/Iab_Page.php:195
msgid "Use profiles to select personalised advertising"
msgstr "Usar perfiles para seleccionar publicidad personalizada"

#: src/settings/pages/Iab_Page.php:199
msgid "Create profiles to personalise content"
msgstr "Elaborar perfiles para personalizar contenido"

#: src/settings/pages/Iab_Page.php:203
msgid "Use profiles to select personalised content"
msgstr "Usar perfiles para personalizar contenido"

#: src/settings/pages/Iab_Page.php:207
msgid "Measure advertising performance"
msgstr "Medir el rendimiento de la publicidad"

#: src/settings/pages/Iab_Page.php:211
msgid "Measure content performance"
msgstr "Medir el rendimiento del contenido"

#: src/settings/pages/Iab_Page.php:215
msgid ""
"Understand audiences through statistics or combinations of data from "
"different sources"
msgstr ""
"Comprender las audiencias mediante estadísticas o combinaciones de datos de "
"diferentes fuentes"

#: src/settings/pages/Iab_Page.php:219
msgid "Develop and improve services"
msgstr "Desarrollar o mejorar servicios"

#: src/settings/pages/Iab_Page.php:223
msgid "Use limited data to select content"
msgstr "Uso limitado de datos para seleccionar contenido"

#: src/settings/pages/Iab_Page.php:229
msgid "Ensure security, prevent and detect fraud, and fix errors"
msgstr ""
"Garantizar la seguridad, prevenir y detectar fraudes, y solucionar errores"

#: src/settings/pages/Iab_Page.php:233
msgid "Deliver and present advertising and content"
msgstr "Proveer y mostrar publicidad y contenido"

#: src/settings/pages/Iab_Page.php:239
msgid "Match and combine data from other data sources"
msgstr "Igualar y combinar datos de otras fuentes de datos"

#: src/settings/pages/Iab_Page.php:243
msgid "Link different devices"
msgstr "Enlazar diferentes dispositivos"

#: src/settings/pages/Iab_Page.php:247
msgid "Identify devices based on information transmitted automatically"
msgstr ""
"Identificar dispositivos en función de la información transmitida "
"automáticamente"

#: src/settings/pages/Iab_Page.php:253
msgid "Use precise geolocation data"
msgstr "Usar datos de geolocalización precisos"

#: src/settings/pages/Iab_Page.php:257
msgid "Actively scan device characteristics for identification"
msgstr ""
"Escanear activamente las características del dispositivo para identificarlo"

#: src/shortcode/Cookiebot_Declaration_Shortcode.php:53
msgid "Please add your Cookiebot ID to show Cookie Declarations"
msgstr "Agrega tu ID de Cookiebot para mostrar la declaración de cookies"

#: src/shortcode/Cookiebot_Embedding_Shortcode.php:28
msgid "Please add a service ID into the shortcode \"service\" parameter."
msgstr "Añade un ID de servicio en el parámetro «service» del código corto."

#: src/view/admin/cb_frame/dashboard-page.php:33
msgid "I already have a Cookiebot CMP account"
msgstr "Ya tengo una cuenta de Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:37
msgid "Connect my existing account"
msgstr "Conectar mi cuenta existente"

#: src/view/admin/cb_frame/dashboard-page.php:42
msgid "Your Cookiebot CMP for WordPress solution"
msgstr "Tu solución de Cookiebot CMP para WordPress"

#: src/view/admin/cb_frame/dashboard-page.php:47
#: src/view/admin/uc_frame/dashboard-page.php:35
msgid "Account added"
msgstr "Cuenta añadida"

#: src/view/admin/cb_frame/dashboard-page.php:50
msgid "Congratulations!"
msgstr "¡Enhorabuena!"

#: src/view/admin/cb_frame/dashboard-page.php:51
msgid "You have added your Domain Group ID to WordPress. You are all set!"
msgstr "Has añadido tu ID del grupo de dominios a WordPress. ¡Ya estás listo!"

#: src/view/admin/cb_frame/dashboard-page.php:62
#: src/view/admin/uc_frame/dashboard-page.php:48
msgid "Your opinion matters"
msgstr "Tu opinión nos importa"

#: src/view/admin/cb_frame/dashboard-page.php:65
msgid ""
"Are you happy with our WordPress plugin? Your feedback will help us make our "
"product better for you."
msgstr ""
"¿Estás satisfecho con nuestro plugin de WordPress? Tu feedback nos ayuda a "
"mejorar nuestro producto para ti."

#: src/view/admin/cb_frame/dashboard-page.php:69
msgid "Write a review"
msgstr "Escribe una reseña"

#: src/view/admin/cb_frame/dashboard-page.php:80
#: src/view/admin/common/dashboard-page.php:45
msgid "Get started"
msgstr "Empezar"

#: src/view/admin/cb_frame/dashboard-page.php:83
msgid "Create a new Cookiebot CMP account"
msgstr "Crear una nueva cuenta de Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:87
#: src/view/admin/common/dashboard-page.php:52
msgid "Create a new account"
msgstr "Crear una cuenta nueva"

#: src/view/admin/cb_frame/dashboard-page.php:92
msgid "Learn more about how to optimize your Cookiebot CMP setup?"
msgstr "¡Descubre cómo optimizar tu configuración de Cookiebot CMP!"

#: src/view/admin/cb_frame/dashboard-page.php:96
msgid "Visit Help Center"
msgstr "Consulta nuestro Centro de Asistencia"

#: src/view/admin/cb_frame/dashboard-page.php:113
#: src/view/admin/cb_frame/support-page.php:43
#: src/view/admin/common/dashboard-page.php:63
#: src/view/admin/common/support-page.php:43
#: src/view/admin/uc_frame/dashboard-page.php:66
#: src/view/admin/uc_frame/support-page.php:43
msgid "Video guide"
msgstr "Videoguía"

#: src/view/admin/cb_frame/dashboard-page.php:115
msgid "How to get started with Cookiebot CMP"
msgstr "Cómo empezar con Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:119
msgid "Learn more about Cookiebot CMP"
msgstr "Más información sobre Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:131
#: src/view/admin/common/dashboard-page.php:81
#: src/view/admin/uc_frame/dashboard-page.php:84
#: src/view/admin/uc_frame/settings/embeddings-page.php:32
msgid "GDPR"
msgstr "RGPD"

#: src/view/admin/cb_frame/dashboard-page.php:134
#: src/view/admin/common/dashboard-page.php:84
#: src/view/admin/uc_frame/dashboard-page.php:87
msgid "Europe"
msgstr "Europa"

#: src/view/admin/cb_frame/dashboard-page.php:138
#: src/view/admin/cb_frame/dashboard-page.php:140
#: src/view/admin/cb_frame/dashboard-page.php:155
#: src/view/admin/cb_frame/dashboard-page.php:157
#: src/view/admin/common/dashboard-page.php:88
#: src/view/admin/common/dashboard-page.php:90
#: src/view/admin/common/dashboard-page.php:105
#: src/view/admin/common/dashboard-page.php:107
#: src/view/admin/uc_frame/dashboard-page.php:91
#: src/view/admin/uc_frame/dashboard-page.php:93
#: src/view/admin/uc_frame/dashboard-page.php:108
#: src/view/admin/uc_frame/dashboard-page.php:110
msgid "Learn More"
msgstr "Más información"

#: src/view/admin/cb_frame/dashboard-page.php:148
#: src/view/admin/common/dashboard-page.php:98
#: src/view/admin/uc_frame/dashboard-page.php:101
msgid "CCPA"
msgstr "CCPA"

#: src/view/admin/cb_frame/dashboard-page.php:151
#: src/view/admin/common/dashboard-page.php:101
#: src/view/admin/uc_frame/dashboard-page.php:104
msgid "North America"
msgstr "Norteamérica"

#: src/view/admin/cb_frame/dashboard-page.php:162
#: src/view/admin/common/dashboard-page.php:112
msgid "See other legislations"
msgstr "Consulta otras legislaciones"

#: src/view/admin/cb_frame/debug-page.php:22
msgid "Debug information"
msgstr "Información de debug"

#: src/view/admin/cb_frame/debug-page.php:25
msgid ""
"The information below is for debugging purposes. If you have any issues with "
"your Cookiebot CMP integration, this information is the best place to start."
msgstr ""
"La información debajo es para debuguear. Si tienes cualquier problema con tu "
"integración de Cookiebot CMP, esta información es el mejor punto de partida."

#: src/view/admin/cb_frame/debug-page.php:33
#: src/view/admin/uc_frame/debug-page.php:33
msgid "Copy debug information to clipboard"
msgstr "Copia la información de debug a tu portapapeles"

#: src/view/admin/cb_frame/debug-page.php:42
msgid ""
"If you have any issues with the implemenation of Cookiebot CMP, please visit "
"our Support Center."
msgstr ""
"SI tienes cualquier problema con la implementación de Cookiebot CMP, por "
"favor, visita nuestro Centro de Asistencia."

#: src/view/admin/cb_frame/debug-page.php:45
#: src/view/admin/uc_frame/debug-page.php:44
msgid "Visit Support Center"
msgstr "Visita el Centro de Asistencia (en inglés)"

#: src/view/admin/cb_frame/network-settings-page.php:22
#: src/view/admin/common/network-settings-page.php:23
#: src/view/admin/uc_frame/network-settings-page.php:23
msgid "Network Settings"
msgstr "Ajustes de la Red"

#: src/view/admin/cb_frame/network-settings-page.php:33
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:3
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:3
#: src/view/admin/uc_frame/network-settings-page.php:34
msgid "Are you sure?"
msgstr "¿Estás seguro?"

#: src/view/admin/cb_frame/network-settings-page.php:36
#: src/view/admin/uc_frame/network-settings-page.php:37
msgid ""
"You will need to add a new ID before updating other network settings. If any "
"subsite is using its own account disconnecting this account won’t affect it."
msgstr ""
"Tendrás que añadir un nuevo ID antes de actualizar otros ajustes de red. Si "
"alguna subpágina está utilizando su propia cuenta, la desconexión de esta "
"cuenta no la afectará."

#: src/view/admin/cb_frame/network-settings-page.php:40
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:10
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:10
#: src/view/admin/uc_frame/network-settings-page.php:41
msgid "Cancel"
msgstr "Cancelar"

#: src/view/admin/cb_frame/network-settings-page.php:43
#: src/view/admin/cb_frame/network-settings-page.php:65
#: src/view/admin/cb_frame/settings/general-page.php:52
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:13
#: src/view/admin/uc_frame/network-settings-page.php:44
#: src/view/admin/uc_frame/network-settings-page.php:75
#: src/view/admin/uc_frame/settings/general-page.php:45
msgid "Disconnect account"
msgstr "Desconectar cuenta"

#: src/view/admin/cb_frame/network-settings-page.php:50
msgid "Network Domain Group ID"
msgstr "ID de grupo de dominios de red"

#: src/view/admin/cb_frame/network-settings-page.php:52
msgid ""
"If added this will be the default Cookiebot ID for all subsites. Subsites "
"are able to override the Cookiebot ID."
msgstr ""
"Si se agrega, será el ID de Cookiebot predeterminado para todos los "
"subsitios. Los subsitios pueden anular la ID de Cookiebot."

#: src/view/admin/cb_frame/network-settings-page.php:54
#: src/view/admin/cb_frame/settings/general-page.php:37
msgid "Read more on the Domain Group ID"
msgstr "Más información sobre la ID de Grupo de Dominios"

#: src/view/admin/cb_frame/network-settings-page.php:58
#: src/view/admin/cb_frame/settings/general-page.php:43
msgid "Add your Domain Group ID"
msgstr "Añade tu ID de Grupo de Dominios"

#: src/view/admin/cb_frame/network-settings-page.php:73
#: src/view/admin/cb_frame/settings/general-page.php:146
#: src/view/admin/uc_frame/network-settings-page.php:111
#: src/view/admin/uc_frame/settings/general-page.php:120
msgid "Cookie-blocking"
msgstr "Bloqueo de cookies"

#: src/view/admin/cb_frame/network-settings-page.php:75
msgid ""
"Select your cookie-blocking mode here. Auto cookie-blocking mode will "
"automatically block all cookies (except for ‘strictly necessary’ cookies) "
"until a user has given consent. Manual cookie-blocking mode requests manual "
"adjustments to the cookie-setting scripts. Please find our implementation "
"guides below:"
msgstr ""
"Selecciona tu modo de bloqueo de cookies aquí. El modo de bloqueo de cookies "
"automático bloqueará todas las cookies (excepto las “estrictamente "
"necesarias”) hasta que el usuario haya dado su consentimiento. El modo de "
"bloqueo de cookies manual requiere de una configuración manual de los "
"scripts de ajustes de cookies. Puedes encontrar nuestras guías de "
"implementación debajo:"

#: src/view/admin/cb_frame/network-settings-page.php:80
#: src/view/admin/cb_frame/settings/general-page.php:167
#: src/view/admin/uc_frame/network-settings-page.php:118
#: src/view/admin/uc_frame/settings/general-page.php:133
msgid "Select cookie-blocking mode"
msgstr "Seleccionar modo de bloqueo de cookies"

#: src/view/admin/cb_frame/network-settings-page.php:87
#: src/view/admin/cb_frame/settings/general-page.php:175
#: src/view/admin/uc_frame/network-settings-page.php:125
#: src/view/admin/uc_frame/settings/general-page.php:141
msgid "Automatic"
msgstr "Automático"

#: src/view/admin/cb_frame/network-settings-page.php:88
#: src/view/admin/cb_frame/settings/general-page.php:176
#: src/view/admin/uc_frame/network-settings-page.php:126
#: src/view/admin/uc_frame/settings/general-page.php:142
msgid "Recommended"
msgstr "Recomendado"

#: src/view/admin/cb_frame/network-settings-page.php:96
#: src/view/admin/cb_frame/network-settings-page.php:146
#: src/view/admin/cb_frame/network-settings-page.php:193
#: src/view/admin/uc_frame/network-settings-page.php:134
msgid "Choose per subsite"
msgstr "Elija por subsitio"

#: src/view/admin/cb_frame/network-settings-page.php:104
#: src/view/admin/cb_frame/settings/general-page.php:206
msgid "Cookiebot™ script tag"
msgstr "Etiqueta de script de Cookiebot™"

#: src/view/admin/cb_frame/network-settings-page.php:106
msgid ""
"Add async or defer attribute to Cookie banner script tag. Default: Choose "
"per subsite"
msgstr ""
"Agrega el atributo async o defer a la etiqueta script del banner de cookies. "
"Predeterminado: elegir por subsitio"

#: src/view/admin/cb_frame/network-settings-page.php:109
msgid "This feature is only available when not using Auto Blocking"
msgstr ""
"Esta función solo está disponible cuando no se utiliza el bloqueo automático"

#: src/view/admin/cb_frame/network-settings-page.php:112
#: src/view/admin/cb_frame/network-settings-page.php:156
#: src/view/admin/cb_frame/network-settings-page.php:228
#: src/view/admin/uc_frame/network-settings-page.php:169
msgid ""
"Setting will apply for all subsites. Subsites will not be able to override."
msgstr ""
"Los ajustes se aplicarán a todos los subsitios. Los subsitios no podrán "
"cambiarlo."

#: src/view/admin/cb_frame/network-settings-page.php:125
#: src/view/admin/cb_frame/network-settings-page.php:172
#: src/view/admin/cb_frame/settings/additional-page.php:99
#: src/view/admin/cb_frame/settings/general-page.php:222
msgid "None"
msgstr "Ninguno"

#: src/view/admin/cb_frame/network-settings-page.php:154
msgid "Cookiebot declaration script tag"
msgstr "Etiqueta script de la declaración de cookies"

#: src/view/admin/cb_frame/network-settings-page.php:159
msgid ""
"Add async or defer attribute to Cookie declaration script tag. Default: "
"Choose per subsite"
msgstr ""
"Agrega el atributo async o defer a la etiqueta del script de declaración de "
"cookies. Predeterminado: elegir por subsitio"

#: src/view/admin/cb_frame/network-settings-page.php:201
#: src/view/admin/cb_frame/settings/additional-page.php:10
#: src/view/admin/uc_frame/network-settings-page.php:142
#: src/view/admin/uc_frame/settings/additional-page.php:10
msgid "Automatic updates"
msgstr "Actualizaciones automáticas"

#: src/view/admin/cb_frame/network-settings-page.php:203
#: src/view/admin/cb_frame/settings/additional-page.php:13
#: src/view/admin/uc_frame/network-settings-page.php:144
#: src/view/admin/uc_frame/settings/additional-page.php:13
msgid ""
"Enable automatic updates whenever we release a new version of the plugin."
msgstr ""
"Activa las actualizaciones automáticas cada vez que salga una nueva versión "
"del plugin."

#: src/view/admin/cb_frame/network-settings-page.php:218
#: src/view/admin/cb_frame/settings/additional-page.php:28
#: src/view/admin/uc_frame/network-settings-page.php:159
#: src/view/admin/uc_frame/settings/additional-page.php:28
msgid "Automatically update to new version"
msgstr "Actualización automática a la nueva versión"

#: src/view/admin/cb_frame/network-settings-page.php:226
#: src/view/admin/cb_frame/settings/general-page.php:248
#: src/view/admin/uc_frame/network-settings-page.php:167
#: src/view/admin/uc_frame/settings/general-page.php:172
msgid "Hide cookie popup"
msgstr "Esconder el popup de cookies"

#: src/view/admin/cb_frame/network-settings-page.php:231
#: src/view/admin/cb_frame/settings/general-page.php:254
#: src/view/admin/uc_frame/network-settings-page.php:172
#: src/view/admin/uc_frame/settings/general-page.php:178
msgid ""
"This will remove the cookie consent banner from your website. The cookie "
"declaration shortcode will still be available if you are using Google Tag "
"Manager (or equal), you need to add the Cookiebot script in your Tag Manager."
msgstr ""
"Esta acción eliminará el banner de consentimiento de cookies de tu sitio "
"web. El código corto de declaración de cookies seguirá estando disponible si "
"usas Google Tag Manager (o similar), necesitas añadir el script de Cookiebot "
"en tu Tag Manager."

#: src/view/admin/cb_frame/network-settings-page.php:246
#: src/view/admin/cb_frame/settings/general-page.php:280
#: src/view/admin/uc_frame/network-settings-page.php:187
#: src/view/admin/uc_frame/settings/general-page.php:204
msgid "Hide the cookie popup banner"
msgstr "Esconder el banner popup de cookies"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:11
#: src/view/admin/common/prior-consent/available-addons/tab-header.php:4
#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:14
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:14
msgid "Remember to save your changes before switching tabs"
msgstr "Recuerda guardar los cambios antes de cambiar de pestaña"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:20
msgid "Consent Level API Settings"
msgstr "Ajustes de Consent Level API"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:29
msgid ""
"WP Consent Level API and Cookiebot™ categorize cookies a bit differently. "
"The default settings should fit most needs, but if you need to change the "
"mapping you can do so below."
msgstr ""
"WP Consent Level API y Cookiebot™ categorizan las cookies de forma algo "
"diferente. Los ajustes por defecto deberían cubrir las necesidades de la "
"mayoría, pero si necesitas cambiar algún ajuste puedes hacerlo debajo."

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:79
msgid "Cookiebot™ cookie categories"
msgstr "Categorías de cookies en Cookiebot™"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:96
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:51
msgid "WP Consent API cookies categories equivalent"
msgstr "Categorías de cookies equivalentes en WP Consent API"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:102
msgid "Functional"
msgstr "Funcionales"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:150
msgid "Statistics Anonymous"
msgstr "Estadísticas Anónimas"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:179
msgid "Reset to default mapping"
msgstr "Restablecer los ajustes por defecto"

#: src/view/admin/cb_frame/settings-page.php:48
msgid "Select at least one vendor on TCF tab"
msgstr "Seleccione al menos un vendedor en la pestaña TCF"

#: src/view/admin/cb_frame/settings-page.php:57
#: src/view/admin/uc_frame/settings-page.php:54
msgid "General Settings"
msgstr "Ajustes generales"

#: src/view/admin/cb_frame/settings-page.php:61
#: src/view/admin/uc_frame/settings-page.php:58
msgid "Additional Settings"
msgstr "Ajustes adicionales"

#: src/view/admin/cb_frame/settings-page.php:65
#: src/view/admin/cb_frame/settings/gtm-page.php:27
#: src/view/admin/uc_frame/settings-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:12
#: src/view/admin/uc_frame/settings/gtm-page.php:27
msgid "Google Tag Manager"
msgstr "Google Tag Manager"

#: src/view/admin/cb_frame/settings-page.php:69
#: src/view/admin/cb_frame/settings/gcm-page.php:50
#: src/view/admin/uc_frame/settings-page.php:66
#: src/view/admin/uc_frame/settings/gcm-page.php:28
#: src/view/admin/uc_frame/settings/gcm-page.php:43
msgid "Google Consent Mode"
msgstr "Modo de Consentimiento de Google"

#: src/view/admin/cb_frame/settings-page.php:73
#: src/view/admin/uc_frame/settings/embeddings-page.php:33
msgid "TCF"
msgstr "TCF"

#: src/view/admin/cb_frame/settings-page.php:77
msgid "Multiple Configurations"
msgstr "Configuraciones Múltiples"

#: src/view/admin/cb_frame/settings/additional-page.php:37
#: src/view/admin/uc_frame/settings/additional-page.php:37
msgid "Show the banner while logged in"
msgstr "Mostrar el banner mientras se está conectado"

#: src/view/admin/cb_frame/settings/additional-page.php:40
#: src/view/admin/uc_frame/settings/additional-page.php:40
msgid ""
"You can choose to display the consent banner on your website while you’re "
"logged in and changing settings or customizing your banner."
msgstr ""
"Puedes elegir mostrar el banner en tu sitio web cuando estés conectado y "
"mientras cambias la configuración o la personalización."

#: src/view/admin/cb_frame/settings/additional-page.php:66
#: src/view/admin/uc_frame/settings/additional-page.php:66
msgid "Show banner on website when logged in"
msgstr "Mostrar el banner en el sitio web mientras se está conectado"

#: src/view/admin/cb_frame/settings/additional-page.php:75
msgid "Cookie declaration script tag:"
msgstr "Etiqueta de script de declaración de cookies:"

#: src/view/admin/cb_frame/settings/additional-page.php:78
msgid ""
"If you implemented the declaration on your page through our widget in "
"WordPress, you can choose here how the script should be loaded."
msgstr ""
"Si has implementado la declaración en tu sitio web mediante nuestro widget "
"en WordPress, puedes elegir aquí cómo se debe cargar el script."

#: src/view/admin/cb_frame/settings/additional-page.php:84
msgid "Select the cookie declaration script loading setting"
msgstr "Selecciona el ajuste de carga del script de la declaración de cookies"

#: src/view/admin/cb_frame/settings/additional-page.php:116
#: src/view/admin/cb_frame/settings/general-page.php:187
#: src/view/admin/cb_frame/settings/general-page.php:239
#: src/view/admin/cb_frame/settings/general-page.php:283
#: src/view/admin/uc_frame/settings/general-page.php:153
#: src/view/admin/uc_frame/settings/general-page.php:207
msgid "Disabled by active setting in Network Settings"
msgstr "Deshabilitado por configuración activa en Configuración de red"

#: src/view/admin/cb_frame/settings/additional-page.php:125
msgid "Ignore scripts in queue from Cookiebot CMP scan:"
msgstr "Ignorar los scripts en cola del escaneo de Cookiebot CMP:"

#: src/view/admin/cb_frame/settings/additional-page.php:128
msgid ""
"List scripts source URL (one per line) from the queue to ignore Cookiebot "
"CMP scan. Partial source URL will also work, e.g. wp-content/plugins/"
"woocommerce will block every WooCommerce script."
msgstr ""
"Crea una lista con los URL fuente de los scripts (uno por línea) de la cola "
"para ignorar el escaneo de Cookiebot CMP. Los URL fuentes parciales también "
"funcionarán, p. ej., wp-cntent/plugins/woocommerce bloqueará cualquier "
"script de WooCommerce."

#: src/view/admin/cb_frame/settings/additional-page.php:131
msgid ""
"This feature only works for scripts loaded via wp_enqueue_script. Manually "
"added scripts must be manually edited."
msgstr ""
"Esta característica solo funciona con los scripts caragados mediante "
"wp_enqueue_script. Los scripts añadidos manualmente deben ser editados "
"manualmente."

#: src/view/admin/cb_frame/settings/additional-page.php:137
msgid "Script source URL:"
msgstr "URL fuente de los scripts:"

#: src/view/admin/cb_frame/settings/additional-page.php:143
msgid "Add script source URL, one per line"
msgstr "Añade la URL de origen del script, una por línea"

#: src/view/admin/cb_frame/settings/gcm-page.php:14
msgid "What is Google Consent Mode and why should you enable it?"
msgstr ""
"¿Qué es el Modo de Consentimiento de Google y por qué debería activarlo?"

#: src/view/admin/cb_frame/settings/gcm-page.php:17
msgid ""
"Google Consent Mode is a way for your website to measure conversions and get "
"analytics insights while being fully GDPR-compliant when using services like "
"Google Analytics, Google Tag Manager (GTM) and Google Ads."
msgstr ""
"El Modo de Consentimiento de Google te permite medir las conversiones y "
"obtener analíticas e información de tu sitio web al mismo tiempo que cumples "
"con el RGPD usando servicios como Google Analytics, Google Tag Manager (GTM) "
"y Google Ads."

#: src/view/admin/cb_frame/settings/gcm-page.php:20
msgid ""
"Cookiebot consent managment platform (CMP) and Google Consent Mode integrate "
"seamlessly to offer you plug-and-play compliance and streamlined use of all "
"Google's services in one easy solution."
msgstr ""
"Nuestra plataforma de Gestión del Consentimiento, Cookiebot CMP, y el Modo "
"de Consentimiento de Google se integran perfectamente para ofrecerte un "
"cumplimiento automático y un uso optimizado de los servicios de Google en "
"una solución fácil y sencilla."

#: src/view/admin/cb_frame/settings/gcm-page.php:24
msgid "Read more about Cookiebot CMP and Google Consent Mode"
msgstr ""
"Más información sobre Cookiebot CMP y el Modo de Consentimiento de Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:31
msgid "Google Consent Mode:"
msgstr "Modo de Consentimiento de Google:"

#: src/view/admin/cb_frame/settings/gcm-page.php:34
msgid ""
"Enable Google Consent Mode with default settings on your WordPress page."
msgstr ""
"Permite el Modo de Consentimiento de Google con una configuración por "
"defecto en tu página creada con WordPress."

#: src/view/admin/cb_frame/settings/gcm-page.php:38
#: src/view/admin/cb_frame/settings/gcm-page.php:68
#: src/view/admin/cb_frame/settings/gtm-page.php:18
msgid "Read more"
msgstr "Más información"

#: src/view/admin/cb_frame/settings/gcm-page.php:61
msgid "URL passthrough:"
msgstr "Paso de URL:"

#: src/view/admin/cb_frame/settings/gcm-page.php:64
msgid ""
"This feature will allow you to pass data between pages when not able to use "
"cookies without/prior consent."
msgstr ""
"Esta función le permitirá pasar datos entre páginas cuando no pueda usar "
"cookies sin/previo consentimiento."

#: src/view/admin/cb_frame/settings/gcm-page.php:80
msgid "URL passthrough"
msgstr "Paso de URL"

#: src/view/admin/cb_frame/settings/gcm-page.php:89
msgid "Google Consent Mode cookies"
msgstr "Cookies del Modo de Consentimiento de Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:91
msgid ""
"Select the cookie types that need to be consented for the Google Consent "
"Mode script"
msgstr ""
"Selecciona los tipos de cookies que tienen que ser consentidas para el "
"script del Modo de Consentimiento de Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:93
#: src/view/admin/cb_frame/settings/general-page.php:212
#: src/view/admin/cb_frame/settings/general-page.php:251
#: src/view/admin/cb_frame/settings/gtm-page.php:80
#: src/view/admin/uc_frame/settings/general-page.php:175
msgid "This feature is only available when using Manual Blocking"
msgstr "Esta función solo está disponible al usar el Bloqueo Manual"

#: src/view/admin/cb_frame/settings/gcm-page.php:95
#: src/view/admin/cb_frame/settings/gtm-page.php:82
msgid ""
"This option may affect the behaviour of your GTM Tags, as the script will "
"run on the selected cookies consent."
msgstr ""
"Esta opción puede afectar el comportamiento de tus etiquetas de GTM, ya que "
"el script se ejecutará en el consentimiento de las cookies seleccionadas."

#: src/view/admin/cb_frame/settings/gcm-page.php:97
#: src/view/admin/cb_frame/settings/gtm-page.php:84
msgid ""
"Please make sure your Tags in Google Tag Manager are triggered correctly."
msgstr ""
"Por favor, asegúrate de que tus etiquetas en Google Tag Manager funcionan "
"correctamente."

#: src/view/admin/cb_frame/settings/gcm-page.php:102
#: src/view/admin/cb_frame/settings/gtm-page.php:89
#: src/view/admin/common/prior-consent/available-addons/tab.php:28
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:25
msgid "Check one or multiple cookie types:"
msgstr "Selecciona uno o más tipos de cookies:"

#: src/view/admin/cb_frame/settings/general-page.php:30
msgid "Connect your Domain Group"
msgstr "Conecta tu grupo de dominios"

#: src/view/admin/cb_frame/settings/general-page.php:33
msgid ""
"To connect your Domain Group, paste your Domain Group ID here. If you want "
"to connect a second ID for other regions, you can do this under the "
"\"Multiple Configurations\" tab."
msgstr ""
"Para conectar tu Grupo de Dominios, pega tu ID de Grupo de Dominios aquí. Si "
"quieres conectar una segunda ID para otras regiones, puedes hacerlo en la "
"pestaña de “Configuraciones Múltiples”."

#: src/view/admin/cb_frame/settings/general-page.php:54
#: src/view/admin/uc_frame/settings/general-page.php:48
msgid "Using network account"
msgstr "Al usar la cuenta de red"

#: src/view/admin/cb_frame/settings/general-page.php:64
#: src/view/admin/uc_frame/settings/general-page.php:59
msgid "Do not use Network Settings ID"
msgstr "No utilizar ajustes de red ID de cuenta"

#: src/view/admin/cb_frame/settings/general-page.php:75
msgid "Language:"
msgstr "Idioma:"

#: src/view/admin/cb_frame/settings/general-page.php:77
msgid "Select your main language here."
msgstr "Selecciona tu idioma principal aquí."

#: src/view/admin/cb_frame/settings/general-page.php:82
msgid "Select the language"
msgstr "Selecciona el idioma"

#: src/view/admin/cb_frame/settings/general-page.php:86
msgid "Default (Autodetect)"
msgstr "Por defecto (Autodetección)"

#: src/view/admin/cb_frame/settings/general-page.php:90
msgid "Use WordPress Language"
msgstr "Usar el idioma de WordPress"

#: src/view/admin/cb_frame/settings/general-page.php:111
msgid ""
"If enabled, Cookiebot™ will use the current location to set the banner and "
"cookie declaration language."
msgstr ""
"Si se activa, Cookiebot™ usará la ubicación actual para configurar el idioma "
"del banner y de la declaración de cookies."

#: src/view/admin/cb_frame/settings/general-page.php:114
msgid ""
"Please make sure that all languages in use have been added in the Cookiebot™ "
"Manager."
msgstr ""
"Por favor, asegúrate de que todos los idiomas empleados hayan sido también "
"añadidos al admin de Cookiebot™."

#: src/view/admin/cb_frame/settings/general-page.php:116
msgid "This feature disables the main language selector."
msgstr "Esta función desactiva el selector del idioma principal."

#: src/view/admin/cb_frame/settings/general-page.php:117
msgid ""
"If you have already set a language in the cookie declaration shortcode, this "
"feature will not change it."
msgstr ""
"Si ya has configurado un idioma en el código corto de la declaración de "
"cookies, esta función no lo cambiará."

#: src/view/admin/cb_frame/settings/general-page.php:120
msgid "Read more on how to add languages"
msgstr "Más información sobre cómo añadir idiomas"

#: src/view/admin/cb_frame/settings/general-page.php:135
msgid "Use website location to set language"
msgstr "Usar la ubicación del sitio web para configurar el idioma"

#: src/view/admin/cb_frame/settings/general-page.php:149
#: src/view/admin/uc_frame/network-settings-page.php:113
#: src/view/admin/uc_frame/settings/general-page.php:123
msgid ""
"Choose the type of your cookie-blocking mode. Select automatic to "
"automatically block all cookies except those strictly necessary to use "
"before user gives consent. Manual mode lets you adjust your cookie settings "
"within your website’s HTML."
msgstr ""
"Elige el modo de bloqueo de cookies. Selecciona «automático» para bloquear "
"automáticamente todas las cookies excepto las estrictamente necesarias antes "
"de que el usuario dé su consentimiento. El modo manual te permite ajustar la "
"configuración de las cookies dentro del HTML de tu sitio web."

#: src/view/admin/cb_frame/settings/general-page.php:154
msgid "Guide to auto cookie-blocking"
msgstr "Guía al bloqueo de cookies automático"

#: src/view/admin/cb_frame/settings/general-page.php:160
msgid "Guide to manual cookie-blocking"
msgstr "Guía al bloqueo de cookies manual"

#: src/view/admin/cb_frame/settings/general-page.php:184
#: src/view/admin/uc_frame/settings/general-page.php:150
msgid "Manual"
msgstr "Manual"

#: src/view/admin/cb_frame/settings/general-page.php:209
msgid "Add async or defer attribute to cookie declaration script tag"
msgstr ""
"Añade los atributos “async” o “defer” al tag de script de la declaración de "
"cookies"

#: src/view/admin/cb_frame/settings/gtm-page.php:12
msgid "Google Tag Manager:"
msgstr "Google Tag Manager:"

#: src/view/admin/cb_frame/settings/gtm-page.php:14
msgid "For more details about Cookiebot CMP and Google Tag Manager."
msgstr "Para más detalles sobre Cookiebot CMP y Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:35
#: src/view/admin/cb_frame/settings/gtm-page.php:46
#: src/view/admin/uc_frame/settings/gtm-page.php:35
#: src/view/admin/uc_frame/settings/gtm-page.php:46
msgid "Google Tag Manager ID"
msgstr "ID de Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:37
msgid "Paste your Tag Manager ID into the field on the right."
msgstr "Pega tu ID de Google Tag Manager en el campo a la derecha."

#: src/view/admin/cb_frame/settings/gtm-page.php:41
#: src/view/admin/uc_frame/settings/gtm-page.php:41
msgid "How to find the GTM ID"
msgstr "Cómo encontrar la ID de GTM"

#: src/view/admin/cb_frame/settings/gtm-page.php:48
msgid "Enter GTM ID"
msgstr "Introducir la ID de GTM"

#: src/view/admin/cb_frame/settings/gtm-page.php:56
msgid "Data Layer Name (optional)"
msgstr "Nombre de la Capa de Datos (opcional)"

#: src/view/admin/cb_frame/settings/gtm-page.php:58
msgid ""
"You can also paste your Data Layer Name here. This is optional information."
msgstr ""
"También puedes pegar tu Nombre de la Capa de Datos aquí. Esta información es "
"opcional."

#: src/view/admin/cb_frame/settings/gtm-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:62
msgid "How to find the Data Layer Name"
msgstr "Cómo encontrar el Nombre de la Capa de Datos"

#: src/view/admin/cb_frame/settings/gtm-page.php:67
#: src/view/admin/uc_frame/settings/gtm-page.php:67
msgid "Name of your Data Layer"
msgstr "Nombre de la Capa de Datos"

#: src/view/admin/cb_frame/settings/gtm-page.php:76
msgid "Google Tag Manager cookies"
msgstr "Cookies de Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:78
msgid ""
"Select the cookie types that need to be consented for the Google Tag Manager "
"script"
msgstr ""
"Selecciona los tipos de cookies que tienen que ser consentidas para el "
"script del Google Tag Manager"

#: src/view/admin/cb_frame/settings/iab-page.php:26
msgid "IAB Integration:"
msgstr "Integración de la IAB:"

#: src/view/admin/cb_frame/settings/iab-page.php:28
msgid ""
"If you want to use the IAB Framework TCF within your Consent Management "
"Platform (CMP) you can enable it on the right. Be aware that activating this "
"could override some of the configurations you made with the default setup "
"defined by the IAB."
msgstr ""
"Si quieres usar el Marco TCF de la IAB en tu Plataforma de Gestión del "
"Consentimiento (CMP), puedes activarlo a la derecha. Por favor, ten en "
"cuenta que activar esto podría sobreescribir algunos de los ajustos que has "
"hecho con la configuración por defecto de la IAB."

#: src/view/admin/cb_frame/settings/iab-page.php:32
msgid "Read more on IAB with Cookiebot CMP here"
msgstr "Más información sobre la IAB con Cookiebot CMP"

#: src/view/admin/cb_frame/settings/iab-page.php:41
msgid "IAB TCF V2.2 integration"
msgstr "Integración del TCF 2.2 de la IAB"

#: src/view/admin/cb_frame/settings/iab-page.php:83
msgid ""
"IAB vendor list is temporarily offline. Please try refreshing the page after "
"a couple of minutes."
msgstr ""
"La lista de vendedores de la IAB está temporalmente offline. Prueba a "
"recargar la página en unos minutos."

#: src/view/admin/cb_frame/settings/iab-page.php:85
msgid ""
"If you had previously saved configurations, don’t worry, they will continue "
"to work."
msgstr "Las configuraciones previamente guardadas continuarán funcionando."

#: src/view/admin/cb_frame/settings/iab-page.php:108
msgid "Sharing data with third-party vendors"
msgstr "Compartición de datos con terceros vendedores"

#: src/view/admin/cb_frame/settings/iab-page.php:112
msgid ""
"Select vendors with whom you’ll share users’ data. We’ll include this "
"information on the second layer of your consent banner, where users "
"interested in more granular detail about who will access their data can view "
"it."
msgstr ""
"Selecciona los vendedores con los que compartirás los datos de los usuarios. "
"Esta información se incluirá en la segunda capa de tu banner de "
"consentimiento, donde los usuarios interesados en información más granular "
"sobre quién tiene acceso a sus datos podrán verlo."

#: src/view/admin/cb_frame/settings/iab-page.php:130
#: src/view/admin/cb_frame/settings/iab-page.php:162
#: src/view/admin/cb_frame/settings/iab-page.php:218
msgid "Search"
msgstr "Buscar"

#: src/view/admin/cb_frame/settings/iab-page.php:131
#: src/view/admin/cb_frame/settings/iab-page.php:163
msgid "Select All"
msgstr "Seleccionar todo"

#: src/view/admin/cb_frame/settings/iab-page.php:132
#: src/view/admin/cb_frame/settings/iab-page.php:164
msgid "Deselect All"
msgstr "Anular selección"

#: src/view/admin/cb_frame/settings/iab-page.php:133
msgid "Select at least one vendor"
msgstr "Seleccione al menos un vendedor"

#: src/view/admin/cb_frame/settings/iab-page.php:157
msgid "Google Ads certified external vendors"
msgstr "Vendedores externos certificados por Google Ads"

#: src/view/admin/cb_frame/settings/iab-page.php:188
msgid "Restrictions of data use purposes for vendors"
msgstr "Restricciones a los propósitos del uso de datos de los vendedores"

#: src/view/admin/cb_frame/settings/iab-page.php:192
msgid ""
"Set restrictions on data use purposes for specific vendors. Add vendors and "
"the data use purposes that each vendor is allowed. We’ll share this "
"information with users within your consent banner."
msgstr ""
"Impón restricciones a los propósitos del uso de datos para vendedores "
"específicos. Añade los vendedores y los propósitos de uso de datos a los que "
"cada vendedor tiene acceso. Esta información se compartirá con tus usuarios "
"en tu banner de consentimiento."

#: src/view/admin/cb_frame/settings/iab-page.php:198
msgid "Add Vendor"
msgstr "Añadir vendedor"

#: src/view/admin/cb_frame/settings/iab-page.php:204
#: src/view/admin/cb_frame/settings/iab-page.php:215
msgid "Select Vendor"
msgstr "Selecciona vendedor"

#: src/view/admin/cb_frame/settings/iab-page.php:228
msgid "Set Purposes"
msgstr "Configurar propósitos"

#: src/view/admin/cb_frame/settings/multiple-configuration/extra/region-item.php:18
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:75
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:101
msgid "Select region"
msgstr "Seleccionar región"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:16
msgid "Additional configurations:"
msgstr "Ajustes adicionales:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:18
msgid ""
"You can add a second alternative banner or configuration to your website by "
"creating a second Domain Group and specify it on a region."
msgstr ""
"Puedes añadir un segundo o configuración banner allternativo a tu sitio web "
"creando un segundo Grupo de Dominios y especificandolo en una región."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:22
msgid "Read more about multiple configurations here"
msgstr "Más inofrmación sobre configuraciones múltiples"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:34
msgid "Multiple configurations"
msgstr "Múltiples configuraciones"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:44
msgid "Set up your additional banner configuration:"
msgstr "Prepara tu configuración del banner adicional:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:47
msgid ""
"To enable a different configuration, create a separate DomainGroup without "
"adding the domain to it and paste the ID below. Then select the countries in "
"which you want to show this configuration. For example, if your main Domain "
"Group is defined as a banner matching GDPR requirements, you might want to "
"add another Domain Group for visitors from California. The number of "
"additional configurations is restricted to one at the moment."
msgstr ""
"Para permitir una configuración diferente, crea un Grupo de Dominios "
"separado sin añadir el dominio y pega la ID de debajo. Después, selecciona "
"los países en los que quieras mostrar esta configuración. Por ejemplo, si tu "
"Grupo de Dominio principal está preparado para cumplir con el RGPD, podrías "
"estar intereado en otro Grupo de Dominios para California. El número de "
"configuraciones adicionales está limitado a uno por el momento."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:57
msgid "Domain Group ID"
msgstr "ID del Grupo de Dominio"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:61
msgid "Region"
msgstr "Región"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:71
msgid "Primary domain group"
msgstr "Grupo de Dominio principal"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:138
msgid "Add banner"
msgstr "Añadir banner"

#: src/view/admin/cb_frame/support-page.php:23
msgid "Need help with your configuration?"
msgstr "¿Necesitas ayuda con tu configuración?"

#: src/view/admin/cb_frame/support-page.php:26
msgid ""
"In our Help Center you find all the answers to your questions. If you have "
"additional questions, create a support request and our Support Team will "
"help out as soon as possible."
msgstr ""
"En nuestro Centro de Asistencia encontrarás todas las respuestas a tus "
"preguntas. Si tienes más preguntas, crea una petición de ayuda y nuestro "
"Equipo de Atención al Cliente te ayudará lo más rápido posible."

#: src/view/admin/cb_frame/support-page.php:34
msgid "Visit Cookiebot CMP Help Center"
msgstr "Visita el Centro de Asistencia de Cookiebot CMP (en inglés)"

#: src/view/admin/cb_frame/support-page.php:58
msgid "How to find my Cookiebot™ ID"
msgstr "Cómo encuentro ¿mi ID de Cookiebot™?"

#. translators: the first placeholder string will be replaced with a html anchor open tag and the second placeholder string will be replaced by the html anchor closing tag
#: src/view/admin/cb_frame/support-page.php:66
msgid "Log in to your %1$sCookiebot CMP account%2$s."
msgstr "Inicia sesión en tu %1$scuenta de Cookiebot CMP%2$s."

#. translators: the placeholder strings denote the positions of <b>, </b>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:76
msgid "Go to %1$s\"Settings\"%2$s and setup your Cookiebot CMP"
msgstr "Dirígete a %1$s“Ajustes”%2$s y configura tu Cookiebot CMP"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:86
msgid "Go to the %1$s\"Your scripts\"%2$s tab"
msgstr "Ve a la pestaña %1$s“Tus scripts”%2$s"

#: src/view/admin/cb_frame/support-page.php:92
msgid ""
"Copy the value inside the data-cid parameter - eg.: abcdef12-3456-7890-abcd-"
"ef1234567890"
msgstr ""
"Copia el valor dentro del parámetro data-cid - p. ej..: abcdef12-3456-7890-"
"abcd-ef1234567890"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:97
msgid ""
"Add %1$s[cookie_declaration]%2$s shortcode to a page to show the declaration"
msgstr ""
"Añade el shortcode %1$s[cookie_declaration]%2$s a una página para mostrar la "
"declaración"

#: src/view/admin/cb_frame/support-page.php:103
msgid "Remember to change your scripts as described below"
msgstr "Recuerda cambiar tus scripts como se describe debajo"

#: src/view/admin/cb_frame/support-page.php:110
msgid "Add the Cookie Declaration to your website"
msgstr "Añade la declaración de cookies a tu sitio web"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:116
msgid ""
"Use the shortcode %1$s[cookie_declaration]%2$s to add the cookie declaration "
"to a page or post. The cookie declaration will always show the latest "
"version from Cookiebot CMP."
msgstr ""
"Usa el shortcode %1$s[cookie_declaration]%2$s para añadir la declaración de "
"cookies a una página o post. La declaración de cookies siempre se mostrará "
"como la versión más reciente de Cookiebot CMP."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:126
msgid ""
"If you want to show the cookie declaration in a specific language, you can "
"add the %1$s\"lang\"%2$s attribute, e.g. %3$s[cookie_declaration "
"lang=\"de\"]%4$s."
msgstr ""
"Si quieres mostrar la declaración de cookies en un idioma en particular, "
"puedes añadir el atributo %1$s“lang”%2$s, p. ej.: %3$s[cookie_declaration "
"lang=“es”]%4$s."

#: src/view/admin/cb_frame/support-page.php:138
msgid "Update your script tags"
msgstr "Actualiza tus etiquetas de script"

#: src/view/admin/cb_frame/support-page.php:142
msgid ""
"To enable prior consent, apply the attribute \"data-cookieconsent\" to "
"cookie-setting script tags on your website. Set the comma-separated value to "
"one or more of the cookie categories \"preferences\", \"statistics\" and/or "
"\"marketing\" in accordance with the types of cookies being set by each "
"script. Finally, change the attribute \"type\" from \"text/javascript\" to "
"\"text/plain\"."
msgstr ""
"Para habilitar el consentimiento previo, aplica el atributo “data-"
"cookieconsent” a los tags de script de cookies-setting en tu sitio web. "
"Establece el valor separado por comas a una o más de las categorías de "
"cookies, “preferencias”, “estadísticas” y/o “marketing”, de acuerdo con los "
"tipos de cookies establecidos por cada script. Finalmente, cambia el "
"atributo “type” de “text/javascript” a “text/plain”."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:152
msgid ""
"Example on modifying an existing Google Analytics Universal script tag can "
"be found %1$shere in step 4%2$s."
msgstr ""
"Puedes encontrar un ejemplo de cómo modificar un tag de script ya existente "
"de Google Analytics Universal %1$saquí en el paso 4%2$s."

#: src/view/admin/cb_frame/support-page.php:172
msgid "Helper function to update your scripts"
msgstr "Función de ayuda para actualizar tus scripts"

#: src/view/admin/cb_frame/support-page.php:176
msgid ""
"You can update your scripts yourself. However, Cookiebot CMP also offers a "
"small helper function that can make the work easier."
msgstr ""
"Puedes actualizar tus scripts por ti mismo. Sin embargo, desde Cookiebot CMP "
"también te ofrecemos un pequeña función de ayuda para facilitarte la vida."

#: src/view/admin/cb_frame/support-page.php:183
msgid "Update your script tags this way:"
msgstr "Actualiza tus etiquetas de script así:"

#. translators: %1$s refers to the original script tag HTML, and %2$s refers to its replacement
#: src/view/admin/cb_frame/support-page.php:188
msgid "%1$s to %2$s"
msgstr "%1$s a %2$s"

#: src/view/admin/common/dashboard-page.php:31
msgid "I already have an account"
msgstr "Ya tengo una cuenta"

#: src/view/admin/common/dashboard-page.php:35
msgid "Connect my account"
msgstr "Conectar mi cuenta"

#: src/view/admin/common/dashboard-page.php:48
#: src/view/admin/common/network-settings-page.php:33
#: src/view/admin/common/settings-page.php:37
msgid "New to our solutions? Create your account. "
msgstr "¿Eres nuevo aquí? Crea tu cuenta."

#: src/view/admin/common/dashboard-page.php:65
msgid "How to get started"
msgstr "Cómo empezar"

#: src/view/admin/common/dashboard-page.php:69
msgid "Learn more about your CMP"
msgstr "Descubrir más sobre CMP"

#: src/view/admin/common/network-settings-page.php:35
#: src/view/admin/common/settings-page.php:39
msgid ""
"If you’re new to our solutions, create an account first to obtain your "
"settings ID."
msgstr ""
"Si no conocías nuestras soluciones, crea primero una cuenta para obtener tu "
"ID."

#: src/view/admin/common/network-settings-page.php:40
#: src/view/admin/common/settings-page.php:44
msgid "Create your account"
msgstr "Crear una cuenta   "

#: src/view/admin/common/network-settings-page.php:49
#: src/view/admin/common/settings-page.php:52
#: src/view/admin/uc_frame/network-settings-page.php:51
msgid "Connect your account"
msgstr "Conectar tu cuenta"

#: src/view/admin/common/network-settings-page.php:52
#: src/view/admin/common/settings-page.php:55
msgid "Enter the ID of your account to quickly connect it with the plugin."
msgstr "Introduce el ID de tu cuenta para conectarla rápidamente con el plugin."

#: src/view/admin/uc_frame/network-settings-page.php:54
msgid "Enter your settings ID to quickly link your account with the plugin."
msgstr "Introduce tu ID de cuenta para vincular rápidamente tu cuenta con el plugin."

#: src/view/admin/common/network-settings-page.php:55
#: src/view/admin/uc_frame/network-settings-page.php:57
msgid ""
"If added this will be the default account for all subsites. Subsites are "
"able to override this and use their own account."
msgstr ""
"Si se añade, esta será la cuenta por defecto para todos los subsitios. Cada "
"subsitio puede anularla y utilizar su propia cuenta."

#: src/view/admin/common/network-settings-page.php:59
#: src/view/admin/common/settings-page.php:59
#: src/view/admin/uc_frame/network-settings-page.php:61
#: src/view/admin/uc_frame/settings/general-page.php:29
msgid "Where to find settings ID"
msgstr "Dónde encontrar el ID de cuenta"

#: src/view/admin/common/network-settings-page.php:63
#: src/view/admin/common/settings-page.php:63
msgid "How to find your Usercentrics Settings ID"
msgstr "Cómo encontrar tu ID de configuración de Usercentrics"

#: src/view/admin/common/network-settings-page.php:70
#: src/view/admin/common/settings-page.php:70
msgid "How to find your Cookiebot CMP Domain Group ID"
msgstr "Cómo encontrar tu ID de grupo de dominio CMP de Cookiebot"

#: src/view/admin/common/network-settings-page.php:65
#: src/view/admin/common/settings-page.php:65
msgid "Settings ID or Domain Group ID"
msgstr "ID de configuración o ID de grupo de dominio"

#: src/view/admin/uc_frame/network-settings-page.php:67
#: src/view/admin/uc_frame/settings/general-page.php:35
msgid "Your settings ID"
msgstr "Tu ID de cuenta"

#: src/view/admin/common/network-settings-page.php:82
#: src/view/admin/common/settings-page.php:82
#: src/view/admin/uc_frame/network-settings-page.php:83
#: src/view/admin/uc_frame/settings/general-page.php:70
msgid ""
"Let us know if your account is set for compliance with a single privacy law "
"(e.g. GDPR) or multiple laws (e.g. GDPR and CCPA) based on user’s location. "
"The default is a single privacy law, so this is likely your setting unless "
"modified."
msgstr ""
"Indícanos si tu cuenta está configurada para cumplir una única ley de "
"privacidad (por ejemplo, el RGPD) o varias leyes (el RGPD y la CCPA) en "
"función de la ubicación del usuario. Por defecto, se aplica una única ley de "
"privacidad, por lo que es probable que esa sea la configuración, a menos que "
"se modifique."

#: src/view/admin/common/network-settings-page.php:88
#: src/view/admin/common/settings-page.php:88
#: src/view/admin/uc_frame/network-settings-page.php:89
#: src/view/admin/uc_frame/settings/general-page.php:76
msgid "Your current account setup:"
msgstr "La configuración actual de tu cuenta:"

#: src/view/admin/common/network-settings-page.php:95
#: src/view/admin/common/settings-page.php:95
#: src/view/admin/uc_frame/network-settings-page.php:96
#: src/view/admin/uc_frame/settings/general-page.php:83
msgid "Compliance with one privacy law"
msgstr "Cumplimiento de una normativa de privacidad"

#: src/view/admin/common/network-settings-page.php:102
#: src/view/admin/common/settings-page.php:102
#: src/view/admin/uc_frame/network-settings-page.php:103
#: src/view/admin/uc_frame/settings/general-page.php:90
msgid "Compliance with multiple privacy laws (geolocation)"
msgstr "Cumplimiento de varias normativas de privacidad (geolocalización)"

#: src/view/admin/common/prior-consent/available-addons/tab.php:25
msgid "Plugin enabled"
msgstr "Plugin activo"

#: src/view/admin/common/prior-consent/available-addons/tab.php:58
msgid "Placeholder text:"
msgstr "Texto del marcador:"

#: src/view/admin/common/prior-consent/available-addons/tab.php:67
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:53
msgid "Display a placeholder"
msgstr "Mostrar un marcador"

#: src/view/admin/common/prior-consent/available-addons/tab.php:78
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:71
msgid "+ Add language"
msgstr "+ Añadir idioma"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:7
msgid "Jetpack settings"
msgstr "Ajustes de Jetpack"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:8
msgid "Enable Jetpack on \"Available Addons\" to see this page options."
msgstr ""
"Activa Jetpack en “Addons disponibles” para ver las opciones de esta página."

#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:15
msgid "Enable"
msgstr "Activar"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:11
msgid "Show advanced options"
msgstr "Mostrar opciones avanzadas"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:17
msgid "This is for more advanced users."
msgstr "Para usuarios avanzados"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:23
msgid "Regex:"
msgstr "Regex:"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:35
msgid "Edit regex"
msgstr "Editar regex"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:49
msgid "Reset to default regex"
msgstr "Restaurar regex por defecto"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:4
msgid "Information"
msgstr "Información"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:5
msgid ""
"These add-ons are created by a dedicated open-source community to make it "
"easier for you to manage cookie and tracker consent on your WordPress site. "
"They’re designed to help you ensure ‘prior consent’ even for plugins that "
"don’t include this feature."
msgstr ""
"Estos complementos han sido creados por una comunidad de código abierto para "
"facilitar la gestión del consentimiento de cookies y rastreadores en tu web "
"de WordPress. Están diseñados para ayudarte a garantizar el «consentimiento "
"previo» incluso en plugins que no incluyen esta función."

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:6
msgid ""
"Right now, these add-ons are the best way for you to signal user consent to "
"other plugins. While we don’t know if or when WordPress Core will add this "
"functionality, these tools are here to support you and work seamlessly with "
"Usercentrics solution."
msgstr ""
"Actualmente, estos complementos son la mejor forma de transmitir el "
"consentimiento del usuario a otros plugins. Aunque no sabemos si WordPress "
"Core añadirá esta funcionalidad o cuándo lo hará, estas herramientas están "
"aquí para ayudarte y adaptarse perfectamente a la solución de Usercentrics."

#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:10
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:19
#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:26
msgid "Language"
msgstr "Idioma"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:4
msgid "Unavailable plugins"
msgstr "Plugins no disponibles"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:5
msgid ""
"The following addons are unavailable. This is because the corresponding "
"plugin is not installed or activated."
msgstr ""
"Los siguientes add-ons no están disponibles. Esto es porque el plugin "
"correspondiente no está instalado o activado."

#: src/view/admin/common/support-page.php:23
#: src/view/admin/uc_frame/support-page.php:23
msgid "Need help?"
msgstr "¿Necesitas ayuda?"

#: src/view/admin/common/support-page.php:26
#: src/view/admin/uc_frame/support-page.php:26
msgid ""
"Visit our Support Center to find answers to your questions or get help with "
"configuration. If you need further assistance, use the Contact Support "
"button in the top navigation to create a support request. We’ll respond as "
"soon as possible."
msgstr ""
"Visita nuestro centro de ayuda para responder a tus preguntas u obtener "
"ayuda con la configuración. Si necesita más asistencia, utiliza el botón "
"«Contactar con asistencia» de la barra de navegación superior para crear una "
"solicitud. Te responderemos lo antes posible."

#: src/view/admin/common/support-page.php:34
#: src/view/admin/uc_frame/support-page.php:34
msgid "Go to Support Center"
msgstr "Ir al centro de ayuda"

#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:6
msgid "You will need to add a new ID before updating other settings"
msgstr "Deberás añadir un nuevo ID antes de actualizar otros ajustes."

#: src/view/admin/common/templates/extra/review-form.php:10
msgid "Cookiebot CMP Deactivation"
msgstr "Desactivación de Cookiebot CMP"

#: src/view/admin/common/templates/extra/review-form.php:14
msgid "We are sad to lose you. Take a moment to help us improve?"
msgstr "Sentimos que te vayas. ¿Puedes ayudarnos a mejorar?"

#: src/view/admin/common/templates/extra/review-form.php:19
msgid "The installation is too complicated"
msgstr "La instalación es muy complicada"

#: src/view/admin/common/templates/extra/review-form.php:26
msgid "I found a plugin that better serves my needs"
msgstr "He encontrado un plugin que responde mejor a mis necesidades"

#: src/view/admin/common/templates/extra/review-form.php:33
msgid "Missing features / did not meet my expectations"
msgstr "Faltan funciones / no cumple mis expectativas"

#: src/view/admin/common/templates/extra/review-form.php:40
msgid "I need more customization options"
msgstr "Necesito más opciones de personalización"

#: src/view/admin/common/templates/extra/review-form.php:47
msgid "The premium plan is too expensive"
msgstr "El plan Premium es demasiado caro"

#: src/view/admin/common/templates/extra/review-form.php:54
msgid "I’m only deactivating the plugin temporarily"
msgstr "Sólo desactivaré el plugin temporalmente."

#: src/view/admin/common/templates/extra/review-form.php:61
msgid "Other"
msgstr "Otros"

#: src/view/admin/common/templates/extra/review-form.php:65
msgid "Please specify here"
msgstr "Especifica más, por favor"

#: src/lib/Cookiebot_Review.php:105
msgid "(Optional)"
msgstr "(Opcional)"

#: src/lib/Cookiebot_Review.php:106
msgid " By checking this box, you agree to submit troubleshooting information and allow us to contact you regarding the problem if necessary."
msgstr " Al marcar esta casilla, aceptas enviar información sobre la resolución de problemas y nos permites ponernos en contacto contigo en relación con este asunto si fuera necesario."

#: src/lib/Cookiebot_Review.php:110
msgid "The information will be kept for no longer than 90 days. You may revoke this consent at any time, e.g. by sending an email to "
msgstr "La información no se conservará más de 90 días. Puedes revocar este consentimiento en cualquier momento, por ejemplo, enviando un correo electrónico a "

#: src/lib/Cookiebot_Review.php:117
msgid "Skip and Deactivate"
msgstr "Saltar y Desactivar"

#: src/lib/Cookiebot_Review.php:118
msgid "Submit and Deactivate"
msgstr "Enviar y Desactivar"

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:6
msgid ""
"If there is a network settings ID connected it will be used for this subsite, "
"if not you will need to add a new ID before updating other settings"
msgstr ""
"Si hay un ID de cuenta de red conectado, se utilizará para este subsitio; si "
"no, tendrá que añadir un nuevo ID antes de actualizar otros ajustes."

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:13
msgid "Disconnect subsite account"
msgstr "Desconectar cuenta del subsitio"

#: src/view/admin/common/templates/header.php:10
msgid "Changes has been saved"
msgstr "Los cambios se han guardado"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:13
msgid "Absolutely, you deserve it!"
msgstr "¡Por supuesto!"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:24
msgid "Maybe later?"
msgstr "En otro momento"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:35
msgid "I`ve already left my feedback"
msgstr "Ya he dejado mi reseña"

#: src/view/admin/uc_frame/dashboard-page.php:30
msgid "Welcome to Usercentrics Cookiebot WordPress Plugin"
msgstr "Bienvenido al plugin de Usercentrics Cookiebot para WordPress"

#: src/view/admin/uc_frame/dashboard-page.php:38
msgid ""
"You’ve added your settings ID to your Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Has añadido tu ID de cuenta al plugin de Usercentrics Cookiebot para "
"WordPress."

#: src/view/admin/uc_frame/dashboard-page.php:51
msgid ""
"Are you happy with Usercentrics Cookiebot WordPress Plugin? Your feedback "
"helps us improve it."
msgstr ""
"¿Te ha gustado el plugin de Usercentrics Cookiebot para WordPress? Tus "
"comentarios nos ayudan a mejorar."

#: src/view/admin/uc_frame/dashboard-page.php:55
msgid "Share feedback"
msgstr "Compartir feedback"

#: src/view/admin/uc_frame/dashboard-page.php:68
msgid "How to set up Usercentrics Cookiebot WordPress Plugin"
msgstr "Cómo configurar el plugin de Usercentrics Cookiebot para WordPress"

#: src/view/admin/uc_frame/dashboard-page.php:72
#: src/view/admin/uc_frame/settings/gcm-page.php:21
#: src/view/admin/uc_frame/settings/general-page.php:127
#: src/view/admin/uc_frame/settings/gtm-page.php:18
msgid "Learn more"
msgstr "Más información"

#: src/view/admin/uc_frame/dashboard-page.php:115
msgid "Visit our blog to learn about other legislations"
msgstr "Visita nuestro blog para conocer otras normativas"

#: src/view/admin/uc_frame/debug-page.php:22
msgid "Debug your plugin"
msgstr "Eliminar errores del plugin"

#: src/view/admin/uc_frame/debug-page.php:25
msgid ""
"If you encounter any issues with your Usercentrics Cookiebot WordPress "
"Plugin, provide the information below to help us assist you. Visit our "
"Support Center and send us a copy of what is displayed in the window below."
msgstr ""
"Si tienes algún problema con el plugin de Usercentrics Cookiebot para "
"WordPress, proporciona la siguiente información para ayudarnos a resolverlo. "
"Visita nuestro centro de ayuda y envíanos una copia de lo que se muestra en "
"la ventana que aparece a continuación."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:23
msgid "WP Consent API Settings"
msgstr "Configuración de la API de consentimiento de WP"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:32
msgid ""
"WP Consent API and Usercentrics Cookiebot WordPress Plugin categorize "
"cookies a bit differently. The default settings should fit most needs, but "
"if you need to change the mapping you can do so below."
msgstr ""
"La API de Consentimiento de WP y el plugin de Usercentrics Cookiebot para "
"WordPress categorizan las cookies de modo diferente. La configuración "
"predeterminada debería ajustarse a la mayoría de las necesidades, pero si "
"necesitas cambiar la aisgnación puedes hacerlo a continuación."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:48
msgid "Usercentrics Cookiebot cookie categories"
msgstr "Categorías de cookies de Usercentrics Cookiebot"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:58
msgid "essential"
msgstr "Esenciales"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:72
msgid "functional"
msgstr "Funcionales"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:106
msgid "Reset to default categories"
msgstr "Restablecer categorías predeterminadas"

#: src/view/admin/uc_frame/settings-page.php:48
msgid "Save changes"
msgstr "Guardar cambios"

#: src/view/admin/uc_frame/settings-page.php:70
#: src/view/admin/uc_frame/settings/embeddings-page.php:4
msgid "Privacy Policy Sync"
msgstr "Sincronización de la política de privacidad"

#: src/view/admin/uc_frame/settings/embeddings-page.php:7
msgid ""
"Use our pre-defined, automatically generated embeddings to help you keep "
"your Privacy Policy page in sync with your consent banner settings. This "
"feature saves you time by automatically updating legally required "
"information, so you don’t need to manually copy data into your Privacy "
"Policy page. Once you’re done setting the options below, simply copy the "
"code and paste it into your Privacy Policy page."
msgstr ""
"Utiliza nuestras incrustaciones predefinidas y generadas automáticamente "
"para mantener tu política de privacidad sincronizada con la configuración de "
"tu banner de consentimiento. Con esta función podrás ahorrar tiempo, ya que "
"la información exigida por ley se actualiza automáticamente, sin necesidad "
"de copiar manualmente los datos en tu política de privacidad. Una vez que "
"hayas configurado las opciones que aparecen a continuación, sólo tienes que "
"copiar el código y pegarlo en tu página de política de privacidad."

#: src/view/admin/uc_frame/settings/embeddings-page.php:12
msgid "Copy shortcode"
msgstr "Copiar código corto"

#: src/view/admin/uc_frame/settings/embeddings-page.php:23
msgid "Sync options for privacy legislations"
msgstr "Opciones de sincronización para la normativa de privacidad"

#: src/view/admin/uc_frame/settings/embeddings-page.php:26
msgid ""
"Select the legislation you want to automatically sync with your Privacy "
"Policy page."
msgstr ""
"Selecciona la normativa que deseas sincronizar automáticamente con tu página "
"de política de privacidad."

#: src/view/admin/uc_frame/settings/embeddings-page.php:41
msgid "Sync options for data processing services (DPS) "
msgstr ""
"Opciones de sincronización para los servicios de procesamiento de datos (DPS)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:44
msgid ""
"Define what to include on your Privacy Policy page: DPS categories only, "
"categories with their services, a single service, or detailed information on "
"both categories and services. Choose based on the level of detail you want "
"to display."
msgstr ""
"Define qué incluir en tu página de política de privacidad: solo categorías "
"DPS, categorías con sus servicios, un solo servicio o información detallada "
"tanto de categorías como de servicios. Elige en función del nivel de detalle "
"que quieras mostrar."

#: src/view/admin/uc_frame/settings/embeddings-page.php:50
msgid "Services (Default)"
msgstr "Servicios (por defecto)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:51
msgid "Categories and services"
msgstr "Categorías y servicios"

#: src/view/admin/uc_frame/settings/embeddings-page.php:52
msgid "Categories only"
msgstr "Solo categorías"

#: src/view/admin/uc_frame/settings/embeddings-page.php:53
msgid "Single service"
msgstr "Servicio único"

#: src/view/admin/uc_frame/settings/embeddings-page.php:54
msgid "Purposes"
msgstr "Objetivos"

#: src/view/admin/uc_frame/settings/embeddings-page.php:55
msgid "Vendors"
msgstr "Proveedores"

#: src/view/admin/uc_frame/settings/embeddings-page.php:63
msgid "Single Service ID"
msgstr "ID de servicio único"

#: src/view/admin/uc_frame/settings/embeddings-page.php:66
msgid "Add the service ID that you want to display."
msgstr "Añade el ID del servicio que quieras mostrar."

#: src/view/admin/uc_frame/settings/embeddings-page.php:69
msgid "This feature is required."
msgstr "Esta función es obligatoria."

#: src/view/admin/uc_frame/settings/embeddings-page.php:81
msgid "Privacy toggles"
msgstr "Toggles de privacidad"

#: src/view/admin/uc_frame/settings/embeddings-page.php:84
msgid ""
"Define whether you want the privacy toggles to be enabled and displayed on "
"your Privacy Policy page."
msgstr ""
"Determina si quieres que los toggles de privacidad se activen y se muestren "
"en tu página de política de privacidad."

#: src/view/admin/uc_frame/settings/embeddings-page.php:92
msgid "Enable privacy toggles"
msgstr "Habilitar toggles de privacidad"

#: src/view/admin/uc_frame/settings/gcm-page.php:14
msgid "Integration with Google Consent Mode"
msgstr "Integración con el modo de consentimiento de Google"

#: src/view/admin/uc_frame/settings/gcm-page.php:17
msgid ""
"The Usercentrics Cookiebot WordPress Plugin and Google Consent Mode "
"integrate seamlessly, providing plug-and-play privacy compliance and "
"effortless use of all Google services in one solution."
msgstr ""
"El plugin de Usercentrics Cookiebot para WordPress se integra a la "
"perfección con el modo de consentimiento de Google, ofreciendo un "
"cumplimiento de la privacidad inmediato y un uso eficiente de todos los "
"servicios de Google en una única solución"

#: src/view/admin/uc_frame/settings/gcm-page.php:31
msgid ""
"Enable Google Consent Mode integration within your Usercentrics Cookiebot "
"WordPress Plugin."
msgstr ""
"Habilita la integración del modo de consentimiento de Google en tu plugin de "
"Usercentrics Cookiebot para WordPress."

#: src/view/admin/uc_frame/settings/general-page.php:22
msgid "Settings ID"
msgstr "ID de cuenta"

#: src/view/admin/uc_frame/settings/general-page.php:25
msgid ""
"To disconnect your account, enter your settings ID into the field and confirm "
"with the button."
msgstr ""
"Para desconectar tu cuenta, introduce tu ID de cuenta en el campo y confirma "
"pulsando el botón."

#: src/view/admin/uc_frame/settings/general-page.php:99
msgid "TCF integration"
msgstr "Integración del TCF"

#: src/view/admin/uc_frame/settings/general-page.php:102
msgid "Enable the integration with the latest version of IAB TCF."
msgstr "Habilita la integración con la última versión del TCF de la IAB."

#: src/view/admin/uc_frame/settings/general-page.php:111
msgid "IAB TCF integration"
msgstr "Integración del TCF de la IAB"

#: src/view/admin/uc_frame/settings/gtm-page.php:14
msgid ""
"Enable Google Tag Manager integration to streamline tracking tags with your "
"Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Habilita la integración de Google Tag Manager para optimizar las etiquetas "
"de seguimiento con tu plugin Usercentrics Cookiebot para WordPress."

#: src/view/admin/uc_frame/settings/gtm-page.php:37
msgid "Enter your Google Tag Manager ID for seamless integration."
msgstr "Introduce tu ID de Google Tag Manager para una integración perfecta."

#: src/view/admin/uc_frame/settings/gtm-page.php:48
msgid "GTM-XXXXXXX"
msgstr "GTM-XXXXXXX"

#: src/view/admin/uc_frame/settings/gtm-page.php:56
msgid "Data layer name (only if changed)"
msgstr "Nombre de la capa de datos (sólo si se modifica)"

#: src/view/admin/uc_frame/settings/gtm-page.php:58
msgid ""
"The default name for the data layer in Google Tag Manager is ‘dataLayer’. If "
"you renamed it, enter the new name. Otherwise, leave this field empty."
msgstr ""
"El nombre por defecto de la capa de datos en Google Tag Manager es "
"‘dataLayer’. Si lo modificaste, introduce el nuevo nombre. Si no es así, "
"deja este campo vacío."

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:14
msgid "Title"
msgstr "Título"

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:33
msgid "- Default -"
msgstr "- Predeterminado -"

#: src/widgets/Cookiebot_Declaration_Widget.php:17
msgid "Cookiebot - Cookie Declaration"
msgstr "Cookiebot -Declaración de cookies"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:23
msgid "Cookiebot Status"
msgstr "Estado de Cookiebo"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:39
msgid "You need to enter your Cookiebot ID."
msgstr "Debes ingresar tu ID de Cookiebot."

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:41
msgid "Update your Cookiebot ID"
msgstr "Actualiza tu ID de Cookiebot"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:44
msgid "Your Cookiebot is working!"
msgstr "¡Cookiebot está funcionando!"

#~ msgid ""
#~ "We hope you enjoy using WordPress Cookiebot! Would you consider leaving "
#~ "us a review on WordPress.org?"
#~ msgstr ""
#~ "¡Esperamos que estés disfrutando de WordPress Cookiebot™! ¿Nos dejarías "
#~ "una reseña en WordPress.org?"

#~ msgid "Legislations"
#~ msgstr "Legislaciones"

#~ msgid "Sure! I'd love to!"
#~ msgstr "¡Me encantaría!"

#~ msgid "I've already left a review"
#~ msgstr "Ya he dejado una reseña"

#~ msgid "Maybe Later"
#~ msgstr "Quizás más tarde"

#~ msgid "Never show again"
#~ msgstr "No volver a mostrar"

#~ msgid "TCF version:"
#~ msgstr "Versión del TCF"

#~ msgid ""
#~ "In May 2023 The Interactive Advertising Bureau (IAB) announced the latest "
#~ "version of its Transparency and Consent Framework (TCF), or TCF v2.2, "
#~ "which must be implemented by all consent management platforms (CMPs) by "
#~ "November 20, 2023. We will migrate you automatically on November 20,2023, "
#~ "but we recommend to do it manually before. To manually switch the version "
#~ "before please select it on the right."
#~ msgstr ""
#~ "En mayo del 2023, el Interactive Advertising Buraeu (IAB) anunció la "
#~ "última versión de su Marco de Transparencia y Consentimiento (TCF, por "
#~ "sus siglas en inglés), o TCF v2.2, que debe ser implementado en todas las "
#~ "plataformas de gestión del consentimiento (CMP) antes del 20 de noviembre "
#~ "del 2023. Te migraremos automáticamente en esa fecha, pero aun así te "
#~ "recomendamos que los hagas manualmente antes. Para cambiar la versión "
#~ "manualmente con anterioridad, selecciónalo a la derecha."

#~ msgid "Select the TCF Version below"
#~ msgstr "Selecciona la versión del TCF"

#~ msgid "New"
#~ msgstr "Nuevo"

#~ msgid "Create a new Account"
#~ msgstr "Crear una nueva Cuenta"

#~ msgid "Get help with connecting your account"
#~ msgstr "Consigue ayuda conectando tu cuenta"

#~ msgid "Select the Cookie-blocking mode"
#~ msgstr "Selecciona el modo de bloqueo de cookies"

#~ msgid "Automatic cookie-blocking mode"
#~ msgstr "Modo de bloqueo de cookies automático"

#~ msgid "Manual cookie-blocking mode"
#~ msgstr "Modo de bloqueo de cookies manual"

#~ msgid "Depending on Cookie-blocking mode"
#~ msgstr "En función del modo de bloqueo de cookies"

#~ msgid "Auto-update Cookiebot™ Plugin:"
#~ msgstr "Actualización automática del Plugin de Cookiebot™"

#~ msgid ""
#~ "Automatically update your Cookiebot™ plugin when new releases becomes "
#~ "available."
#~ msgstr ""
#~ "Actualiza tu plugin de Cookiebot™ automáticamente cuando se publique una "
#~ "nueva versión."

#~ msgid ""
#~ "These add-ons are produced by an open-source community of developers. "
#~ "This is done to help make it easier for WordPress users to implement "
#~ "‘prior consent’ for cookies and trackers set by plugins that do not offer "
#~ "this as a built-in function. The add-ons are currently the best "
#~ "alternative to a WordPress Core framework that can signal the user’s "
#~ "consent state to other plugins (if and when this will be implemented is "
#~ "unknown) and to those plugins that do not yet offer native support for "
#~ "Cookiebot CMP. "
#~ msgstr ""
#~ "Estos add-ons son creados por una comunidad de desarroladores de código "
#~ "abierto. Esto se hace para facilitar a los usuarios de WordPress la "
#~ "implementación de un “consentimiento previo” para cookies y rastreadores "
#~ "instalados en plugins que no ofrecen esta función por sí mismos. Estos "
#~ "add-ons son actualmente la mejor alternativa a un marco Core de WordPress "
#~ "que podría enviar el estado del consentimiento del usuario a otros "
#~ "plugins, pero que todavía se desconoce cuándo se implementará, si es que "
#~ "se implementa; y para los plugins que no ofrecen todavía un soporte "
#~ "nativo para Cookiebot CMP."

#~ msgid "Do you not have an account yet?"
#~ msgstr "¿Todavía no tienes una cuenta?"

#~ msgid ""
#~ "Before you can get started with Cookiebot CMP for WordPress, you need to "
#~ "create an account on our website by clicking on \"Create a new account\" "
#~ "below. After you have signed up, you can configure your banner in the "
#~ "Cookiebot Manager and then place the Cookiebot Domain Group ID in the "
#~ "designated field below. You can find your ID in the Cookiebot Manager by "
#~ "navigating to \"Settings\" and \"Your Scripts\"."
#~ msgstr ""
#~ "Antes de poder empezar con Cookiebot CMP para WordPress, tienes que crear "
#~ "una cuenta en nuestro sitio web clicando debajo en “Crear una nueva "
#~ "cuenta”. Tras registrarte, puedes configurar el banner en el "
#~ "Administrador de Cookiebot y después poner la ID del Grupo de Dominios en "
#~ "el campo designado debajo. Puedes encontrar tu ID en el Administrador de "
#~ "Cookiebot navegando a “Ajustes” y “Tus scripts”."

#~ msgid "Depending on cookie-blocking mode"
#~ msgstr "En función del modo de bloqueo de cookies"

#~ msgid "Cookiebot CMP in WP Admin:"
#~ msgstr "Cookiebot CMP en el Admin de WP:"

#~ msgid ""
#~ "This checkbox will disable Cookiebot CMP to act within the WordPress "
#~ "Admin area"
#~ msgstr ""
#~ "Esta casilla no permitirá a Cookiebot CMP actuar en el área de Admin de "
#~ "WordPress"

#~ msgid "Disable Cookiebot CMP in the WordPress Admin area"
#~ msgstr "Inhabilitar Cookiebot CMP en el área de Admin de WordPress"

#~ msgid "Cookiebot CMP on front-end while logged in:"
#~ msgstr "Cookiebot CMP en la interfaz cuando estés conectado:"

#~ msgid ""
#~ "This setting will enable Cookiebot CMP on the front-end while you're "
#~ "logged in."
#~ msgstr ""
#~ "Este ajuste permitirá a Cookiebot CMP en la interfaz mientras estés "
#~ "conectado."

#~ msgid "Render Cookiebot CMP on front-end while logged in"
#~ msgstr "Activar Cookiebot CMP en la interfaz mientras estoy conectado"

#~ msgid "Watch video demonstration"
#~ msgstr "Consulta el vídeo de demostración"
