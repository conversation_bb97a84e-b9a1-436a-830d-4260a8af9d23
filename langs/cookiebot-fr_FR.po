msgid ""
msgstr ""
"Project-Id-Version: Cookiebot | GDPR/CCPA Compliant <PERSON><PERSON> and "
"Control\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/cookiebot\n"
"POT-Creation-Date: 2024-12-18T18:13:49+00:00\n"
"PO-Revision-Date: 2025-01-13 10:37+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: fr_FR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: cookiebot.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"

#. Plugin Name of the plugin
#: cookiebot.php
msgid "Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: cookiebot.php
msgid "https://www.cookiebot.com/"
msgstr ""

#. Description of the plugin
#: cookiebot.php
msgid ""
"The Cookiebot CMP WordPress cookie banner and cookie policy help you comply "
"with the major data protection laws (GDPR, ePrivacy, CCPA, LGPD, etc.) in a "
"simple and fully automated way. Secure your website and get peace of mind."
msgstr ""

#. Author of the plugin
#: cookiebot.php
msgid "Usercentrics A/S"
msgstr ""

#: src/addons/config/Settings_Config.php:89
#: src/addons/config/Settings_Config.php:90
#: src/view/admin/common/prior-consent/page.php:24
#: src/view/admin/common/templates/main-tabs.php:39
msgid "Plugins"
msgstr "Plugins"

#: src/addons/config/Settings_Config.php:130
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:25
msgid "Remove language"
msgstr "Supprimer la langue"

#: src/addons/config/Settings_Config.php:602
msgid "The plugin is not installed."
msgstr "Le plugin n’est pas installé."

#: src/addons/config/Settings_Config.php:605
msgid "The theme is not installed."
msgstr "Le thème n’est pas installé."

#: src/addons/config/Settings_Config.php:609
msgid "The plugin is not activated."
msgstr "Le plugin n’est pas activé."

#: src/addons/config/Settings_Config.php:612
msgid "The theme is not activated."
msgstr "Le thème n’est pas activé."

#: src/addons/config/Settings_Config.php:672
#: src/lib/Cookiebot_Admin_Links.php:54 src/lib/Cookiebot_Admin_Links.php:56
#: src/lib/Cookiebot_Admin_Links.php:124
msgid "%s"
msgstr "%s"

#: src/addons/config/Settings_Config.php:708
#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:18
#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:19
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:40
msgid "Info"
msgstr "Informations"

#: src/addons/config/Settings_Config.php:715
msgid "Available Add-ons"
msgstr "Modules complémentaires disponibles"

#: src/addons/config/Settings_Config.php:721
msgid "Unavailable Add-ons"
msgstr "Modules complémentaires non disponibles"

#: src/addons/config/Settings_Config.php:738
msgid "Jetpack"
msgstr "Jetpack"

#: src/addons/config/Settings_Config.php:749
msgid "WP Consent API"
msgstr "API de consentement WP"

#: src/addons/controller/addons/add_to_any/Add_To_Any.php:86
#: src/addons/controller/addons/embed_autocorrect/Embed_Autocorrect.php:443
msgid "Blocks embedded videos from Youtube, Twitter, Vimeo and Facebook."
msgstr "Bloque les vidéos intégrées de Youtube, Twitter, Vimeo et Facebook."

#: src/addons/controller/addons/enfold/Enfold.php:29
msgid "Blocks cookies created by Enfold theme's Google Services."
msgstr "Bloque les cookies créés par les services Google du thème Enfold."

#: src/addons/controller/addons/enhanced_ecommerce_for_woocommerce_store/Enhanced_Ecommerce_For_WooCommerce_Store.php:43
msgid "Blocks enhanced e-commerce for WooCommerce store"
msgstr "Bloque le e-commerce optimisé pour la boutique WooCommerce"

#: src/addons/controller/addons/google_analytics/Google_Analytics.php:36
msgid ""
"Google Analytics is used to track how visitor interact with website content."
msgstr ""
"Google Analytics est utilisé pour déterminer comment les visiteurs "
"interagissent avec le contenu du site web."

#: src/addons/controller/addons/google_analytics_plus/Google_Analytics_Plus.php:32
msgid ""
"Google Analytics is a simple, easy-to-use tool that helps website owners "
"measure how users interact with website content"
msgstr ""
"Google Analytics est un outil simple et facile à utiliser qui aide les "
"propriétaires de sites web à mesurer la façon dont les utilisateurs "
"interagissent avec le contenu du site."

#: src/addons/controller/addons/google_site_kit/Google_Site_Kit.php:33
msgid "Blocks Google Analytics scripts"
msgstr "Bloque les scripts Google Analytics"

#: src/addons/controller/addons/jetpack/widget/Facebook_Jetpack_Widget.php:71
msgid "Facebook widget."
msgstr "Facebook widget"

#: src/addons/controller/addons/litespeed_cache/Litespeed_Cache.php:51
msgid ""
"Excludes cookiebot javascript files when the Litespeed Cache deter option is "
"enabled."
msgstr ""
"Exclut les fichiers javascript de cookiebot lorsque l’option  « Litespeed "
"Cache deter » est activée."

#: src/addons/controller/addons/official_facebook_pixel/Official_Facebook_Pixel.php:247
msgid "Blocks Official Meta Pixel scripts"
msgstr "Bloque les scripts officiels Meta Pixel"

#: src/addons/controller/addons/optinmonster/Optinmonster.php:32
msgid ""
"OptinMonster API plugin to connect your WordPress site to your OptinMonster "
"account."
msgstr ""
"Le plugin OptinMonster API pour connecter votre site WordPress à votre "
"compte OptinMonster."

#: src/addons/controller/addons/simple_share_buttons_adder/Simple_Share_Buttons_Adder.php:32
msgid "Blocks Simple Share Buttons Adder."
msgstr "Blocque Simple Share Buttons Adder."

#: src/addons/controller/addons/wpforms/Wpforms.php:61
msgid ""
"If the user gives correct consent, IP and Unique User ID will be saved on "
"form submissions, otherwise not."
msgstr ""
"Si, et seulement si, l’utilisateur donne un consentement correct, l’IP et "
"l’identifiant d’utilisateur unique seront enregistrés lors de la "
"transmission de formulaire."

#: src/addons/controller/addons/wpforms/Wpforms.php:62
msgid "Increases opt-in rate compared to WPForms \"GDPR mode\"."
msgstr "Augmente le taux d’opt-in par rapport au « mode RGPD » de WPForms."

#: src/addons/controller/addons/wp_google_analytics_events/Wp_Google_Analytics_Events.php:46
msgid ""
"The plugin allows you to fire events whenever someone interacts or views "
"elements on your website."
msgstr ""
"Ce plugin vous permet de lancer des événements chaque fois qu’une personne "
"interagit ou consulte des éléments sur votre site web."

#: src/addons/controller/addons/wp_rocket/Wp_Rocket.php:48
msgid ""
"Excludes cookiebot javascript files when the WP-Rocket deter option is "
"enabled."
msgstr ""
"Exclut les fichiers javascript de cookiebot lorsque l’option « WP-Rocket "
"deter » est activée"

#: src/addons/controller/addons/wp_seopress/Wp_Seopress.php:41
msgid "Blocks cookies from WP SEOPress' Google Analytics integration."
msgstr "Bloque les cookies de l’intégration Google Analytics de WP SEOPress."

#: src/addons/controller/Plugin_Controller.php:50
msgid "You enabled Cookiebot™ auto blocking mode but still using addons"
msgstr ""
"Vous avez activé le mode de blocage automatique de Cookiebot mais vous "
"utilisez toujours des modules complémentaires"

#: src/addons/controller/Plugin_Controller.php:55
msgid ""
"In some occasions this may cause client side errors. If you notice any "
"errors please try to disable Cookiebot™ addons or contact Cookiebot™ support."
msgstr ""
"Dans certains cas, cela peut entraîner des erreurs côté client. Si vous "
"remarquez des erreurs, essayez de désactiver les modules complémentaires "
"Cookiebot ou contactez le support Cookiebot."

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:84
msgid "Share your experience"
msgstr "Partagez votre expérience"

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:88
msgid ""
"Hi there! We are thrilled you love the Cookiebot CMP plugin. Could you do us "
"a huge favor and leave a 5-star rating on WordPress? Your support will help "
"us spread the word and empower more WordPress websites to meet GDPR and CCPA "
"compliance standards effortlessly. Thank you for your support!"
msgstr ""
"Bonjour ! Tout d'abord, sachez que nous sommes ravis que le plugin Cookiebot "
"CMP vous plaise. Pourriez-vous nous donner un petit coup de pouce en "
"laissant une évaluation de 5 étoiles sur WordPress ? Cela nous aidera à "
"faire connaître notre solution et permettra à davantage de sites WordPress "
"de se conformer facilement aux normes RGPD et CCPA. Merci pour votre "
"soutien !"

#: src/admin_notices/Cookiebot_Temp_Notice.php:43
msgid ""
"Cookiebot CMP Plugin will soon no longer support PHP 5. If your website "
"still runs on this version we recommend upgrading so you can continue "
"enjoying the features Cookiebot CMP offers."
msgstr ""
"Le plugin Cookiebot CMP ne prendra bientôt plus en charge PHP 5. Si votre "
"site utilise encore cette version, nous vous recommandons de le mettre à "
"jour pour continuer à profiter des fonctionnalités offertes par Cookiebot "
"CMP."

#: src/lib/Cookiebot_Review.php:82
msgid "Sorry you are not allowed to do this."
msgstr "Action non autorisée."

#: src/lib/Cookiebot_Review.php:85
#: src/view/admin/common/templates/extra/review-form.php:94
msgid "Please select one option"
msgstr "Veuillez sélectionner une option"

#. translators: The placeholder is for the COOKIEBOT_MIN_PHP_VERSION constant
#: src/lib/Cookiebot_WP.php:66
msgid "The Cookiebot plugin requires PHP version %s or greater."
msgstr "Le plug-in Cookiebot nécessite la version PHP %s ou supérieure."

#: src/lib/Cookiebot_WP.php:242 src/settings/pages/Dashboard_Page.php:35
#: src/view/admin/common/templates/main-tabs.php:21
msgid "Dashboard"
msgstr "Tableau de bord"

#: src/lib/helper.php:245 src/lib/helper.php:343
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:91
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:167
#: src/view/admin/cb_frame/settings/gcm-page.php:126
#: src/view/admin/cb_frame/settings/gtm-page.php:113
#: src/view/admin/common/prior-consent/available-addons/tab.php:55
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:88
msgid "marketing"
msgstr "marketing"

#: src/lib/helper.php:248 src/lib/helper.php:342
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:88
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:134
#: src/view/admin/cb_frame/settings/gcm-page.php:118
#: src/view/admin/cb_frame/settings/gtm-page.php:105
#: src/view/admin/common/prior-consent/available-addons/tab.php:46
msgid "statistics"
msgstr "statistiques"

#: src/lib/helper.php:251 src/lib/helper.php:341
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:85
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:118
#: src/view/admin/cb_frame/settings/gcm-page.php:110
#: src/view/admin/cb_frame/settings/gtm-page.php:97
#: src/view/admin/common/prior-consent/available-addons/tab.php:37
msgid "preferences"
msgstr "préférences"

#: src/lib/helper.php:254
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:82
msgid "necessary"
msgstr "nécessaires"

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:367
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent]."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:372
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"tracking."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour activer le suivi."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:377
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Social Share buttons."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour activer les boutons de partage sur les réseaux sociaux."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:382
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to view "
"this element."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour afficher cet élément."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:387
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to watch "
"this video."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour regarder cette vidéo."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:392
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Services."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour activer les services Google."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:397
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"facebook shopping feature."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour activer la fonction d'achat Facebook."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:402
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to track "
"for google analytics."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour effectuer le suivi de Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:407
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Analytics."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour activer Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:412
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"instagram feed."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour activer le feed Instagram."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:417
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Facebook Pixel."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour activer le pixel Facebook."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:422
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to Social "
"Share buttons."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour les boutons de partage sur les réseaux sociaux."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:427
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to allow "
"Matomo statistics."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour autoriser les statistiques Matomo."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:432
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"saving user information."
msgstr ""
"Veuillez accepter les cookies [renew_consent]%cookie_types[/renew_consent] "
"pour permettre l'enregistrement des informations des utilisateurs."

#: src/lib/Supported_Languages.php:8
msgid "Norwegian Bokmål"
msgstr "Bokmål norvégien"

#: src/lib/Supported_Languages.php:9
msgid "Turkish"
msgstr "Turc"

#: src/lib/Supported_Languages.php:10
msgid "German"
msgstr "Allemand"

#: src/lib/Supported_Languages.php:11
msgid "Czech"
msgstr "Tchèque"

#: src/lib/Supported_Languages.php:12
msgid "Danish"
msgstr "Danois"

#: src/lib/Supported_Languages.php:13
msgid "Albanian"
msgstr "Albanais"

#: src/lib/Supported_Languages.php:14
msgid "Hebrew"
msgstr "Hébreu"

#: src/lib/Supported_Languages.php:15
msgid "Korean"
msgstr "Coréen"

#: src/lib/Supported_Languages.php:16
msgid "Italian"
msgstr "Italien"

#: src/lib/Supported_Languages.php:17
msgid "Dutch"
msgstr "Néerlandais"

#: src/lib/Supported_Languages.php:18
msgid "Vietnamese"
msgstr "Vietnamien"

#: src/lib/Supported_Languages.php:19
msgid "Tamil"
msgstr "Tamoul"

#: src/lib/Supported_Languages.php:20
msgid "Icelandic"
msgstr "Islandais"

#: src/lib/Supported_Languages.php:21
msgid "Romanian"
msgstr "Roumain"

#: src/lib/Supported_Languages.php:22
msgid "Sinhala"
msgstr "Cingalais"

#: src/lib/Supported_Languages.php:23
msgid "Catalan"
msgstr "Catalan"

#: src/lib/Supported_Languages.php:24
msgid "Bulgarian"
msgstr "Bulgare"

#: src/lib/Supported_Languages.php:25
msgid "Ukrainian"
msgstr "Ukrainien"

#: src/lib/Supported_Languages.php:26
msgid "Chinese"
msgstr "Chinois"

#: src/lib/Supported_Languages.php:27
msgid "English"
msgstr "Anglais"

#: src/lib/Supported_Languages.php:28
msgid "Arabic"
msgstr "Arabe"

#: src/lib/Supported_Languages.php:29
msgid "Croatian"
msgstr "Croate"

#: src/lib/Supported_Languages.php:30
msgid "Thai"
msgstr "Thaïlandais"

#: src/lib/Supported_Languages.php:31
msgid "Greek"
msgstr "Grec"

#: src/lib/Supported_Languages.php:32
msgid "Lithuanian"
msgstr "Lituanien"

#: src/lib/Supported_Languages.php:33
msgid "Polish"
msgstr "Polonais"

#: src/lib/Supported_Languages.php:34
msgid "Latvian"
msgstr "Letton"

#: src/lib/Supported_Languages.php:35
msgid "French"
msgstr "Français"

#: src/lib/Supported_Languages.php:36
msgid "Indonesian"
msgstr "Indonésien"

#: src/lib/Supported_Languages.php:37
msgid "Macedonian"
msgstr "Macédonien"

#: src/lib/Supported_Languages.php:38
msgid "Estonian"
msgstr "Estonien"

#: src/lib/Supported_Languages.php:39
msgid "Portuguese"
msgstr "Portugais"

#: src/lib/Supported_Languages.php:40
msgid "Irish"
msgstr "Irlandais"

#: src/lib/Supported_Languages.php:41
msgid "Malay"
msgstr "Malais"

#: src/lib/Supported_Languages.php:42
msgid "Slovenian"
msgstr "Slovène"

#: src/lib/Supported_Languages.php:43
msgid "Russian"
msgstr "Russe"

#: src/lib/Supported_Languages.php:44
msgid "Japanese"
msgstr "Japonais"

#: src/lib/Supported_Languages.php:45
msgid "Hindi"
msgstr "Hindi"

#: src/lib/Supported_Languages.php:46
msgid "Slovak"
msgstr "Slovaque"

#: src/lib/Supported_Languages.php:47
msgid "Spanish"
msgstr "Espagnol"

#: src/lib/Supported_Languages.php:48
msgid "Swedish"
msgstr "Suédois"

#: src/lib/Supported_Languages.php:49
msgid "Serbian"
msgstr "Serbe"

#: src/lib/Supported_Languages.php:50
msgid "Finnish"
msgstr "Finnois"

#: src/lib/Supported_Languages.php:51
msgid "Basque"
msgstr "Basque"

#: src/lib/Supported_Languages.php:52
msgid "Hungarian"
msgstr "Hongrois"

#: src/lib/Supported_Regions.php:7
msgid "Afghanistan"
msgstr "Afghanistan"

#: src/lib/Supported_Regions.php:8
msgid "Albania"
msgstr "Albanie"

#: src/lib/Supported_Regions.php:9
msgid "Algeria"
msgstr "Algérie"

#: src/lib/Supported_Regions.php:10
msgid "American Samoa"
msgstr "Samoa américaines"

#: src/lib/Supported_Regions.php:11
msgid "Andorra"
msgstr "Andorre"

#: src/lib/Supported_Regions.php:12
msgid "Angola"
msgstr "Angola"

#: src/lib/Supported_Regions.php:13
msgid "Anguilla"
msgstr "Anguilla"

#: src/lib/Supported_Regions.php:14
msgid "Antarctica"
msgstr "Antarctique"

#: src/lib/Supported_Regions.php:15
msgid "Antigua and Barbuda"
msgstr "Antigua-et-Barduda"

#: src/lib/Supported_Regions.php:16
msgid "Argentina"
msgstr "Argentine"

#: src/lib/Supported_Regions.php:17
msgid "Armenia"
msgstr "Arménie"

#: src/lib/Supported_Regions.php:18
msgid "Aruba"
msgstr "Aruba"

#: src/lib/Supported_Regions.php:19
msgid "Australia"
msgstr "Australie"

#: src/lib/Supported_Regions.php:20
msgid "Austria"
msgstr "Autriche"

#: src/lib/Supported_Regions.php:21
msgid "Azerbaijan"
msgstr "Azerbaïdjan"

#: src/lib/Supported_Regions.php:22
msgid "Bahamas"
msgstr "Bahamas"

#: src/lib/Supported_Regions.php:23
msgid "Bahrain"
msgstr "Bahreïn"

#: src/lib/Supported_Regions.php:24
msgid "Bangladesh"
msgstr "Bangladesh"

#: src/lib/Supported_Regions.php:25
msgid "Barbados"
msgstr "Barbade"

#: src/lib/Supported_Regions.php:26
msgid "Belarus"
msgstr "Biélorussie"

#: src/lib/Supported_Regions.php:27
msgid "Belgium"
msgstr "Belgique"

#: src/lib/Supported_Regions.php:28
msgid "Belize"
msgstr "Bélize"

#: src/lib/Supported_Regions.php:29
msgid "Benin"
msgstr "Bénin"

#: src/lib/Supported_Regions.php:30
msgid "Bermuda"
msgstr "Bermudes"

#: src/lib/Supported_Regions.php:31
msgid "Bhutan"
msgstr "Bhoutan"

#: src/lib/Supported_Regions.php:32
msgid "Bolivia"
msgstr "Bolivie"

#: src/lib/Supported_Regions.php:33
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Saint-Eustache et Saba"

#: src/lib/Supported_Regions.php:34
msgid "Bosnia and Herzegovina"
msgstr "Bosnie-Herzégovine"

#: src/lib/Supported_Regions.php:35
msgid "Botswana"
msgstr "Botswana"

#: src/lib/Supported_Regions.php:36
msgid "Bouvet Island"
msgstr "Île Bouvet"

#: src/lib/Supported_Regions.php:37
msgid "Brazil"
msgstr "Brésil"

#: src/lib/Supported_Regions.php:38
msgid "British Indian Ocean Territory"
msgstr "Territoire britannique de l'océan Indien"

#: src/lib/Supported_Regions.php:39
msgid "Brunei "
msgstr "Brunei"

#: src/lib/Supported_Regions.php:40
msgid "Bulgaria"
msgstr "Bulgarie"

#: src/lib/Supported_Regions.php:41
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: src/lib/Supported_Regions.php:42
msgid "Burundi"
msgstr "Burundi"

#: src/lib/Supported_Regions.php:43
msgid "Cambodia"
msgstr "Cambodge"

#: src/lib/Supported_Regions.php:44
msgid "Cameroon"
msgstr "Cameroun"

#: src/lib/Supported_Regions.php:45
msgid "Canada"
msgstr "Canada"

#: src/lib/Supported_Regions.php:46
msgid "Cape Verde"
msgstr "Cap Vert"

#: src/lib/Supported_Regions.php:47
msgid "Cayman Islands"
msgstr "Îles Caïmans"

#: src/lib/Supported_Regions.php:48
msgid "Central African Republic"
msgstr "République centrafricaine"

#: src/lib/Supported_Regions.php:49
msgid "Chad"
msgstr "Tchad"

#: src/lib/Supported_Regions.php:50
msgid "Chile"
msgstr "Chili"

#: src/lib/Supported_Regions.php:51
msgid "China"
msgstr "Chine"

#: src/lib/Supported_Regions.php:52
msgid "Christmas Island"
msgstr "Île Christmas"

#: src/lib/Supported_Regions.php:53
msgid "Cocos (Keeling) Islands"
msgstr "Îles Cocos"

#: src/lib/Supported_Regions.php:54
msgid "Colombia"
msgstr "Colombie"

#: src/lib/Supported_Regions.php:55
msgid "Comoros"
msgstr "Comores"

#: src/lib/Supported_Regions.php:56
msgid "Congo"
msgstr "Congo"

#: src/lib/Supported_Regions.php:57
msgid "Congo, the Democratic Republic of the"
msgstr "République démocratique du Congo"

#: src/lib/Supported_Regions.php:58
msgid "Cook Islands"
msgstr "Îles Cook"

#: src/lib/Supported_Regions.php:59
msgid "Costa Rica"
msgstr "Costa Rica"

#: src/lib/Supported_Regions.php:60
msgid "Croatia"
msgstr "Croatie"

#: src/lib/Supported_Regions.php:61
msgid "Cuba"
msgstr "Cuba"

#: src/lib/Supported_Regions.php:62
msgid "Curaçao"
msgstr "Curaçao"

#: src/lib/Supported_Regions.php:63
msgid "Cyprus"
msgstr "Chypre"

#: src/lib/Supported_Regions.php:64
msgid "Czech Republic"
msgstr "République tchèque"

#: src/lib/Supported_Regions.php:65
msgid "Côte d'Ivoire"
msgstr "Côte d'Ivoire"

#: src/lib/Supported_Regions.php:66
msgid "Denmark"
msgstr "Danemark"

#: src/lib/Supported_Regions.php:67
msgid "Djibouti"
msgstr "Djibouti"

#: src/lib/Supported_Regions.php:68
msgid "Dominica"
msgstr "Dominique"

#: src/lib/Supported_Regions.php:69
msgid "Dominican Republic"
msgstr "République dominicaine"

#: src/lib/Supported_Regions.php:70
msgid "Ecuador"
msgstr "Équateur"

#: src/lib/Supported_Regions.php:71
msgid "Egypt"
msgstr "Égypte"

#: src/lib/Supported_Regions.php:72
msgid "El Salvador"
msgstr "Salvador"

#: src/lib/Supported_Regions.php:73
msgid "Equatorial Guinea"
msgstr "Guinée équatoriale"

#: src/lib/Supported_Regions.php:74
msgid "Eritrea"
msgstr "Érythrée"

#: src/lib/Supported_Regions.php:75
msgid "Estonia"
msgstr "Estonie"

#: src/lib/Supported_Regions.php:76
msgid "Ethiopia"
msgstr "Éthiopie"

#: src/lib/Supported_Regions.php:77
msgid "Falkland Islands (Malvinas)"
msgstr "Îles Malouines"

#: src/lib/Supported_Regions.php:78
msgid "Faroe Islands"
msgstr "Îles Féroé"

#: src/lib/Supported_Regions.php:79
msgid "Fiji"
msgstr "Fidji"

#: src/lib/Supported_Regions.php:80
msgid "Finland"
msgstr "Finlande"

#: src/lib/Supported_Regions.php:81
msgid "France"
msgstr "France"

#: src/lib/Supported_Regions.php:82
msgid "French Guiana"
msgstr "Guinée française"

#: src/lib/Supported_Regions.php:83
msgid "French Polynesia"
msgstr "Polynésie française"

#: src/lib/Supported_Regions.php:84
msgid "French Southern and Antarctic Lands"
msgstr "Terres australes et antarctiques françaises"

#: src/lib/Supported_Regions.php:85
msgid "Gabon"
msgstr "Gabon"

#: src/lib/Supported_Regions.php:86
msgid "Gambia"
msgstr "Gambie"

#: src/lib/Supported_Regions.php:87
msgid "Georgia"
msgstr "Géorgie"

#: src/lib/Supported_Regions.php:88
msgid "Germany"
msgstr "Allemagne"

#: src/lib/Supported_Regions.php:89
msgid "Ghana"
msgstr "Ghana"

#: src/lib/Supported_Regions.php:90
msgid "Gibraltar"
msgstr "Gibraltar"

#: src/lib/Supported_Regions.php:91
msgid "Greece"
msgstr "Grèce"

#: src/lib/Supported_Regions.php:92
msgid "Greenland"
msgstr "Groenland"

#: src/lib/Supported_Regions.php:93
msgid "Grenada"
msgstr "Grenade"

#: src/lib/Supported_Regions.php:94
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: src/lib/Supported_Regions.php:95
msgid "Guam"
msgstr "Guam"

#: src/lib/Supported_Regions.php:96
msgid "Guatemala"
msgstr "Guatemala"

#: src/lib/Supported_Regions.php:97
msgid "Guernsey"
msgstr "Guernesey"

#: src/lib/Supported_Regions.php:98
msgid "Guinea"
msgstr "Guinée"

#: src/lib/Supported_Regions.php:99
msgid "Guinea-Bissau"
msgstr "Guinée-Bissau"

#: src/lib/Supported_Regions.php:100
msgid "Guyana"
msgstr "Guyane"

#: src/lib/Supported_Regions.php:101
msgid "Haiti"
msgstr "Haïti"

#: src/lib/Supported_Regions.php:102
msgid "Heard Island and McDonald Islands"
msgstr "Îles Heard-et-MacDonald"

#: src/lib/Supported_Regions.php:103
msgid "Holy See (Vatican City State)"
msgstr "Saint-Siège (État de la Cité du Vatican)"

#: src/lib/Supported_Regions.php:104
msgid "Honduras"
msgstr "Honduras"

#: src/lib/Supported_Regions.php:105
msgid "Hong Kong"
msgstr "Hong Kong"

#: src/lib/Supported_Regions.php:106
msgid "Hungary"
msgstr "Hongrie"

#: src/lib/Supported_Regions.php:107
msgid "Iceland"
msgstr "Islande"

#: src/lib/Supported_Regions.php:108
msgid "India"
msgstr "Inde"

#: src/lib/Supported_Regions.php:109
msgid "Indonesia"
msgstr "Indonésie"

#: src/lib/Supported_Regions.php:110
msgid "Iran"
msgstr "Iran"

#: src/lib/Supported_Regions.php:111
msgid "Iraq"
msgstr "Iraq"

#: src/lib/Supported_Regions.php:112
msgid "Ireland"
msgstr "Irelande"

#: src/lib/Supported_Regions.php:113
msgid "Isle of Man"
msgstr "Île de Man"

#: src/lib/Supported_Regions.php:114
msgid "Israel"
msgstr "Israël"

#: src/lib/Supported_Regions.php:115
msgid "Italy"
msgstr "Italie"

#: src/lib/Supported_Regions.php:116
msgid "Jamaica"
msgstr "Jamaïque"

#: src/lib/Supported_Regions.php:117
msgid "Japan"
msgstr "Japon"

#: src/lib/Supported_Regions.php:118
msgid "Jersey"
msgstr "Jersey"

#: src/lib/Supported_Regions.php:119
msgid "Jordan"
msgstr "Jordanie"

#: src/lib/Supported_Regions.php:120
msgid "Kazakhstan"
msgstr "Kazakhstan"

#: src/lib/Supported_Regions.php:121
msgid "Kenya"
msgstr "Kenya"

#: src/lib/Supported_Regions.php:122
msgid "Kiribati"
msgstr "Kiribati"

#: src/lib/Supported_Regions.php:123
msgid "Kuwait"
msgstr "Koweït"

#: src/lib/Supported_Regions.php:124
msgid "Kyrgyzstan"
msgstr "Kirghizistan"

#: src/lib/Supported_Regions.php:125
msgid "Laos"
msgstr "Laos"

#: src/lib/Supported_Regions.php:126
msgid "Latvia"
msgstr "Lettonie"

#: src/lib/Supported_Regions.php:127
msgid "Lebanon"
msgstr "Liban"

#: src/lib/Supported_Regions.php:128
msgid "Lesotho"
msgstr "Lesotho"

#: src/lib/Supported_Regions.php:129
msgid "Liberia"
msgstr "Liberia"

#: src/lib/Supported_Regions.php:130
msgid "Libya"
msgstr "Libye"

#: src/lib/Supported_Regions.php:131
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: src/lib/Supported_Regions.php:132
msgid "Lithuania"
msgstr "Lituanie"

#: src/lib/Supported_Regions.php:133
msgid "Luxembourg"
msgstr "Luxembourg"

#: src/lib/Supported_Regions.php:134
msgid "Macao"
msgstr "Macao"

#: src/lib/Supported_Regions.php:135
msgid "North Macedonia"
msgstr "Macédoine du Nord"

#: src/lib/Supported_Regions.php:136
msgid "Madagascar"
msgstr "Madagascar"

#: src/lib/Supported_Regions.php:137
msgid "Malawi"
msgstr "Malawi"

#: src/lib/Supported_Regions.php:138
msgid "Malaysia"
msgstr "Malaisie"

#: src/lib/Supported_Regions.php:139
msgid "Maldives"
msgstr "Maldives"

#: src/lib/Supported_Regions.php:140
msgid "Mali"
msgstr "Mali"

#: src/lib/Supported_Regions.php:141
msgid "Malta"
msgstr "Malte"

#: src/lib/Supported_Regions.php:142
msgid "Marshall Islands"
msgstr "Îles Marshall"

#: src/lib/Supported_Regions.php:143
msgid "Martinique"
msgstr "Martinique"

#: src/lib/Supported_Regions.php:144
msgid "Mauritania"
msgstr "Mauritanie"

#: src/lib/Supported_Regions.php:145
msgid "Mauritius"
msgstr "Maurice"

#: src/lib/Supported_Regions.php:146
msgid "Mayotte"
msgstr "Mayotte"

#: src/lib/Supported_Regions.php:147
msgid "Mexico"
msgstr "Mexique"

#: src/lib/Supported_Regions.php:148
msgid "Micronesia, Federated States of"
msgstr "Micronésie, États fédérés de"

#: src/lib/Supported_Regions.php:149
msgid "Moldova"
msgstr "Moldavie"

#: src/lib/Supported_Regions.php:150
msgid "Monaco"
msgstr "Monaco"

#: src/lib/Supported_Regions.php:151
msgid "Mongolia"
msgstr "Mongolie"

#: src/lib/Supported_Regions.php:152
msgid "Montenegro"
msgstr "Montenegro"

#: src/lib/Supported_Regions.php:153
msgid "Montserrat"
msgstr "Montserrat"

#: src/lib/Supported_Regions.php:154
msgid "Morocco"
msgstr "Maroc"

#: src/lib/Supported_Regions.php:155
msgid "Mozambique"
msgstr "Mozambique"

#: src/lib/Supported_Regions.php:156
msgid "Myanmar"
msgstr "Myanmar"

#: src/lib/Supported_Regions.php:157
msgid "Namibia"
msgstr "Namibie"

#: src/lib/Supported_Regions.php:158
msgid "Nauru"
msgstr "Nauru"

#: src/lib/Supported_Regions.php:159
msgid "Nepal"
msgstr "Népal"

#: src/lib/Supported_Regions.php:160
msgid "Netherlands"
msgstr "Pays-Bas"

#: src/lib/Supported_Regions.php:161
msgid "New Caledonia"
msgstr "Nouvelle-Calédonie"

#: src/lib/Supported_Regions.php:162
msgid "New Zealand"
msgstr "Nouvelle-Zélande"

#: src/lib/Supported_Regions.php:163
msgid "Nicaragua"
msgstr "Nicaragua"

#: src/lib/Supported_Regions.php:164
msgid "Niger"
msgstr "Niger"

#: src/lib/Supported_Regions.php:165
msgid "Nigeria"
msgstr "Nigeria"

#: src/lib/Supported_Regions.php:166
msgid "Niue"
msgstr "Niue"

#: src/lib/Supported_Regions.php:167
msgid "Norfolk Island"
msgstr "Île Norfolk"

#: src/lib/Supported_Regions.php:168
msgid "North Korea"
msgstr "Corée du Nord"

#: src/lib/Supported_Regions.php:169
msgid "Northern Mariana Islands"
msgstr "Îles Mariannes du Nord"

#: src/lib/Supported_Regions.php:170
msgid "Norway"
msgstr "Norvège"

#: src/lib/Supported_Regions.php:171
msgid "Oman"
msgstr "Oman"

#: src/lib/Supported_Regions.php:172
msgid "Pakistan"
msgstr "Pakistan"

#: src/lib/Supported_Regions.php:173
msgid "Palau"
msgstr "Palau"

#: src/lib/Supported_Regions.php:174
msgid "Palestinian Territory"
msgstr "Territoires palestiniens"

#: src/lib/Supported_Regions.php:175
msgid "Panama"
msgstr "Panama"

#: src/lib/Supported_Regions.php:176
msgid "Papua New Guinea"
msgstr "Papouasie-Nouvelle-Guinée"

#: src/lib/Supported_Regions.php:177
msgid "Paraguay"
msgstr "Paraguay"

#: src/lib/Supported_Regions.php:178
msgid "Peru"
msgstr "Pérou"

#: src/lib/Supported_Regions.php:179
msgid "Philippines"
msgstr "Philippines"

#: src/lib/Supported_Regions.php:180
msgid "Pitcairn"
msgstr "Pitcairn"

#: src/lib/Supported_Regions.php:181
msgid "Poland"
msgstr "Pologne"

#: src/lib/Supported_Regions.php:182
msgid "Portugal"
msgstr "Portugal"

#: src/lib/Supported_Regions.php:183
msgid "Puerto Rico"
msgstr "Porto Rico"

#: src/lib/Supported_Regions.php:184
msgid "Qatar"
msgstr "Qatar"

#: src/lib/Supported_Regions.php:185
msgid "Romania"
msgstr "Roumanie"

#: src/lib/Supported_Regions.php:186
msgid "Russia"
msgstr "Russie"

#: src/lib/Supported_Regions.php:187
msgid "Rwanda"
msgstr "Rwanda"

#: src/lib/Supported_Regions.php:188
msgid "Réunion"
msgstr "Réunion"

#: src/lib/Supported_Regions.php:189
msgid "Saint Barthélemy"
msgstr "Saint-Barthélemy"

#: src/lib/Supported_Regions.php:190
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr "Sainte-Hélène, Ascension et Tristan da Cunha"

#: src/lib/Supported_Regions.php:191
msgid "Saint Kitts and Nevis"
msgstr "Saint-Christophe-et-Niévès"

#: src/lib/Supported_Regions.php:192
msgid "Saint Lucia"
msgstr "Sainte-Lucie"

#: src/lib/Supported_Regions.php:193
msgid "Saint Martin (French part)"
msgstr "Saint-Martin (Antilles françaises)"

#: src/lib/Supported_Regions.php:194
msgid "Saint Pierre and Miquelon"
msgstr "Saint-Pierre-et-Miquelon"

#: src/lib/Supported_Regions.php:195
msgid "Saint Vincent and the Grenadines"
msgstr "Saint-Vincent-et-les-Grenadines"

#: src/lib/Supported_Regions.php:196
msgid "Samoa"
msgstr "Samoa"

#: src/lib/Supported_Regions.php:197
msgid "San Marino"
msgstr "Saint-Marin"

#: src/lib/Supported_Regions.php:198
msgid "Sao Tome and Principe"
msgstr "Sao Tomé-et-Principe"

#: src/lib/Supported_Regions.php:199
msgid "Saudi Arabia"
msgstr "Arabie Saoudite"

#: src/lib/Supported_Regions.php:200
msgid "Senegal"
msgstr "Sénégal"

#: src/lib/Supported_Regions.php:201
msgid "Serbia"
msgstr "Serbie"

#: src/lib/Supported_Regions.php:202
msgid "Seychelles"
msgstr "Seychelles"

#: src/lib/Supported_Regions.php:203
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: src/lib/Supported_Regions.php:204
msgid "Singapore"
msgstr "Singapour"

#: src/lib/Supported_Regions.php:205
msgid "Sint Maarten (Dutch part)"
msgstr "Saint-Martin (partie néerlandaise)"

#: src/lib/Supported_Regions.php:206
msgid "Slovakia"
msgstr "Slovaquie"

#: src/lib/Supported_Regions.php:207
msgid "Slovenia"
msgstr "Slovénie"

#: src/lib/Supported_Regions.php:208
msgid "Solomon Islands"
msgstr "Îles Salomon"

#: src/lib/Supported_Regions.php:209
msgid "Somalia"
msgstr "Somalie"

#: src/lib/Supported_Regions.php:210
msgid "South Africa"
msgstr "Afrique du Sud"

#: src/lib/Supported_Regions.php:211
msgid "South Georgia and the South Sandwich Islands"
msgstr "Géorgie du Sud-et-les îles Sandwich du Sud"

#: src/lib/Supported_Regions.php:212
msgid "South Korea"
msgstr "Corée du Sud"

#: src/lib/Supported_Regions.php:213
msgid "South Sudan"
msgstr "Soudan du Sud"

#: src/lib/Supported_Regions.php:214
msgid "Spain"
msgstr "Espagne"

#: src/lib/Supported_Regions.php:215
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: src/lib/Supported_Regions.php:216
msgid "Sudan"
msgstr "Soudan"

#: src/lib/Supported_Regions.php:217
msgid "Suriname"
msgstr "Suriname"

#: src/lib/Supported_Regions.php:218
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard et Jan Mayen"

#: src/lib/Supported_Regions.php:219
msgid "Eswatini"
msgstr "Eswatini"

#: src/lib/Supported_Regions.php:220
msgid "Sweden"
msgstr "Suède"

#: src/lib/Supported_Regions.php:221
msgid "Switzerland"
msgstr "Suisse"

#: src/lib/Supported_Regions.php:222
msgid "Syria"
msgstr "Syrie"

#: src/lib/Supported_Regions.php:223
msgid "Taiwan"
msgstr "Taïwan"

#: src/lib/Supported_Regions.php:224
msgid "Tajikistan"
msgstr "Tadjikistan"

#: src/lib/Supported_Regions.php:225
msgid "Tanzania"
msgstr "Tanzanie"

#: src/lib/Supported_Regions.php:226
msgid "Thailand"
msgstr "Thaïlande"

#: src/lib/Supported_Regions.php:227
msgid "Timor-Leste"
msgstr "Timor oriental"

#: src/lib/Supported_Regions.php:228
msgid "Togo"
msgstr "Togo"

#: src/lib/Supported_Regions.php:229
msgid "Tokelau"
msgstr "Tokelau"

#: src/lib/Supported_Regions.php:230
msgid "Tonga"
msgstr "Tonga"

#: src/lib/Supported_Regions.php:231
msgid "Trinidad and Tobago"
msgstr "Trinité-et-Tobago"

#: src/lib/Supported_Regions.php:232
msgid "Tunisia"
msgstr "Tunisie"

#: src/lib/Supported_Regions.php:233
msgid "Türkiye"
msgstr "Turquie"

#: src/lib/Supported_Regions.php:234
msgid "Turkmenistan"
msgstr "Turkménistan"

#: src/lib/Supported_Regions.php:235
msgid "Turks and Caicos Islands"
msgstr "Îles Turques-et-Caïques"

#: src/lib/Supported_Regions.php:236
msgid "Tuvalu"
msgstr "Tuvalu"

#: src/lib/Supported_Regions.php:237
msgid "Uganda"
msgstr "Ouganda"

#: src/lib/Supported_Regions.php:238
msgid "Ukraine"
msgstr "Ukraine"

#: src/lib/Supported_Regions.php:239
msgid "United Arab Emirates"
msgstr "Émirats arabes unis"

#: src/lib/Supported_Regions.php:240
msgid "United Kingdom"
msgstr "Royaume-Uni"

#: src/lib/Supported_Regions.php:241
msgid "United States"
msgstr "États-Unis"

#: src/lib/Supported_Regions.php:242
msgid "United States - State of California"
msgstr "États-Unis - État de Californie"

#: src/lib/Supported_Regions.php:243
msgid "United States - State of Colorado"
msgstr "États-Unis - État du Colorado"

#: src/lib/Supported_Regions.php:244
msgid "United States - State of Connecticut"
msgstr "États-Unis - État du Connecticut"

#: src/lib/Supported_Regions.php:245
msgid "United States - State of Utah"
msgstr "États-Unis - État de l’Utah"

#: src/lib/Supported_Regions.php:246
msgid "United States - State of Virginia"
msgstr "États-Unis - État de Virginie"

#: src/lib/Supported_Regions.php:247
msgid "United States Minor Outlying Islands"
msgstr "Îles mineures éloignées des États-Unis"

#: src/lib/Supported_Regions.php:248
msgid "Uruguay"
msgstr "Uruguay"

#: src/lib/Supported_Regions.php:249
msgid "Uzbekistan"
msgstr "Ouzbékistan"

#: src/lib/Supported_Regions.php:250
msgid "Vanuatu"
msgstr "Vanuatu"

#: src/lib/Supported_Regions.php:251
msgid "Venezuela"
msgstr "Venezuela"

#: src/lib/Supported_Regions.php:252
msgid "Viet Nam"
msgstr "Vietnam"

#: src/lib/Supported_Regions.php:253
msgid "Virgin Islands, British"
msgstr "Îles Vierges britanniques"

#: src/lib/Supported_Regions.php:254
msgid "Virgin Islands, U.S."
msgstr "Îles Vierges américaines"

#: src/lib/Supported_Regions.php:255
msgid "Wallis and Futuna"
msgstr "Wallis et Futuna"

#: src/lib/Supported_Regions.php:256
msgid "Western Sahara"
msgstr "Sahara occidental"

#: src/lib/Supported_Regions.php:257
msgid "Yemen"
msgstr "Yémen"

#: src/lib/Supported_Regions.php:258
msgid "Zambia"
msgstr "Zambie"

#: src/lib/Supported_Regions.php:259
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: src/lib/Supported_Regions.php:260
msgid "Åland Islands"
msgstr "Îles Åland"

#: src/settings/Network_Menu_Settings.php:36
#: src/settings/pages/Dashboard_Page.php:21
msgid "Cookiebot"
msgstr "Cookiebot"

#: src/settings/Network_Menu_Settings.php:44
#: src/settings/pages/Settings_Page.php:23
msgid "Cookiebot Settings"
msgstr "Cookiebot Paramètres"

#: src/settings/Network_Menu_Settings.php:45
#: src/settings/pages/Settings_Page.php:24
#: src/view/admin/common/settings-page.php:30
#: src/view/admin/common/templates/main-tabs.php:32
#: src/view/admin/uc_frame/settings-page.php:47
#: src/view/admin/cb_frame/settings-page.php:50
msgid "Settings"
msgstr "Paramètres"

#: src/settings/Network_Menu_Settings.php:52
#: src/settings/pages/Support_Page.php:18
msgid "Cookiebot Support"
msgstr "Cookiebot Assistance"

#: src/settings/Network_Menu_Settings.php:53
#: src/settings/pages/Support_Page.php:19
#: src/view/admin/cb_frame/support-page.php:19
#: src/view/admin/common/support-page.php:19
#: src/view/admin/common/templates/main-tabs.php:50
#: src/view/admin/uc_frame/support-page.php:19
msgid "Support"
msgstr "Assistance"

#: src/settings/pages/Dashboard_Page.php:34
msgid "Cookiebot Dashboard"
msgstr "Cookiebot Tableau de bord"

#: src/settings/pages/Debug_Page.php:27 src/settings/pages/Debug_Page.php:28
#: src/view/admin/cb_frame/debug-page.php:18
#: src/view/admin/common/templates/main-tabs.php:57
#: src/view/admin/uc_frame/debug-page.php:18
msgid "Debug info"
msgstr "Infos sur le débogage"

#: src/settings/pages/Iab_Page.php:20 src/settings/pages/Iab_Page.php:21
msgid "IAB"
msgstr "IAB"

#: src/settings/pages/Iab_Page.php:130
msgid "Purposes of data use"
msgstr "Finalités du traitement des données"

#: src/settings/pages/Iab_Page.php:131
msgid ""
"Inform your users how you’ll use their data. We’ll show this on the second "
"layer of your consent banner, where users interested in more granular detail "
"about data processing can view it."
msgstr ""
"Informez vos utilisateurs sur la manière dont leurs données vont être "
"utilisées. Les utilisateurs désireux d’obtenir plus de détails sur le "
"traitement de leur données trouveront cette information dans la deuxième "
"section de votre bandeau de consentement."

#: src/settings/pages/Iab_Page.php:138
msgid "Special purposes of data use"
msgstr "Finalités spécifiques du traitement des données"

#: src/settings/pages/Iab_Page.php:139
msgid ""
"Inform your users about special purposes of using their data. We’ll show "
"this on the second layer of your consent banner."
msgstr ""
"Informez vos utilisateurs sur les finalités spécifiques d’utilisation de "
"leurs données. Cette information se trouvera sur la deuxième section de "
"votre bandeau de consentement."

#: src/settings/pages/Iab_Page.php:146
msgid "Features required for data processing"
msgstr "Fonctionnalités nécessaires au traitement des données"

#: src/settings/pages/Iab_Page.php:147
msgid ""
"Inform users about the features necessary for processing their personal "
"data. We’ll list the selected features on the second layer of your consent "
"banner."
msgstr ""
"Informez vos utilisateurs des fonctionnalités indispensables au traitement "
"de leurs données personnelles. Celles-ci seront répertoriées sur la deuxième "
"section de votre bandeau de consentement."

#: src/settings/pages/Iab_Page.php:154
msgid "Special features required for data processing"
msgstr "Fonctionnalités spécifiques nécessaires au traitement des données"

#: src/settings/pages/Iab_Page.php:155
msgid ""
"Inform users about any specially categorized features required for "
"processing their personal data. We’ll list the selected features on the "
"second layer of your consent banner, offering options for users to enable or "
"disable them."
msgstr ""
"Informez vos utilisateurs des fonctionnalités spécifiques requises pour le "
"traitement de leurs données personnelles. Ces fonctionnalités seront "
"répertoriées dans la deuxième section de votre bandeau de consentement, "
"permettant ainsi aux utilisateurs de les activer ou de les désactiver selon "
"leurs préférences."

#: src/settings/pages/Iab_Page.php:162
msgid "TCF listed vendors"
msgstr "Fournisseurs répertoriés dans le TCF"

#: src/settings/pages/Iab_Page.php:183
msgid "Store and/or access information on a device"
msgstr "Collecte et stockage d’informations sur un appareil"

#: src/settings/pages/Iab_Page.php:187
msgid "Use limited data to select advertising"
msgstr "Utilisation restreinte de données pour la sélection de la publicité"

#: src/settings/pages/Iab_Page.php:191
msgid "Create profiles for personalised advertising"
msgstr "Création de profils pour la personnalisation des publicités"

#: src/settings/pages/Iab_Page.php:195
msgid "Use profiles to select personalised advertising"
msgstr "Utilisation de profils pour la sélection de publicités personnalisées"

#: src/settings/pages/Iab_Page.php:199
msgid "Create profiles to personalise content"
msgstr "Création de profils pour la personnalisation du contenu"

#: src/settings/pages/Iab_Page.php:203
msgid "Use profiles to select personalised content"
msgstr "Utilisation de profils pour la personnalisation du contenu"

#: src/settings/pages/Iab_Page.php:207
msgid "Measure advertising performance"
msgstr "Évaluation de la performance publicitaire"

#: src/settings/pages/Iab_Page.php:211
msgid "Measure content performance"
msgstr "Évaluation de la performance du contenu"

#: src/settings/pages/Iab_Page.php:215
msgid ""
"Understand audiences through statistics or combinations of data from "
"different sources"
msgstr ""
"Analyse des audiences à travers des statistiques ou des agrégations de "
"données provenant de diverses sources"

#: src/settings/pages/Iab_Page.php:219
msgid "Develop and improve services"
msgstr "Développement ou amélioration de services"

#: src/settings/pages/Iab_Page.php:223
msgid "Use limited data to select content"
msgstr "Utilisation restreinte de données pour la sélection de contenu"

#: src/settings/pages/Iab_Page.php:229
msgid "Ensure security, prevent and detect fraud, and fix errors"
msgstr ""
"Sécurité, prévention et détection de la fraude, et correction des erreurs"

#: src/settings/pages/Iab_Page.php:233
msgid "Deliver and present advertising and content"
msgstr "Diffusion et présentation de la publicité et du contenu"

#: src/settings/pages/Iab_Page.php:239
msgid "Match and combine data from other data sources"
msgstr "Association et croisement de données provenant d’autres sources"

#: src/settings/pages/Iab_Page.php:243
msgid "Link different devices"
msgstr "Appariement de différents appareils"

#: src/settings/pages/Iab_Page.php:247
msgid "Identify devices based on information transmitted automatically"
msgstr ""
"Identification d’appareils en fonction des informations transmises "
"automatiquement"

#: src/settings/pages/Iab_Page.php:253
msgid "Use precise geolocation data"
msgstr "Utilisation de données de géolocalisation précises"

#: src/settings/pages/Iab_Page.php:257
msgid "Actively scan device characteristics for identification"
msgstr ""
"Analyse active des caractéristiques de l’appareil pour son identification"

#: src/shortcode/Cookiebot_Declaration_Shortcode.php:53
msgid "Please add your Cookiebot ID to show Cookie Declarations"
msgstr ""
"Veuillez ajouter votre identifiant Cookiebot pour afficher les déclarations "
"de cookies"

#: src/shortcode/Cookiebot_Embedding_Shortcode.php:28
msgid "Please add a service ID into the shortcode \"service\" parameter."
msgstr "Ajoutez un ID de service dans le paramètre « service » du shortcode."

#: src/view/admin/cb_frame/dashboard-page.php:33
msgid "I already have a Cookiebot CMP account"
msgstr "Je possède déjà un compte Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:37
msgid "Connect my existing account"
msgstr "Connecter mon compte existant"

#: src/view/admin/cb_frame/dashboard-page.php:42
msgid "Your Cookiebot CMP for WordPress solution"
msgstr "Votre solution Cookiebot CMP pour WordPress"

#: src/view/admin/cb_frame/dashboard-page.php:47
#: src/view/admin/uc_frame/dashboard-page.php:35
msgid "Account added"
msgstr "Compte ajouté"

#: src/view/admin/cb_frame/dashboard-page.php:50
msgid "Congratulations!"
msgstr "Félicitations !"

#: src/view/admin/cb_frame/dashboard-page.php:51
msgid "You have added your Domain Group ID to WordPress. You are all set!"
msgstr ""
"Vous avez ajouté l’identifiant de votre groupe de domaines à WordPress. Vous "
"êtes prêt !"

#: src/view/admin/cb_frame/dashboard-page.php:62
#: src/view/admin/uc_frame/dashboard-page.php:48
msgid "Your opinion matters"
msgstr "Votre opinion compte"

#: src/view/admin/cb_frame/dashboard-page.php:65
msgid ""
"Are you happy with our WordPress plugin? Your feedback will help us make our "
"product better for you."
msgstr ""
"Êtes-vous satisfait du plugin WordPress ?  Vos commentaires nous aideront à "
"améliorer notre produit pour vous."

#: src/view/admin/cb_frame/dashboard-page.php:69
msgid "Write a review"
msgstr "Rédiger un avis"

#: src/view/admin/cb_frame/dashboard-page.php:80
#: src/view/admin/common/dashboard-page.php:45
msgid "Get started"
msgstr "Démarrer "

#: src/view/admin/cb_frame/dashboard-page.php:83
msgid "Create a new Cookiebot CMP account"
msgstr "Créer un compte Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:87
#: src/view/admin/common/dashboard-page.php:52
msgid "Create a new account"
msgstr "Créer un compte"

#: src/view/admin/cb_frame/dashboard-page.php:92
msgid "Learn more about how to optimize your Cookiebot CMP setup?"
msgstr ""
"Vous voulez en savoir plus sur la façon d’optimiser votre configuration de "
"Cookiebot CMP ?"

#: src/view/admin/cb_frame/dashboard-page.php:96
msgid "Visit Help Center"
msgstr "Consultez notre Centre d’aide"

#: src/view/admin/cb_frame/dashboard-page.php:113
#: src/view/admin/cb_frame/support-page.php:43
#: src/view/admin/common/dashboard-page.php:63
#: src/view/admin/common/support-page.php:43
#: src/view/admin/uc_frame/dashboard-page.php:66
#: src/view/admin/uc_frame/support-page.php:43
msgid "Video guide"
msgstr "Vidéoguide"

#: src/view/admin/cb_frame/dashboard-page.php:115
msgid "How to get started with Cookiebot CMP"
msgstr "Comment démarrer avec Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:119
msgid "Learn more about Cookiebot CMP"
msgstr "En savoir plus sur Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:131
#: src/view/admin/common/dashboard-page.php:81
#: src/view/admin/uc_frame/dashboard-page.php:84
#: src/view/admin/uc_frame/settings/embeddings-page.php:32
msgid "GDPR"
msgstr "RGPD"

#: src/view/admin/cb_frame/dashboard-page.php:134
#: src/view/admin/common/dashboard-page.php:84
#: src/view/admin/uc_frame/dashboard-page.php:87
msgid "Europe"
msgstr "Europe"

#: src/view/admin/cb_frame/dashboard-page.php:138
#: src/view/admin/cb_frame/dashboard-page.php:140
#: src/view/admin/cb_frame/dashboard-page.php:155
#: src/view/admin/cb_frame/dashboard-page.php:157
#: src/view/admin/common/dashboard-page.php:88
#: src/view/admin/common/dashboard-page.php:90
#: src/view/admin/common/dashboard-page.php:105
#: src/view/admin/common/dashboard-page.php:107
#: src/view/admin/uc_frame/dashboard-page.php:91
#: src/view/admin/uc_frame/dashboard-page.php:93
#: src/view/admin/uc_frame/dashboard-page.php:108
#: src/view/admin/uc_frame/dashboard-page.php:110
msgid "Learn More"
msgstr "En savoir plus"

#: src/view/admin/cb_frame/dashboard-page.php:148
#: src/view/admin/common/dashboard-page.php:98
#: src/view/admin/uc_frame/dashboard-page.php:101
msgid "CCPA"
msgstr "CCPA"

#: src/view/admin/cb_frame/dashboard-page.php:151
#: src/view/admin/common/dashboard-page.php:101
#: src/view/admin/uc_frame/dashboard-page.php:104
msgid "North America"
msgstr "Amérique du Nord"

#: src/view/admin/cb_frame/dashboard-page.php:162
#: src/view/admin/common/dashboard-page.php:112
msgid "See other legislations"
msgstr "Voir les autres législations"

#: src/view/admin/cb_frame/debug-page.php:22
msgid "Debug information"
msgstr "Informations sur le débogage"

#: src/view/admin/cb_frame/debug-page.php:25
msgid ""
"The information below is for debugging purposes. If you have any issues with "
"your Cookiebot CMP integration, this information is the best place to start."
msgstr ""
"Les informations ci-dessous sont communiquées à des fins de débogage. Si "
"vous rencontrez des problèmes avec votre intégration Cookiebot CMP, ces "
"informations constituent un bon point de départ."

#: src/view/admin/cb_frame/debug-page.php:33
#: src/view/admin/uc_frame/debug-page.php:33
msgid "Copy debug information to clipboard"
msgstr "Copier les informations de débogage dans le presse-papiers"

#: src/view/admin/cb_frame/debug-page.php:42
msgid ""
"If you have any issues with the implemenation of Cookiebot CMP, please visit "
"our Support Center."
msgstr ""
"Si vous avez des problèmes avec l’installation de Cookiebot CMP, veuillez "
"vous rendre sur notre centre d’assistance."

#: src/view/admin/cb_frame/debug-page.php:45
#: src/view/admin/uc_frame/debug-page.php:44
msgid "Visit Support Center"
msgstr "Rendez-vous sur notre centre d’assistance"

#: src/view/admin/cb_frame/network-settings-page.php:22
#: src/view/admin/common/network-settings-page.php:23
#: src/view/admin/uc_frame/network-settings-page.php:23
msgid "Network Settings"
msgstr "Paramètres réseau"

#: src/view/admin/cb_frame/network-settings-page.php:33
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:3
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:3
#: src/view/admin/uc_frame/network-settings-page.php:34
msgid "Are you sure?"
msgstr "Confirmez-vous cette action ?"

#: src/view/admin/cb_frame/network-settings-page.php:36
#: src/view/admin/uc_frame/network-settings-page.php:37
msgid ""
"You will need to add a new ID before updating other network settings. If any "
"subsite is using its own account disconnecting this account won’t affect it."
msgstr ""
"Vous devez ajouter un nouvel ID avant de modifier d’autres paramètres "
"réseau. Si un sous-site utilise son propre compte, la déconnexion de ce "
"compte n’aura aucun impact sur lui."

#: src/view/admin/cb_frame/network-settings-page.php:40
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:10
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:10
#: src/view/admin/uc_frame/network-settings-page.php:41
msgid "Cancel"
msgstr "Annuler"

#: src/view/admin/cb_frame/network-settings-page.php:43
#: src/view/admin/cb_frame/network-settings-page.php:65
#: src/view/admin/cb_frame/settings/general-page.php:52
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:13
#: src/view/admin/uc_frame/network-settings-page.php:44
#: src/view/admin/uc_frame/network-settings-page.php:75
#: src/view/admin/uc_frame/settings/general-page.php:45
msgid "Disconnect account"
msgstr "Déconnecter le compte"

#: src/view/admin/cb_frame/network-settings-page.php:50
msgid "Network Domain Group ID"
msgstr "ID du groupe de domaine réseau"

#: src/view/admin/cb_frame/network-settings-page.php:52
msgid ""
"If added this will be the default Cookiebot ID for all subsites. Subsites "
"are able to override the Cookiebot ID."
msgstr ""
"S’il est ajouté, ce sera l’ID Cookiebot par défaut pour tous les sous-sites. "
"Les sous-sites peuvent remplacer l’ID Cookiebot."

#: src/view/admin/cb_frame/network-settings-page.php:54
#: src/view/admin/cb_frame/settings/general-page.php:37
msgid "Read more on the Domain Group ID"
msgstr "En savoir plus sur l’identifiant du groupe de domaine"

#: src/view/admin/cb_frame/network-settings-page.php:58
#: src/view/admin/cb_frame/settings/general-page.php:43
msgid "Add your Domain Group ID"
msgstr "Ajouter l’identifiant de votre groupe de domaine"

#: src/view/admin/cb_frame/network-settings-page.php:73
#: src/view/admin/cb_frame/settings/general-page.php:146
#: src/view/admin/uc_frame/network-settings-page.php:111
#: src/view/admin/uc_frame/settings/general-page.php:120
msgid "Cookie-blocking"
msgstr "Blocage des cookies"

#: src/view/admin/cb_frame/network-settings-page.php:75
msgid ""
"Select your cookie-blocking mode here. Auto cookie-blocking mode will "
"automatically block all cookies (except for ‘strictly necessary’ cookies) "
"until a user has given consent. Manual cookie-blocking mode requests manual "
"adjustments to the cookie-setting scripts. Please find our implementation "
"guides below:"
msgstr ""
"Sélectionnez ici votre mode de blocage des cookies. Le mode de blocage "
"automatique des cookies bloque automatiquement tous les cookies (sauf les "
"cookies “strictement nécessaires”) jusqu’à ce que l’utilisateur ait donné "
"son consentement. Le mode de blocage manuel des cookies demande des "
"ajustements manuels aux scripts de réglage des cookies. Veuillez trouver nos "
"guides de mise en œuvre ci-dessous :"

#: src/view/admin/cb_frame/network-settings-page.php:80
#: src/view/admin/cb_frame/settings/general-page.php:167
#: src/view/admin/uc_frame/network-settings-page.php:118
#: src/view/admin/uc_frame/settings/general-page.php:133
msgid "Select cookie-blocking mode"
msgstr "Sélectionner un mode de blocage des cookies"

#: src/view/admin/cb_frame/network-settings-page.php:87
#: src/view/admin/cb_frame/settings/general-page.php:175
#: src/view/admin/uc_frame/network-settings-page.php:125
#: src/view/admin/uc_frame/settings/general-page.php:141
msgid "Automatic"
msgstr "Automatique"

#: src/view/admin/cb_frame/network-settings-page.php:88
#: src/view/admin/cb_frame/settings/general-page.php:176
#: src/view/admin/uc_frame/network-settings-page.php:126
#: src/view/admin/uc_frame/settings/general-page.php:142
msgid "Recommended"
msgstr "Recommandé"

#: src/view/admin/cb_frame/network-settings-page.php:96
#: src/view/admin/cb_frame/network-settings-page.php:146
#: src/view/admin/cb_frame/network-settings-page.php:193
#: src/view/admin/uc_frame/network-settings-page.php:134
msgid "Choose per subsite"
msgstr "Choisissez par sous-site"

#: src/view/admin/cb_frame/network-settings-page.php:104
#: src/view/admin/cb_frame/settings/general-page.php:206
msgid "Cookiebot™ script tag"
msgstr "Balise de script Cookiebot™"

#: src/view/admin/cb_frame/network-settings-page.php:106
msgid ""
"Add async or defer attribute to Cookie banner script tag. Default: Choose "
"per subsite"
msgstr ""
"Ajoutez un attribut async ou defer à la balise de script de bannière de "
"cookie. Par défaut : choisissez par sous-site"

#: src/view/admin/cb_frame/network-settings-page.php:109
msgid "This feature is only available when not using Auto Blocking"
msgstr ""
"Cette fonction n'est disponible que lorsque vous n'utilisez pas le blocage "
"automatique"

#: src/view/admin/cb_frame/network-settings-page.php:112
#: src/view/admin/cb_frame/network-settings-page.php:156
#: src/view/admin/cb_frame/network-settings-page.php:228
#: src/view/admin/uc_frame/network-settings-page.php:169
msgid ""
"Setting will apply for all subsites. Subsites will not be able to override."
msgstr ""
"Le paramètre s’appliquera à tous les sous-sites. Les sous-sites ne pourront "
"pas remplacer."

#: src/view/admin/cb_frame/network-settings-page.php:125
#: src/view/admin/cb_frame/network-settings-page.php:172
#: src/view/admin/cb_frame/settings/additional-page.php:99
#: src/view/admin/cb_frame/settings/general-page.php:222
msgid "None"
msgstr "Aucun"

#: src/view/admin/cb_frame/network-settings-page.php:154
msgid "Cookiebot declaration script tag"
msgstr "Balise de script de déclaration de cookies"

#: src/view/admin/cb_frame/network-settings-page.php:159
msgid ""
"Add async or defer attribute to Cookie declaration script tag. Default: "
"Choose per subsite"
msgstr ""
"Ajoutez l’attribut async ou defer à la balise de script de déclaration de "
"cookie. Par défaut : choisissez par sous-site"

#: src/view/admin/cb_frame/network-settings-page.php:201
#: src/view/admin/cb_frame/settings/additional-page.php:10
#: src/view/admin/uc_frame/network-settings-page.php:142
#: src/view/admin/uc_frame/settings/additional-page.php:10
msgid "Automatic updates"
msgstr "Mises à jour automatiques"

#: src/view/admin/cb_frame/network-settings-page.php:203
#: src/view/admin/cb_frame/settings/additional-page.php:13
#: src/view/admin/uc_frame/network-settings-page.php:144
#: src/view/admin/uc_frame/settings/additional-page.php:13
msgid ""
"Enable automatic updates whenever we release a new version of the plugin."
msgstr ""
"Activez les mises à jour automatiques à chaque nouvelle version du plugin."

#: src/view/admin/cb_frame/network-settings-page.php:218
#: src/view/admin/cb_frame/settings/additional-page.php:28
#: src/view/admin/uc_frame/network-settings-page.php:159
#: src/view/admin/uc_frame/settings/additional-page.php:28
msgid "Automatically update to new version"
msgstr "Mise à jour automatique à la dernière version"

#: src/view/admin/cb_frame/network-settings-page.php:226
#: src/view/admin/cb_frame/settings/general-page.php:248
#: src/view/admin/uc_frame/network-settings-page.php:167
#: src/view/admin/uc_frame/settings/general-page.php:172
msgid "Hide cookie popup"
msgstr "Masquer la fenêtre popup de cookies"

#: src/view/admin/cb_frame/network-settings-page.php:231
#: src/view/admin/cb_frame/settings/general-page.php:254
#: src/view/admin/uc_frame/network-settings-page.php:172
#: src/view/admin/uc_frame/settings/general-page.php:178
msgid ""
"This will remove the cookie consent banner from your website. The cookie "
"declaration shortcode will still be available if you are using Google Tag "
"Manager (or equal), you need to add the Cookiebot script in your Tag Manager."
msgstr ""
"Cette action supprimera le bandeau de consentement des cookies de votre "
"site. Si vous utilisez Google Tag Manager ou un équivalent, vous devrez "
"ajouter le script Cookiebot dans votre Tag Manager."

#: src/view/admin/cb_frame/network-settings-page.php:246
#: src/view/admin/cb_frame/settings/general-page.php:280
#: src/view/admin/uc_frame/network-settings-page.php:187
#: src/view/admin/uc_frame/settings/general-page.php:204
msgid "Hide the cookie popup banner"
msgstr "Masquer la bannière popup de cookies"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:11
#: src/view/admin/common/prior-consent/available-addons/tab-header.php:4
#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:14
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:14
msgid "Remember to save your changes before switching tabs"
msgstr ""
"N’oubliez pas d’enregistrer vos modifications avant de changer d’onglet"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:20
msgid "Consent Level API Settings"
msgstr "Paramètres API pour le niveau de consentement"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:29
msgid ""
"WP Consent Level API and Cookiebot™ categorize cookies a bit differently. "
"The default settings should fit most needs, but if you need to change the "
"mapping you can do so below."
msgstr ""
"WP Consent Level API et Cookiebot™ catégorisent les cookies un peu "
"différemment. Les paramètres par défaut devraient convenir à la plupart des "
"besoins, mais si vous avez besoin de modifier la catégorisation, vous pouvez "
"le faire ci-dessous."

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:79
msgid "Cookiebot™ cookie categories"
msgstr "Catégories de cookies Cookiebot™"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:96
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:51
msgid "WP Consent API cookies categories equivalent"
msgstr "Équivalent des catégories de cookies WP Consent API"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:102
msgid "Functional"
msgstr "Fonctionnels"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:150
msgid "Statistics Anonymous"
msgstr "Statistiques anonymes"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:179
msgid "Reset to default mapping"
msgstr "Réinitialiser les paramètres par défaut"

#: src/view/admin/cb_frame/settings-page.php:48
msgid "Select at least one vendor on TCF tab"
msgstr "Sélectionnez au moins un fournisseur dans l'onglet TCF"

#: src/view/admin/cb_frame/settings-page.php:57
#: src/view/admin/uc_frame/settings-page.php:54
msgid "General Settings"
msgstr "Paramètres généraux"

#: src/view/admin/cb_frame/settings-page.php:61
#: src/view/admin/uc_frame/settings-page.php:58
msgid "Additional Settings"
msgstr "Paramètres supplémentaires"

#: src/view/admin/cb_frame/settings-page.php:65
#: src/view/admin/cb_frame/settings/gtm-page.php:27
#: src/view/admin/uc_frame/settings-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:12
#: src/view/admin/uc_frame/settings/gtm-page.php:27
msgid "Google Tag Manager"
msgstr "Google Tag Manager"

#: src/view/admin/cb_frame/settings-page.php:69
#: src/view/admin/cb_frame/settings/gcm-page.php:50
#: src/view/admin/uc_frame/settings-page.php:66
#: src/view/admin/uc_frame/settings/gcm-page.php:28
#: src/view/admin/uc_frame/settings/gcm-page.php:43
msgid "Google Consent Mode"
msgstr "Mode Consentement de Google"

#: src/view/admin/cb_frame/settings-page.php:73
#: src/view/admin/uc_frame/settings/embeddings-page.php:33
msgid "TCF"
msgstr "TCF"

#: src/view/admin/cb_frame/settings-page.php:77
msgid "Multiple Configurations"
msgstr "Configurations multiples"

#: src/view/admin/cb_frame/settings/additional-page.php:37
#: src/view/admin/uc_frame/settings/additional-page.php:37
msgid "Show the banner while logged in"
msgstr "Afficher le bandeau en étant connecté"

#: src/view/admin/cb_frame/settings/additional-page.php:40
#: src/view/admin/uc_frame/settings/additional-page.php:40
msgid ""
"You can choose to display the consent banner on your website while you’re "
"logged in and changing settings or customizing your banner."
msgstr ""
"Vous pouvez choisir d’afficher le bandeau de consentement sur votre site "
"même lorsque vous êtes connecté pour effectuer des modifications ou "
"personnaliser votre bandeau."

#: src/view/admin/cb_frame/settings/additional-page.php:66
#: src/view/admin/uc_frame/settings/additional-page.php:66
msgid "Show banner on website when logged in"
msgstr "Afficher le bandeau pendant la connexion"

#: src/view/admin/cb_frame/settings/additional-page.php:75
msgid "Cookie declaration script tag:"
msgstr "Balise de script de déclaration de cookies :"

#: src/view/admin/cb_frame/settings/additional-page.php:78
msgid ""
"If you implemented the declaration on your page through our widget in "
"WordPress, you can choose here how the script should be loaded."
msgstr ""
"Si vous avez intégré la déclaration sur votre page grâce à notre widget dans "
"WordPress, vous pouvez choisir ici comment le script doit être chargé."

#: src/view/admin/cb_frame/settings/additional-page.php:84
msgid "Select the cookie declaration script loading setting"
msgstr ""
"Sélectionnez le paramètre de chargement du script de déclaration de cookies"

#: src/view/admin/cb_frame/settings/additional-page.php:116
#: src/view/admin/cb_frame/settings/general-page.php:187
#: src/view/admin/cb_frame/settings/general-page.php:239
#: src/view/admin/cb_frame/settings/general-page.php:283
#: src/view/admin/uc_frame/settings/general-page.php:153
#: src/view/admin/uc_frame/settings/general-page.php:207
msgid "Disabled by active setting in Network Settings"
msgstr "Désactivé par un paramètre actif dans les paramètres réseau"

#: src/view/admin/cb_frame/settings/additional-page.php:125
msgid "Ignore scripts in queue from Cookiebot CMP scan:"
msgstr "Ignorer les scripts en attente du scan Cookiebot CMP :"

#: src/view/admin/cb_frame/settings/additional-page.php:128
msgid ""
"List scripts source URL (one per line) from the queue to ignore Cookiebot "
"CMP scan. Partial source URL will also work, e.g. wp-content/plugins/"
"woocommerce will block every WooCommerce script."
msgstr ""
"Liste des URL source des scripts (une par ligne) en attente pour ignorer le "
"scan CMP de Cookiebot. Une URL source partielle fonctionnera également, par "
"exemple, wp-content/plugins/woocommerce bloquera tous les scripts "
"WooCommerce."

#: src/view/admin/cb_frame/settings/additional-page.php:131
msgid ""
"This feature only works for scripts loaded via wp_enqueue_script. Manually "
"added scripts must be manually edited."
msgstr ""
"Cette fonctionnalité ne fonctionne que pour les scripts chargés via "
"wp_enqueue_script. Les scripts ajoutés manuellement doivent être modifiés "
"manuellement."

#: src/view/admin/cb_frame/settings/additional-page.php:137
msgid "Script source URL:"
msgstr "URL de la source du script :"

#: src/view/admin/cb_frame/settings/additional-page.php:143
msgid "Add script source URL, one per line"
msgstr "Ajoutez une URL source du script par ligne"

#: src/view/admin/cb_frame/settings/gcm-page.php:14
msgid "What is Google Consent Mode and why should you enable it?"
msgstr ""
"Qu’est-ce que le mode Consentement de Google et pourquoi devez-vous "
"l’activer ?"

#: src/view/admin/cb_frame/settings/gcm-page.php:17
msgid ""
"Google Consent Mode is a way for your website to measure conversions and get "
"analytics insights while being fully GDPR-compliant when using services like "
"Google Analytics, Google Tag Manager (GTM) and Google Ads."
msgstr ""
"Le mode Consentement de Google est un moyen pour votre site web de mesurer "
"les conversions et d’obtenir des données d’analyse tout en étant entièrement "
"conforme au RGPD lors de l’utilisation de services tels que Google "
"Analytics, Google Tag Manager (GTM) et Google Ads."

#: src/view/admin/cb_frame/settings/gcm-page.php:20
msgid ""
"Cookiebot consent managment platform (CMP) and Google Consent Mode integrate "
"seamlessly to offer you plug-and-play compliance and streamlined use of all "
"Google's services in one easy solution."
msgstr ""
"La plateforme de gestion du consentement (CMP) de Cookiebot™ et le mode "
"Consentement de Google s’intègrent parfaitement pour vous offrir une "
"conformité plug-and-play et une utilisation simplifiée de tous les services "
"de Google grâce à une solution unique."

#: src/view/admin/cb_frame/settings/gcm-page.php:24
msgid "Read more about Cookiebot CMP and Google Consent Mode"
msgstr ""
"En savoir plus sur la plateforme de gestion du consentement Cookiebot et le "
"mode Consentement de Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:31
msgid "Google Consent Mode:"
msgstr "Mode Consentement Google :"

#: src/view/admin/cb_frame/settings/gcm-page.php:34
msgid ""
"Enable Google Consent Mode with default settings on your WordPress page."
msgstr ""
"Activez le mode Consentement de Google avec les paramètres par défaut sur "
"votre page WordPress."

#: src/view/admin/cb_frame/settings/gcm-page.php:38
#: src/view/admin/cb_frame/settings/gcm-page.php:68
#: src/view/admin/cb_frame/settings/gtm-page.php:18
msgid "Read more"
msgstr "Plus d’informations"

#: src/view/admin/cb_frame/settings/gcm-page.php:61
msgid "URL passthrough:"
msgstr "Passerelle d’URL :"

#: src/view/admin/cb_frame/settings/gcm-page.php:64
msgid ""
"This feature will allow you to pass data between pages when not able to use "
"cookies without/prior consent."
msgstr ""
"Cette fonctionnalité vous permettra de transmettre des données entre les "
"pages lorsque vous n’êtes pas en mesure d’utiliser des cookies sans / "
"consentement préalable."

#: src/view/admin/cb_frame/settings/gcm-page.php:80
msgid "URL passthrough"
msgstr "Passerelle d’URL"

#: src/view/admin/cb_frame/settings/gcm-page.php:89
msgid "Google Consent Mode cookies"
msgstr "Cookies du mode Consentement de Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:91
msgid ""
"Select the cookie types that need to be consented for the Google Consent "
"Mode script"
msgstr ""
"Sélectionner les types de cookies qui doivent faire l'objet d'un "
"consentement pour le script du mode Consentement de Google"

#: src/view/admin/cb_frame/settings/gcm-page.php:93
#: src/view/admin/cb_frame/settings/general-page.php:212
#: src/view/admin/cb_frame/settings/general-page.php:251
#: src/view/admin/cb_frame/settings/gtm-page.php:80
#: src/view/admin/uc_frame/settings/general-page.php:175
msgid "This feature is only available when using Manual Blocking"
msgstr ""
"Cette fonction n'est disponible que lorsque vous utilisez le blocage manuel"

#: src/view/admin/cb_frame/settings/gcm-page.php:95
#: src/view/admin/cb_frame/settings/gtm-page.php:82
msgid ""
"This option may affect the behaviour of your GTM Tags, as the script will "
"run on the selected cookies consent."
msgstr ""
"Cette option peut affecter le comportement de vos balises GTM, car le script "
"s'exécutera sur la base du consentement des cookies sélectionnés."

#: src/view/admin/cb_frame/settings/gcm-page.php:97
#: src/view/admin/cb_frame/settings/gtm-page.php:84
msgid ""
"Please make sure your Tags in Google Tag Manager are triggered correctly."
msgstr ""
"Veuillez vous assurer que vos balises dans Google Tag Manager sont "
"correctement activées"

#: src/view/admin/cb_frame/settings/gcm-page.php:102
#: src/view/admin/cb_frame/settings/gtm-page.php:89
#: src/view/admin/common/prior-consent/available-addons/tab.php:28
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:25
msgid "Check one or multiple cookie types:"
msgstr "Cochez un ou plusieurs types de cookies :"

#: src/view/admin/cb_frame/settings/general-page.php:30
msgid "Connect your Domain Group"
msgstr "Connecter votre groupe de domaines"

#: src/view/admin/cb_frame/settings/general-page.php:33
msgid ""
"To connect your Domain Group, paste your Domain Group ID here. If you want "
"to connect a second ID for other regions, you can do this under the "
"\"Multiple Configurations\" tab."
msgstr ""
"Pour connecter votre groupe de domaine, copiez votre identifiant de groupe "
"de domaine ici. Si vous souhaitez connecter un deuxième identifiant pour "
"d’autres régions, vous pouvez le faire sous l’onglet “Configurations "
"multiples”."

#: src/view/admin/cb_frame/settings/general-page.php:54
#: src/view/admin/uc_frame/settings/general-page.php:48
msgid "Using network account"
msgstr "Utiliser un compte réseau"

#: src/view/admin/cb_frame/settings/general-page.php:64
#: src/view/admin/uc_frame/settings/general-page.php:59
msgid "Do not use Network Settings ID"
msgstr "Ne pas utiliser l’ID de compte réseau"

#: src/view/admin/cb_frame/settings/general-page.php:75
msgid "Language:"
msgstr "Langue :"

#: src/view/admin/cb_frame/settings/general-page.php:77
msgid "Select your main language here."
msgstr "Sélectionnez ici votre langue principale."

#: src/view/admin/cb_frame/settings/general-page.php:82
msgid "Select the language"
msgstr "Sélectionnez la langue"

#: src/view/admin/cb_frame/settings/general-page.php:86
msgid "Default (Autodetect)"
msgstr "Par défaut (détection automatique)"

#: src/view/admin/cb_frame/settings/general-page.php:90
msgid "Use WordPress Language"
msgstr "Utiliser la langue de WordPress"

#: src/view/admin/cb_frame/settings/general-page.php:111
msgid ""
"If enabled, Cookiebot™ will use the current location to set the banner and "
"cookie declaration language."
msgstr ""
"Si cette option est activée, Cookiebot™ utilisera la localisation actuelle "
"pour définir la langue du bandeau et de la déclaration de cookies."

#: src/view/admin/cb_frame/settings/general-page.php:114
msgid ""
"Please make sure that all languages in use have been added in the Cookiebot™ "
"Manager."
msgstr ""
"Veuillez vous assurer que toutes les langues utilisées ont également été "
"ajoutées dans le gestionnaire Cookiebot™."

#: src/view/admin/cb_frame/settings/general-page.php:116
msgid "This feature disables the main language selector."
msgstr "Cette fonctionnalité désactive le sélecteur de langue principale."

#: src/view/admin/cb_frame/settings/general-page.php:117
msgid ""
"If you have already set a language in the cookie declaration shortcode, this "
"feature will not change it."
msgstr ""
"Si vous avez déjà défini une langue dans le shortcode de la déclaration de "
"cookie, cette fonctionnalité ne la modifiera pas."

#: src/view/admin/cb_frame/settings/general-page.php:120
msgid "Read more on how to add languages"
msgstr "En savoir plus sur la façon d’ajouter des langues"

#: src/view/admin/cb_frame/settings/general-page.php:135
msgid "Use website location to set language"
msgstr "Utiliser la localisation du site web pour définir la langue."

#: src/view/admin/cb_frame/settings/general-page.php:149
#: src/view/admin/uc_frame/network-settings-page.php:113
#: src/view/admin/uc_frame/settings/general-page.php:123
msgid ""
"Choose the type of your cookie-blocking mode. Select automatic to "
"automatically block all cookies except those strictly necessary to use "
"before user gives consent. Manual mode lets you adjust your cookie settings "
"within your website’s HTML."
msgstr ""
"Choisissez entre le mode automatique, qui bloque toutes les cookies non "
"essentiels avant consentement, ou le mode manuel, qui vous permet d’ajuster "
"les réglages dans le HTML de votre site."

#: src/view/admin/cb_frame/settings/general-page.php:154
msgid "Guide to auto cookie-blocking"
msgstr "Guide du blocage automatique des cookies"

#: src/view/admin/cb_frame/settings/general-page.php:160
msgid "Guide to manual cookie-blocking"
msgstr "Guide du blocage manuel des cookies"

#: src/view/admin/cb_frame/settings/general-page.php:184
#: src/view/admin/uc_frame/settings/general-page.php:150
msgid "Manual"
msgstr "Manuel"

#: src/view/admin/cb_frame/settings/general-page.php:209
msgid "Add async or defer attribute to cookie declaration script tag"
msgstr ""
"Ajouter les attributs “async” ou “defer” à la balise de script de la "
"déclaration de cookies."

#: src/view/admin/cb_frame/settings/gtm-page.php:12
msgid "Google Tag Manager:"
msgstr "Google Tag Manager:"

#: src/view/admin/cb_frame/settings/gtm-page.php:14
msgid "For more details about Cookiebot CMP and Google Tag Manager."
msgstr "Pour plus de détails sur Cookiebot CMP et Google Tag Manager."

#: src/view/admin/cb_frame/settings/gtm-page.php:35
#: src/view/admin/cb_frame/settings/gtm-page.php:46
#: src/view/admin/uc_frame/settings/gtm-page.php:35
#: src/view/admin/uc_frame/settings/gtm-page.php:46
msgid "Google Tag Manager ID"
msgstr "Identifiant Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:37
msgid "Paste your Tag Manager ID into the field on the right."
msgstr "Copiez votre identifiant Tag Manager dans le champ à droite."

#: src/view/admin/cb_frame/settings/gtm-page.php:41
#: src/view/admin/uc_frame/settings/gtm-page.php:41
msgid "How to find the GTM ID"
msgstr "Comment trouver l’identifiant GTM"

#: src/view/admin/cb_frame/settings/gtm-page.php:48
msgid "Enter GTM ID"
msgstr "Entrer l’identifiant GTM"

#: src/view/admin/cb_frame/settings/gtm-page.php:56
msgid "Data Layer Name (optional)"
msgstr "Nom de la couche de données (facultatif)"

#: src/view/admin/cb_frame/settings/gtm-page.php:58
msgid ""
"You can also paste your Data Layer Name here. This is optional information."
msgstr ""
"Vous pouvez également copier le nom de votre couche de données ici. Cette "
"information est facultative."

#: src/view/admin/cb_frame/settings/gtm-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:62
msgid "How to find the Data Layer Name"
msgstr "Comment trouver le nom de la couche de données"

#: src/view/admin/cb_frame/settings/gtm-page.php:67
#: src/view/admin/uc_frame/settings/gtm-page.php:67
msgid "Name of your Data Layer"
msgstr "Nom de votre couche de données"

#: src/view/admin/cb_frame/settings/gtm-page.php:76
msgid "Google Tag Manager cookies"
msgstr "Cookies du Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:78
msgid ""
"Select the cookie types that need to be consented for the Google Tag Manager "
"script"
msgstr ""
"Sélectionner les types de cookies qui doivent faire l'objet d'un "
"consentement pour le script de Google Tag Manager"

#: src/view/admin/cb_frame/settings/iab-page.php:26
msgid "IAB Integration:"
msgstr "Intégration IAB"

#: src/view/admin/cb_frame/settings/iab-page.php:28
msgid ""
"If you want to use the IAB Framework TCF within your Consent Management "
"Platform (CMP) you can enable it on the right. Be aware that activating this "
"could override some of the configurations you made with the default setup "
"defined by the IAB."
msgstr ""
"Si vous souhaitez utiliser le cadre TCF de l’IAB sur votre plateforme de "
"gestion du consentements (CMP), vous pouvez l’activer sur la droite. Sachez "
"que cette activation peut annuler certaines des configurations que vous avez "
"faites avec la configuration par défaut définie par l’IAB."

#: src/view/admin/cb_frame/settings/iab-page.php:32
msgid "Read more on IAB with Cookiebot CMP here"
msgstr "Pour en savoir plus sur l’IAB et Cookiebot CMP, cliquez ici"

#: src/view/admin/cb_frame/settings/iab-page.php:41
msgid "IAB TCF V2.2 integration"
msgstr "Intégration de l’IAB TCF 2.2"

#: src/view/admin/cb_frame/settings/iab-page.php:83
msgid ""
"IAB vendor list is temporarily offline. Please try refreshing the page after "
"a couple of minutes."
msgstr ""
"La liste des fournisseurs IAB est momentanément inaccessible. Veuillez "
"rafraîchir la page dans quelques minutes."

#: src/view/admin/cb_frame/settings/iab-page.php:85
msgid ""
"If you had previously saved configurations, don’t worry, they will continue "
"to work."
msgstr ""
"Si vous aviez sauvegardé des configurations auparavant, ne vous inquiétez "
"pas, elles resteront fonctionnelles."

#: src/view/admin/cb_frame/settings/iab-page.php:108
msgid "Sharing data with third-party vendors"
msgstr "Partage de données avec des tiers"

#: src/view/admin/cb_frame/settings/iab-page.php:112
msgid ""
"Select vendors with whom you’ll share users’ data. We’ll include this "
"information on the second layer of your consent banner, where users "
"interested in more granular detail about who will access their data can view "
"it."
msgstr ""
"Choisissez les tiers avec lesquels vous partagerez les données des "
"utilisateurs. Les utilisateurs désireux d’obtenir plus de détails sur les "
"destinataires de leurs données trouveront cette information dans la deuxième "
"section de votre bandeau de consentement."

#: src/view/admin/cb_frame/settings/iab-page.php:130
#: src/view/admin/cb_frame/settings/iab-page.php:162
#: src/view/admin/cb_frame/settings/iab-page.php:218
msgid "Search"
msgstr "Recherche"

#: src/view/admin/cb_frame/settings/iab-page.php:131
#: src/view/admin/cb_frame/settings/iab-page.php:163
msgid "Select All"
msgstr "Tout sélectionner"

#: src/view/admin/cb_frame/settings/iab-page.php:132
#: src/view/admin/cb_frame/settings/iab-page.php:164
msgid "Deselect All"
msgstr "Tout désélectionner"

#: src/view/admin/cb_frame/settings/iab-page.php:133
msgid "Select at least one vendor"
msgstr "Sélectionnez au moins un fournisseur"

#: src/view/admin/cb_frame/settings/iab-page.php:157
msgid "Google Ads certified external vendors"
msgstr "Fournisseurs externes certifiés par Google Ads"

#: src/view/admin/cb_frame/settings/iab-page.php:188
msgid "Restrictions of data use purposes for vendors"
msgstr "Restrictions des fins d’utilisation des données pour les fournisseurs"

#: src/view/admin/cb_frame/settings/iab-page.php:192
msgid ""
"Set restrictions on data use purposes for specific vendors. Add vendors and "
"the data use purposes that each vendor is allowed. We’ll share this "
"information with users within your consent banner."
msgstr ""
"Établissez des restrictions sur les objectifs d’utilisation des données pour "
"des fournisseurs particuliers. Incluez les fournisseurs ainsi que les "
"autorisations pour chaque fournisseur en matière d’utilisation des données. "
"Ces informations seront communiquées aux utilisateurs dans votre bandeau de "
"consentement."

#: src/view/admin/cb_frame/settings/iab-page.php:198
msgid "Add Vendor"
msgstr "Ajouter un fournisseur"

#: src/view/admin/cb_frame/settings/iab-page.php:204
#: src/view/admin/cb_frame/settings/iab-page.php:215
msgid "Select Vendor"
msgstr "Sélectionnez un fournisseur"

#: src/view/admin/cb_frame/settings/iab-page.php:228
msgid "Set Purposes"
msgstr "Configurer des finalités"

#: src/view/admin/cb_frame/settings/multiple-configuration/extra/region-item.php:18
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:75
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:101
msgid "Select region"
msgstr "Sélectionner la région"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:16
msgid "Additional configurations:"
msgstr "Configurations supplémentaires :"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:18
msgid ""
"You can add a second alternative banner or configuration to your website by "
"creating a second Domain Group and specify it on a region."
msgstr ""
"Vous pouvez ajouter un deuxième bandeau ou une configuration alternative à "
"votre site web en créant un deuxième groupe de domaines et en le définissant "
"pour une région."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:22
msgid "Read more about multiple configurations here"
msgstr "Pour en savoir plus sur les configurations multiples, cliquez ici"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:34
msgid "Multiple configurations"
msgstr "Configurations multiples"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:44
msgid "Set up your additional banner configuration:"
msgstr "Mettez en place votre configuration de bandeau supplémentaire :"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:47
msgid ""
"To enable a different configuration, create a separate DomainGroup without "
"adding the domain to it and paste the ID below. Then select the countries in "
"which you want to show this configuration. For example, if your main Domain "
"Group is defined as a banner matching GDPR requirements, you might want to "
"add another Domain Group for visitors from California. The number of "
"additional configurations is restricted to one at the moment."
msgstr ""
"Pour activer une configuration différente, créez un groupe de domaines "
"distinct sans y ajouter le domaine et collez l’identifiant ci-dessous. "
"Sélectionnez ensuite les pays dans lesquels vous souhaitez afficher cette "
"configuration. Par exemple, si votre groupe de domaines principal est défini "
"comme un bandeau répondant aux exigences du RGPD, vous pouvez ajouter un "
"autre groupe de domaines pour les internautes de Californie. Le nombre de "
"configurations supplémentaires est limité à une seule pour le moment."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:57
msgid "Domain Group ID"
msgstr "Identifiant du groupe de domaine"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:61
msgid "Region"
msgstr "Région"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:71
msgid "Primary domain group"
msgstr "Groupe de domaine primaire"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:138
msgid "Add banner"
msgstr "Ajouter une bannière"

#: src/view/admin/cb_frame/support-page.php:23
msgid "Need help with your configuration?"
msgstr "Vous avez besoin d’aide pour la configuration ?"

#: src/view/admin/cb_frame/support-page.php:26
msgid ""
"In our Help Center you find all the answers to your questions. If you have "
"additional questions, create a support request and our Support Team will "
"help out as soon as possible."
msgstr ""
"Vous trouverez toutes les réponses à vos questions sur notre Centre d’aide. "
"Si vous avez d’autres questions, créez une demande d’assistance et notre "
"équipe d’assistance vous aidera dès que possible."

#: src/view/admin/cb_frame/support-page.php:34
msgid "Visit Cookiebot CMP Help Center"
msgstr "Consultez notre Centre d’aide"

#: src/view/admin/cb_frame/support-page.php:58
msgid "How to find my Cookiebot™ ID"
msgstr "Comment trouver mon identifiant Cookiebot™"

#. translators: the first placeholder string will be replaced with a html anchor open tag and the second placeholder string will be replaced by the html anchor closing tag
#: src/view/admin/cb_frame/support-page.php:66
msgid "Log in to your %1$sCookiebot CMP account%2$s."
msgstr "Connectez-vous à votre %1$scompte Cookiebot CMP%2$s."

#. translators: the placeholder strings denote the positions of <b>, </b>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:76
msgid "Go to %1$s\"Settings\"%2$s and setup your Cookiebot CMP"
msgstr "Allez dans %1$s« Paramètres »%2$s et configurez votre CMP Cookiebot™"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:86
msgid "Go to the %1$s\"Your scripts\"%2$s tab"
msgstr "Allez dans l’onglet %1$s« Vos scripts »%2$s"

#: src/view/admin/cb_frame/support-page.php:92
msgid ""
"Copy the value inside the data-cid parameter - eg.: abcdef12-3456-7890-abcd-"
"ef1234567890"
msgstr ""
"Copiez la valeur dans le paramètre data-cid, par ex. : abcdef12-3456-7890-"
"abcd-ef1234567890"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:97
msgid ""
"Add %1$s[cookie_declaration]%2$s shortcode to a page to show the declaration"
msgstr ""
"Ajoutez le shortcode %1$s[cookie_declaration]%2$s à une page pour afficher "
"la déclaration"

#: src/view/admin/cb_frame/support-page.php:103
msgid "Remember to change your scripts as described below"
msgstr "N’oubliez pas de modifier vos scripts comme décrit ci-dessous "

#: src/view/admin/cb_frame/support-page.php:110
msgid "Add the Cookie Declaration to your website"
msgstr "Ajoutez la Déclaration de cookies à votre site web"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:116
msgid ""
"Use the shortcode %1$s[cookie_declaration]%2$s to add the cookie declaration "
"to a page or post. The cookie declaration will always show the latest "
"version from Cookiebot CMP."
msgstr ""
"Utilisez le shortcode %1$s[cookie_declaration]%2$s pour ajouter la "
"déclaration de cookies à une page ou à un poste. La déclaration de cookies "
"affichera toujours la dernière version de Cookiebot CMP."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:126
msgid ""
"If you want to show the cookie declaration in a specific language, you can "
"add the %1$s\"lang\"%2$s attribute, e.g. %3$s[cookie_declaration "
"lang=\"de\"]%4$s."
msgstr ""
"Si vous souhaitez afficher la déclaration de cookies dans une langue "
"spécifique, vous pouvez ajouter l’attribut %1$s« lang »%2$s , par ex. "
"%3$s[cookie_declaration lang=“de”]%4$s."

#: src/view/admin/cb_frame/support-page.php:138
msgid "Update your script tags"
msgstr "Mettez à jour vos balises de scripts"

#: src/view/admin/cb_frame/support-page.php:142
msgid ""
"To enable prior consent, apply the attribute \"data-cookieconsent\" to "
"cookie-setting script tags on your website. Set the comma-separated value to "
"one or more of the cookie categories \"preferences\", \"statistics\" and/or "
"\"marketing\" in accordance with the types of cookies being set by each "
"script. Finally, change the attribute \"type\" from \"text/javascript\" to "
"\"text/plain\"."
msgstr ""
"Pour permettre le consentement préalable, appliquez l’attribut « data-"
"cookieconsent » aux balises de script définissant les cookies sur votre site "
"web. Attribuez la valeur séparée par une virgule à une ou plusieurs des "
"catégories de cookies « préférences » , « statistiques » et/ou « marketing » "
"en fonction des types de cookies définis par chaque script. Enfin, modifiez "
"l’attribut « type » de « text/javascript » en « text/plain »."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:152
msgid ""
"Example on modifying an existing Google Analytics Universal script tag can "
"be found %1$shere in step 4%2$s."
msgstr ""
"Vous trouverez un exemple de modification d’une balise de script Google "
"Analytics Universal existante à %1$sl’étape 4%2$s."

#: src/view/admin/cb_frame/support-page.php:172
msgid "Helper function to update your scripts"
msgstr "Fonction d’aide pour mettre à jour vos scripts"

#: src/view/admin/cb_frame/support-page.php:176
msgid ""
"You can update your scripts yourself. However, Cookiebot CMP also offers a "
"small helper function that can make the work easier."
msgstr ""
"Vous pouvez mettre vos scripts à jour vous-même. Cependantm Cookiebot CMP "
"propose également une petite fonction d’aide qui peut vous faciliter la "
"tâche."

#: src/view/admin/cb_frame/support-page.php:183
msgid "Update your script tags this way:"
msgstr "Mettez à jour vos balises de script de cette façon :"

#. translators: %1$s refers to the original script tag HTML, and %2$s refers to its replacement
#: src/view/admin/cb_frame/support-page.php:188
msgid "%1$s to %2$s"
msgstr "%1$s à %2$s"

#: src/view/admin/common/dashboard-page.php:31
msgid "I already have an account"
msgstr "J’ai déjà un compte"

#: src/view/admin/common/dashboard-page.php:35
msgid "Connect my account"
msgstr "Connexion à mon compte"

#: src/view/admin/common/dashboard-page.php:48
#: src/view/admin/common/network-settings-page.php:33
#: src/view/admin/common/settings-page.php:37
msgid "New to our solutions? Create your account. "
msgstr "Créez votre compte"

#: src/view/admin/common/dashboard-page.php:65
msgid "How to get started"
msgstr "Comment se lancer"

#: src/view/admin/common/dashboard-page.php:69
msgid "Learn more about your CMP"
msgstr "En savoir plus sur votre CMP"

#: src/view/admin/common/network-settings-page.php:35
#: src/view/admin/common/settings-page.php:39
msgid ""
"If you’re new to our solutions, create an account first to obtain your "
"settings ID."
msgstr "Créez d’abord un compte pour obtenir votre identifiant (ID) de compte."

#: src/view/admin/common/network-settings-page.php:40
#: src/view/admin/common/settings-page.php:44
msgid "Create your account"
msgstr "Créer mon compte"

#: src/view/admin/common/network-settings-page.php:49
#: src/view/admin/common/settings-page.php:52
#: src/view/admin/uc_frame/network-settings-page.php:51
msgid "Connect your account"
msgstr "Connexion à mon compte"

#: src/view/admin/common/network-settings-page.php:52
#: src/view/admin/common/settings-page.php:55
msgid "Enter the ID of your account to quickly connect it with the plugin."
msgstr "Entrez l'ID de votre compte pour le connecter rapidement au plugin."

#: src/view/admin/uc_frame/network-settings-page.php:54
msgid "Enter your settings ID to quickly link your account with the plugin."
msgstr "Saisissez votre ID de compte pour lier rapidement votre compte au plugin."

#: src/view/admin/common/network-settings-page.php:55
#: src/view/admin/uc_frame/network-settings-page.php:57
msgid ""
"If added this will be the default account for all subsites. Subsites are "
"able to override this and use their own account."
msgstr ""
"Si ajouté, ce compte deviendra le compte par défaut pour tous les sous-"
"sites. Cependant, chaque sous-site pourra le remplacer et utiliser son "
"propre compte."

#: src/view/admin/uc_frame/network-settings-page.php:61
#: src/view/admin/uc_frame/settings/general-page.php:29
msgid "Where to find settings ID"
msgstr "Où trouver mon ID de compte"

#: src/view/admin/common/network-settings-page.php:63
#: src/view/admin/common/settings-page.php:63
msgid "How to find your Usercentrics Settings ID"
msgstr "Comment trouver votre ID de paramètres Usercentrics"

#: src/view/admin/common/network-settings-page.php:70
#: src/view/admin/common/settings-page.php:70
msgid "How to find your Cookiebot CMP Domain Group ID"
msgstr "Comment trouver votre ID de groupe de domaine CMP Cookiebot"

#: src/view/admin/common/network-settings-page.php:65
#: src/view/admin/common/settings-page.php:65
msgid "Settings ID or Domain Group ID"
msgstr "ID de paramètres ou ID de groupe de domaine"

#: src/view/admin/uc_frame/network-settings-page.php:67
#: src/view/admin/uc_frame/settings/general-page.php:35
msgid "Your settings ID"
msgstr "Votre ID de compte"

#: src/view/admin/common/network-settings-page.php:82
#: src/view/admin/common/settings-page.php:82
#: src/view/admin/uc_frame/network-settings-page.php:83
#: src/view/admin/uc_frame/settings/general-page.php:70
msgid ""
"Let us know if your account is set for compliance with a single privacy law "
"(e.g. GDPR) or multiple laws (e.g. GDPR and CCPA) based on user’s location. "
"The default is a single privacy law, so this is likely your setting unless "
"modified."
msgstr ""
"Indiquez-nous si votre compte est configuré pour respecter une seule "
"législation sur la protection des données (par ex. RGPD) ou plusieurs lois "
"(par ex. RGPD et CCPA) en fonction de la localisation de l’utilisateur. Par "
"défaut, une seule législation est activée, sauf si ce paramètre a été "
"modifié."

#: src/view/admin/common/network-settings-page.php:88
#: src/view/admin/common/settings-page.php:88
#: src/view/admin/uc_frame/network-settings-page.php:89
#: src/view/admin/uc_frame/settings/general-page.php:76
msgid "Your current account setup:"
msgstr "Configuration actuelle de votre compte :"

#: src/view/admin/common/network-settings-page.php:95
#: src/view/admin/common/settings-page.php:95
#: src/view/admin/uc_frame/network-settings-page.php:96
#: src/view/admin/uc_frame/settings/general-page.php:83
msgid "Compliance with one privacy law"
msgstr "Conformité avec une seule législation"

#: src/view/admin/common/network-settings-page.php:102
#: src/view/admin/common/settings-page.php:102
#: src/view/admin/uc_frame/network-settings-page.php:103
#: src/view/admin/uc_frame/settings/general-page.php:90
msgid "Compliance with multiple privacy laws (geolocation)"
msgstr "Conformité avec plusieurs législations (géolocalisation)"

#: src/view/admin/common/prior-consent/available-addons/tab.php:25
msgid "Plugin enabled"
msgstr "Plugin activé"

#: src/view/admin/common/prior-consent/available-addons/tab.php:58
msgid "Placeholder text:"
msgstr "Texte de paramètre fictif :"

#: src/view/admin/common/prior-consent/available-addons/tab.php:67
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:53
msgid "Display a placeholder"
msgstr "Afficher un paramètre fictif"

#: src/view/admin/common/prior-consent/available-addons/tab.php:78
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:71
msgid "+ Add language"
msgstr "+ Ajouter une langue"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:7
msgid "Jetpack settings"
msgstr "Jetpack Paramètres"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:8
msgid "Enable Jetpack on \"Available Addons\" to see this page options."
msgstr ""
"Activez Jetpack dans « Modules complémentaires disponibles » pour voir les "
"options de cette page."

#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:15
msgid "Enable"
msgstr "Activer"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:11
msgid "Show advanced options"
msgstr "Afficher les options avancées"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:17
msgid "This is for more advanced users."
msgstr "Ceci est pour les utilisateurs plus avancés."

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:23
msgid "Regex:"
msgstr "Regex:"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:35
msgid "Edit regex"
msgstr "Éditer le regex"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:49
msgid "Reset to default regex"
msgstr "Réinitialiser à la regex par défaut"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:4
msgid "Information"
msgstr "Informations"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:5
msgid ""
"These add-ons are created by a dedicated open-source community to make it "
"easier for you to manage cookie and tracker consent on your WordPress site. "
"They’re designed to help you ensure ‘prior consent’ even for plugins that "
"don’t include this feature."
msgstr ""
"Ces modules complémentaires, développés par une communauté open-source "
"dédiée, simplifient la gestion des cookies et traceurs sur votre site "
"WordPress. Ils garantissent le consentement préalable, même pour les plugins "
"sans cette fonctionnalité."

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:6
msgid ""
"Right now, these add-ons are the best way for you to signal user consent to "
"other plugins. While we don’t know if or when WordPress Core will add this "
"functionality, these tools are here to support you and work seamlessly with "
"Usercentrics solution."
msgstr ""
"Ces modules sont actuellement la meilleure solution pour transmettre le "
"consentement des utilisateurs aux autres plugins. Bien que l’ajout de cette "
"fonctionnalité dans WordPress Core demeure incertaine, ces outils "
"s’intègrent parfaitement à la solution Usercentrics."

#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:10
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:19
#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:26
msgid "Language"
msgstr "Langue"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:4
msgid "Unavailable plugins"
msgstr "Plugins non disponibles"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:5
msgid ""
"The following addons are unavailable. This is because the corresponding "
"plugin is not installed or activated."
msgstr ""
"Les modules complémentaires suivants sont indisponibles. Cela est dû au fait "
"que le plugin correspondant n’est pas installé ou activé."

#: src/view/admin/common/support-page.php:23
#: src/view/admin/uc_frame/support-page.php:23
msgid "Need help?"
msgstr "Besoin d’aide ?"

#: src/view/admin/common/support-page.php:26
#: src/view/admin/uc_frame/support-page.php:26
msgid ""
"Visit our Support Center to find answers to your questions or get help with "
"configuration. If you need further assistance, use the Contact Support "
"button in the top navigation to create a support request. We’ll respond as "
"soon as possible."
msgstr ""
"Visitez notre Centre d’assistance pour trouver des réponses ou configurer "
"votre plugin. Pour une aide personnalisée, utilisez le bouton “Contacter le "
"support” dans la navigation. Nous répondrons dès que possible."

#: src/view/admin/common/support-page.php:34
#: src/view/admin/uc_frame/support-page.php:34
msgid "Go to Support Center"
msgstr "Accéder au Centre d’assistance"

#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:6
msgid "You will need to add a new ID before updating other settings"
msgstr ""
"Vous devrez ajouter un nouvel identifiant avant de mettre à jour les autres "
"paramètres."

#: src/view/admin/common/templates/extra/review-form.php:10
msgid "Cookiebot CMP Deactivation"
msgstr "Désactivation de Cookiebot CMP"

#: src/view/admin/common/templates/extra/review-form.php:14
msgid "We are sad to lose you. Take a moment to help us improve?"
msgstr ""
"Nous sommes désolés de vous voir partir. Comment pouvons-nous nous "
"améliorer ?"

#: src/view/admin/common/templates/extra/review-form.php:19
msgid "The installation is too complicated"
msgstr "L’installation est trop compliquée"

#: src/view/admin/common/templates/extra/review-form.php:26
msgid "I found a plugin that better serves my needs"
msgstr "J’ai trouvé un plugin plus adapté à mes besoins"

#: src/view/admin/common/templates/extra/review-form.php:33
msgid "Missing features / did not meet my expectations"
msgstr "Fonctionnalités manquantes / Ne répond pas à mes attentes"

#: src/view/admin/common/templates/extra/review-form.php:40
msgid "I need more customization options"
msgstr "Je souhaite plus d’options de personnalisation"

#: src/view/admin/common/templates/extra/review-form.php:47
msgid "The premium plan is too expensive"
msgstr "Le plan Premium est trop cher"

#: src/view/admin/common/templates/extra/review-form.php:54
msgid "I’m only deactivating the plugin temporarily"
msgstr "Je désactive le plugin temporairement"

#: src/view/admin/common/templates/extra/review-form.php:61
msgid "Other"
msgstr "Autre"

#: src/view/admin/common/templates/extra/review-form.php:65
msgid "Please specify here"
msgstr "Précisez ici"

#: src/lib/Cookiebot_Review.php:105
msgid "(Optional)"
msgstr "(Optionnel)"

#: src/lib/Cookiebot_Review.php:106
msgid " By checking this box, you agree to submit troubleshooting information and allow us to contact you regarding the problem if necessary."
msgstr " En cochant cette case, vous acceptez de nous transmettre les informations nécessaires à la résolution du problème et autorisez notre équipe à vous contacter si nécessaire."

#: src/lib/Cookiebot_Review.php:110
msgid "The information will be kept for no longer than 90 days. You may revoke this consent at any time, e.g. by sending an email to "
msgstr "Les données seront conservées 90 jours maximum. Vous pouvez révoquer ce consentement à tout moment, par exemple, en envoyant un e-mail à l’adresse "

#: src/lib/Cookiebot_Review.php:117
msgid "Skip and Deactivate"
msgstr "Sauter et désactiver"

#: src/lib/Cookiebot_Review.php:118
msgid "Submit and Deactivate"
msgstr "Envoyer et désactiver"

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:6
msgid ""
"If there is a network settings ID connected it will be used for this subsite, "
"if not you will need to add a new ID before updating other settings"
msgstr ""
"Si un identifiant de compte réseau est déjà associé, il sera utilisé pour ce "
"sous-site. Sinon, vous devrez ajouter un nouvel identifiant avant de "
"modifier les autres paramètres."

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:13
msgid "Disconnect subsite account"
msgstr "Déconnecter le compte du sous-site"

#: src/view/admin/common/templates/header.php:10
msgid "Changes has been saved"
msgstr "Les modifications ont été enregistrées"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:13
msgid "Absolutely, you deserve it!"
msgstr "Avec plaisir !"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:24
msgid "Maybe later?"
msgstr "Peut-être plus tard."

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:35
msgid "I`ve already left my feedback"
msgstr "C'est déjà fait."

#: src/view/admin/uc_frame/dashboard-page.php:30
msgid "Welcome to Usercentrics Cookiebot WordPress Plugin"
msgstr "Bienvenue dans le plugin Usercentrics Cookiebot pour WordPress !"

#: src/view/admin/uc_frame/dashboard-page.php:38
msgid ""
"You’ve added your settings ID to your Usercentrics Cookiebot WordPress Plugin."
msgstr "Vous avez ajouté votre ID de compte au plugin."

#: src/view/admin/uc_frame/dashboard-page.php:51
msgid ""
"Are you happy with Usercentrics Cookiebot WordPress Plugin? Your feedback "
"helps us improve it."
msgstr ""
"Votre avis compte ! Partagez vos impressions sur le plugin Usercentrics "
"Cookiebot WordPress pour nous aider à nous améliorer."

#: src/view/admin/uc_frame/dashboard-page.php:55
msgid "Share feedback"
msgstr "Donner mon avis"

#: src/view/admin/uc_frame/dashboard-page.php:68
msgid "How to set up Usercentrics Cookiebot WordPress Plugin"
msgstr "Comment configurer le plugin Usercentrics Cookiebot pour WordPress"

#: src/view/admin/uc_frame/dashboard-page.php:72
#: src/view/admin/uc_frame/settings/gcm-page.php:21
#: src/view/admin/uc_frame/settings/general-page.php:127
#: src/view/admin/uc_frame/settings/gtm-page.php:18
msgid "Learn more"
msgstr "En savoir plus"

#: src/view/admin/uc_frame/dashboard-page.php:115
msgid "Visit our blog to learn about other legislations"
msgstr "Visitez notre blog pour découvrir d’autres législations."

#: src/view/admin/uc_frame/debug-page.php:22
msgid "Debug your plugin"
msgstr "Résoudre les bugs du plugin"

#: src/view/admin/uc_frame/debug-page.php:25
msgid ""
"If you encounter any issues with your Usercentrics Cookiebot WordPress "
"Plugin, provide the information below to help us assist you. Visit our "
"Support Center and send us a copy of what is displayed in the window below."
msgstr ""
"En cas de problème avec votre plugin, fournissez les informations ci-dessous "
"pour que nous puissions vous aider. Rendez-vous dans notre Centre "
"d’assistance et envoyez une copie des données affichées."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:23
msgid "WP Consent API Settings"
msgstr "Paramètres de l’API de consentement WP"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:32
msgid ""
"WP Consent API and Usercentrics Cookiebot WordPress Plugin categorize "
"cookies a bit differently. The default settings should fit most needs, but "
"if you need to change the mapping you can do so below."
msgstr ""
"L’API de consentement WP et le plugin Usercentrics Cookiebot catégorisent "
"les cookies différemment. Les paramètres par défaut devraient répondre à la "
"majorité des besoins, mais vous pouvez ajuster le mappage ci-dessous si "
"nécessaire."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:48
msgid "Usercentrics Cookiebot cookie categories"
msgstr "Catégories de cookies Usercentrics Cookiebot"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:58
msgid "essential"
msgstr "Essentiels"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:72
msgid "functional"
msgstr "Fonctionnel"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:106
msgid "Reset to default categories"
msgstr "Réinitialiser les catégories par défaut"

#: src/view/admin/uc_frame/settings-page.php:48
msgid "Save changes"
msgstr "Enregistrer les modifications "

#: src/view/admin/uc_frame/settings-page.php:70
#: src/view/admin/uc_frame/settings/embeddings-page.php:4
msgid "Privacy Policy Sync"
msgstr "Synchronisation avec la politique de confidentialité "

#: src/view/admin/uc_frame/settings/embeddings-page.php:7
msgid ""
"Use our pre-defined, automatically generated embeddings to help you keep "
"your Privacy Policy page in sync with your consent banner settings. This "
"feature saves you time by automatically updating legally required "
"information, so you don’t need to manually copy data into your Privacy "
"Policy page. Once you’re done setting the options below, simply copy the "
"code and paste it into your Privacy Policy page."
msgstr ""
"Utilisez nos blocs prédéfinis pour synchroniser automatiquement votre page "
"de politique de confidentialité avec les paramètres de votre bandeau. Ainsi, "
"vous gagnez du temps en actualisant automatiquement les informations légales "
"requises. Une fois les options configurées, copiez simplement le code et "
"collez-le dans votre page de politique de confidentialité."

#: src/view/admin/uc_frame/settings/embeddings-page.php:12
msgid "Copy shortcode"
msgstr "Copier le code court (shortcode)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:23
msgid "Sync options for privacy legislations"
msgstr ""
"Options de synchronisation pour les législations sur la confidentialité"

#: src/view/admin/uc_frame/settings/embeddings-page.php:26
msgid ""
"Select the legislation you want to automatically sync with your Privacy "
"Policy page."
msgstr ""
"Sélectionnez la législation que vous souhaitez synchroniser automatiquement "
"avec votre page de Politique de confidentialité."

#: src/view/admin/uc_frame/settings/embeddings-page.php:41
msgid "Sync options for data processing services (DPS) "
msgstr ""
"Options de synchronisation pour les services de traitement des données (DPS)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:44
msgid ""
"Define what to include on your Privacy Policy page: DPS categories only, "
"categories with their services, a single service, or detailed information on "
"both categories and services. Choose based on the level of detail you want "
"to display."
msgstr ""
"Définissez ce que vous souhaitez inclure dans votre page Politique de "
"confidentialité : uniquement les catégories DPS, les catégories avec leurs "
"services, un seul service, ou des informations détaillées sur les catégories "
"et les services. Choisissez en fonction du niveau de détail que vous "
"souhaitez afficher."

#: src/view/admin/uc_frame/settings/embeddings-page.php:50
msgid "Services (Default)"
msgstr "Services (par défaut)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:51
msgid "Categories and services"
msgstr "Catégories et services"

#: src/view/admin/uc_frame/settings/embeddings-page.php:52
msgid "Categories only"
msgstr "Catégories uniquement"

#: src/view/admin/uc_frame/settings/embeddings-page.php:53
msgid "Single service"
msgstr "Service unique"

#: src/view/admin/uc_frame/settings/embeddings-page.php:54
msgid "Purposes"
msgstr "Finalités"

#: src/view/admin/uc_frame/settings/embeddings-page.php:55
msgid "Vendors"
msgstr "Fournisseurs"

#: src/view/admin/uc_frame/settings/embeddings-page.php:63
msgid "Single Service ID"
msgstr "ID de service unique"

#: src/view/admin/uc_frame/settings/embeddings-page.php:66
msgid "Add the service ID that you want to display."
msgstr "Ajoutez l’ID du service que vous souhaitez afficher."

#: src/view/admin/uc_frame/settings/embeddings-page.php:69
msgid "This feature is required."
msgstr "Cette fonctionnalité est requise."

#: src/view/admin/uc_frame/settings/embeddings-page.php:81
msgid "Privacy toggles"
msgstr "Options de confidentialité "

#: src/view/admin/uc_frame/settings/embeddings-page.php:84
msgid ""
"Define whether you want the privacy toggles to be enabled and displayed on "
"your Privacy Policy page."
msgstr ""
"Définissez si vous souhaitez que les options de confidentialité soient "
"activées et affichées sur votre page Politique de confidentialité."

#: src/view/admin/uc_frame/settings/embeddings-page.php:92
msgid "Enable privacy toggles"
msgstr "Activer les options de confidentialité"

#: src/view/admin/uc_frame/settings/gcm-page.php:14
msgid "Integration with Google Consent Mode"
msgstr "Intégration avec le mode Consentement de Google"

#: src/view/admin/uc_frame/settings/gcm-page.php:17
msgid ""
"The Usercentrics Cookiebot WordPress Plugin and Google Consent Mode "
"integrate seamlessly, providing plug-and-play privacy compliance and "
"effortless use of all Google services in one solution."
msgstr ""
"Le plugin Usercentrics Cookiebot pour WordPress et le mode Consentement de "
"Google s’intègrent parfaitement, offrant une conformité à la vie privée "
"simplifiée et une utilisation sans effort de tous les services Google dans "
"une seule solution."

#: src/view/admin/uc_frame/settings/gcm-page.php:31
msgid ""
"Enable Google Consent Mode integration within your Usercentrics Cookiebot "
"WordPress Plugin."
msgstr ""
"Activez l’intégration du mode Consentement de Google dans votre plugin "
"Usercentrics Cookiebot pour WordPress."

#: src/view/admin/uc_frame/settings/general-page.php:22
msgid "Settings ID"
msgstr "ID du compte"

#: src/view/admin/uc_frame/settings/general-page.php:25
msgid ""
"To disconnect your account, enter your settings ID into the field and confirm "
"with the button."
msgstr ""
"Pour vous déconnecter, saisissez votre identifiant de compte dans le champ "
"prévu à cet effet et confirmez en cliquant sur le bouton."

#: src/view/admin/uc_frame/settings/general-page.php:99
msgid "TCF integration"
msgstr "Intégration TCF"

#: src/view/admin/uc_frame/settings/general-page.php:102
msgid "Enable the integration with the latest version of IAB TCF."
msgstr "Activez l’intégration avec la dernière version de l’IAB TCF."

#: src/view/admin/uc_frame/settings/general-page.php:111
msgid "IAB TCF integration"
msgstr "Intégration IAB TCF"

#: src/view/admin/uc_frame/settings/gtm-page.php:14
msgid ""
"Enable Google Tag Manager integration to streamline tracking tags with your "
"Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Activez l’intégration de Google Tag Manager pour simplifier la gestion des "
"balises de suivi avec votre plugin Usercentrics Cookiebot pour WordPress."

#: src/view/admin/uc_frame/settings/gtm-page.php:37
msgid "Enter your Google Tag Manager ID for seamless integration."
msgstr ""
"Saisissez votre identifiant Google Tag Manager pour une intégration fluide."

#: src/view/admin/uc_frame/settings/gtm-page.php:48
msgid "GTM-XXXXXXX"
msgstr "GTM-XXXXXXX"

#: src/view/admin/uc_frame/settings/gtm-page.php:56
msgid "Data layer name (only if changed)"
msgstr "Nom de la couche de données (uniquement si modifiée)."

#: src/view/admin/uc_frame/settings/gtm-page.php:58
msgid ""
"The default name for the data layer in Google Tag Manager is ‘dataLayer’. If "
"you renamed it, enter the new name. Otherwise, leave this field empty."
msgstr ""
"Le nom par défaut de la couche de données dans Google Tag Manager est "
"« dataLayer ». Si vous l’avez renommée, saisissez le nouveau nom. Sinon, "
"laissez ce champ vide."

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:14
msgid "Title"
msgstr "Titre"

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:33
msgid "- Default -"
msgstr "- Défaut -"

#: src/widgets/Cookiebot_Declaration_Widget.php:17
msgid "Cookiebot - Cookie Declaration"
msgstr "Cookiebot - Déclaration de cookies"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:23
msgid "Cookiebot Status"
msgstr "Statut du Cookiebot"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:39
msgid "You need to enter your Cookiebot ID."
msgstr "Vous devez saisir votre identifiant Cookiebot."

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:41
msgid "Update your Cookiebot ID"
msgstr "Mettre à jour votre identifiant Cookiebot"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:44
msgid "Your Cookiebot is working!"
msgstr "Votre Cookiebot fonctionne !"

#~ msgid ""
#~ "We hope you enjoy using WordPress Cookiebot! Would you consider leaving "
#~ "us a review on WordPress.org?"
#~ msgstr ""
#~ "Nous espérons que vous prenez plaisir à utiliser WordPress Cookiebot ! "
#~ "Seriez-vous disposé à nous laisser un avis sur WordPress.org ?"

#~ msgid "Legislations"
#~ msgstr "Législations"

#~ msgid "Sure! I'd love to!"
#~ msgstr "Bien sûr, j’en serais ravi !"

#~ msgid "I've already left a review"
#~ msgstr "J’ai déjà laissé un avis"

#~ msgid "Maybe Later"
#~ msgstr "Peut-être plus tard"

#~ msgid "Never show again"
#~ msgstr "Ne plus jamais afficher"

#~ msgid "TCF version:"
#~ msgstr "Version TCF"

#~ msgid ""
#~ "In May 2023 The Interactive Advertising Bureau (IAB) announced the latest "
#~ "version of its Transparency and Consent Framework (TCF), or TCF v2.2, "
#~ "which must be implemented by all consent management platforms (CMPs) by "
#~ "November 20, 2023. We will migrate you automatically on November 20,2023, "
#~ "but we recommend to do it manually before. To manually switch the version "
#~ "before please select it on the right."
#~ msgstr ""
#~ "En mai 2023, l'Interactive Advertising Bureau (IAB) a annoncé la dernière "
#~ "version de son Cadre de transparence et de consentement (TCF), ou TCF "
#~ "v2.2, qui doit être mis en œuvre par toutes les plateformes de gestion du "
#~ "consentement (CMP) d'ici le 20 novembre 2023. Nous procéderons à une "
#~ "migration automatique le 20 novembre 2023, mais nous vous recommandons de "
#~ "le faire manuellement avant. Pour changer manuellement la version avant, "
#~ "veuillez la sélectionner à droite."

#~ msgid "Select the TCF Version below"
#~ msgstr "Sélectionnez la version du TCF ci-dessous"

#~ msgid "New"
#~ msgstr "Nouveau"

#~ msgid "Create a new Account"
#~ msgstr "Créer un compte"

#~ msgid "Get help with connecting your account"
#~ msgstr "Obtenir de l’aide pour connecter votre compte"

#~ msgid "Select the Cookie-blocking mode"
#~ msgstr "Sélectionnez le mode de blocage des cookies"

#~ msgid "Automatic cookie-blocking mode"
#~ msgstr "Mode de blocage automatique des cookies"

#~ msgid "Manual cookie-blocking mode"
#~ msgstr "Mode de blocage manuel des cookies"

#~ msgid "Depending on Cookie-blocking mode"
#~ msgstr "En fonction du mode de blocage des cookies"

#~ msgid "Auto-update Cookiebot™ Plugin:"
#~ msgstr "Mise à jour automatique du plugin Cookiebot™ :"

#~ msgid ""
#~ "Automatically update your Cookiebot™ plugin when new releases becomes "
#~ "available."
#~ msgstr ""
#~ "Mettez automatiquement à jour votre plugin Cookiebot™ lorsque de "
#~ "nouvelles versions sont disponibles."

#~ msgid ""
#~ "These add-ons are produced by an open-source community of developers. "
#~ "This is done to help make it easier for WordPress users to implement "
#~ "‘prior consent’ for cookies and trackers set by plugins that do not offer "
#~ "this as a built-in function. The add-ons are currently the best "
#~ "alternative to a WordPress Core framework that can signal the user’s "
#~ "consent state to other plugins (if and when this will be implemented is "
#~ "unknown) and to those plugins that do not yet offer native support for "
#~ "Cookiebot CMP. "
#~ msgstr ""
#~ "Ces modules complémentaires sont produits par une communauté de "
#~ "développeurs open-source. Ils ont pour but de faciliter la mise en place, "
#~ "par les utilisateurs de WordPress, d’un  « consentement préalable »  pour "
#~ "les cookies et les traceurs définis par des plugins qui ne proposent pas "
#~ "cette fonction intégrée. Les modules complémentaires sont actuellement la "
#~ "meilleure alternative à un cadre de référence WordPress qui peut signaler "
#~ "l’état du consentement de l’utilisateur à d’autres plugins (on ne sait "
#~ "pas si et quand cela sera mis en œuvre) et aux plugins qui n’offrent pas "
#~ "encore de support natif pour Cookiebot CMP."

#~ msgid "Do you not have an account yet?"
#~ msgstr "Vous ne disposez pas encore de compte ?"

#~ msgid ""
#~ "Before you can get started with Cookiebot CMP for WordPress, you need to "
#~ "create an account on our website by clicking on \"Create a new account\" "
#~ "below. After you have signed up, you can configure your banner in the "
#~ "Cookiebot Manager and then place the Cookiebot Domain Group ID in the "
#~ "designated field below. You can find your ID in the Cookiebot Manager by "
#~ "navigating to \"Settings\" and \"Your Scripts\"."
#~ msgstr ""
#~ "Avant de pouvoir démarrer avec Cookiebot CMP pour Wordpress, vous devez "
#~ "créer un compte sur notre site web en cliquant sur “Créer un compte” ci-"
#~ "dessous. Après vous être inscrit, vous pouvez configurer votre bandeau "
#~ "dans le gestionnaire Cookiebot, puis placer l’identifiant de groupe de "
#~ "domaine Cookiebot dans le champ réservé ci-dessous. Vous trouverez votre "
#~ "identifiant dans le gestionnaire Cookiebot en naviguant vers “Paramètres” "
#~ "et “Vos scripts”."

#~ msgid "Depending on cookie-blocking mode"
#~ msgstr "En fonction du mode de blocage des cookies"

#~ msgid "Cookiebot CMP in WP Admin:"
#~ msgstr "Cookiebot CMP dans WP Admin :"

#~ msgid ""
#~ "This checkbox will disable Cookiebot CMP to act within the WordPress "
#~ "Admin area"
#~ msgstr ""
#~ "Cette case à cocher empêchera Cookiebot CMP de fonctionner dans la zone "
#~ "d’administration de WordPress."

#~ msgid "Disable Cookiebot CMP in the WordPress Admin area"
#~ msgstr "Désactiver Cookiebot CMP dans la zone d’administration de WordPress"

#~ msgid "Cookiebot CMP on front-end while logged in:"
#~ msgstr "Cookiebot CMP sur le front-end lorsque vous êtes connecté :"

#~ msgid ""
#~ "This setting will enable Cookiebot CMP on the front-end while you're "
#~ "logged in."
#~ msgstr ""
#~ "Ce paramètre permet d’activer Cookiebot CMP sur le front-end lorsque vous "
#~ "êtes connecté."

#~ msgid "Render Cookiebot CMP on front-end while logged in"
#~ msgstr "Activer Cookiebot CMP sur le front-end pendant la connexion"

#~ msgid "Watch video demonstration"
#~ msgstr "Regardez la vidéo de démonstration"
