msgid ""
msgstr ""
"Project-Id-Version: Cookiebot | GDPR/CCPA Compliant <PERSON><PERSON> and "
"Control\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/cookiebot\n"
"POT-Creation-Date: 2024-12-18T18:13:49+00:00\n"
"PO-Revision-Date: 2025-01-13 11:26+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: de_DE\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: cookiebot.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"

#. Plugin Name of the plugin
#: cookiebot.php
msgid "Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: cookiebot.php
msgid "https://www.cookiebot.com/"
msgstr ""

#. Description of the plugin
#: cookiebot.php
msgid ""
"The Cookiebot CMP WordPress cookie banner and cookie policy help you comply "
"with the major data protection laws (GDPR, ePrivacy, CCPA, LGPD, etc.) in a "
"simple and fully automated way. Secure your website and get peace of mind."
msgstr ""

#. Author of the plugin
#: cookiebot.php
msgid "Usercentrics A/S"
msgstr ""

#: src/addons/config/Settings_Config.php:89
#: src/addons/config/Settings_Config.php:90
#: src/view/admin/common/prior-consent/page.php:24
#: src/view/admin/common/templates/main-tabs.php:39
msgid "Plugins"
msgstr "Plug-ins"

#: src/addons/config/Settings_Config.php:130
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:25
msgid "Remove language"
msgstr "Sprache entfernen"

#: src/addons/config/Settings_Config.php:602
msgid "The plugin is not installed."
msgstr "Das Plug-in ist nicht installiert."

#: src/addons/config/Settings_Config.php:605
msgid "The theme is not installed."
msgstr "Das Theme ist nicht installiert."

#: src/addons/config/Settings_Config.php:609
msgid "The plugin is not activated."
msgstr "Das Plug-in ist nicht aktiviert."

#: src/addons/config/Settings_Config.php:612
msgid "The theme is not activated."
msgstr "Das Theme ist nicht aktiviert."

#: src/addons/config/Settings_Config.php:672
#: src/lib/Cookiebot_Admin_Links.php:54 src/lib/Cookiebot_Admin_Links.php:56
#: src/lib/Cookiebot_Admin_Links.php:124
msgid "%s"
msgstr "%s"

#: src/addons/config/Settings_Config.php:708
#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:18
#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:19
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:40
msgid "Info"
msgstr "Information"

#: src/addons/config/Settings_Config.php:715
msgid "Available Add-ons"
msgstr "Verfügbare Add-ons"

#: src/addons/config/Settings_Config.php:721
msgid "Unavailable Add-ons"
msgstr "Nicht verfügbare Add-ons"

#: src/addons/config/Settings_Config.php:738
msgid "Jetpack"
msgstr "Jetpack"

#: src/addons/config/Settings_Config.php:749
msgid "WP Consent API"
msgstr "WP Consent API "

#: src/addons/controller/addons/add_to_any/Add_To_Any.php:86
#: src/addons/controller/addons/embed_autocorrect/Embed_Autocorrect.php:443
msgid "Blocks embedded videos from Youtube, Twitter, Vimeo and Facebook."
msgstr "Blockiert integrierte Videos von Youtube, Twitter, Vimeo und Facebook."

#: src/addons/controller/addons/enfold/Enfold.php:29
msgid "Blocks cookies created by Enfold theme's Google Services."
msgstr ""
"Blockiert Cookies, die von Google Diensten des Enfold Themes erstellt werden."

#: src/addons/controller/addons/enhanced_ecommerce_for_woocommerce_store/Enhanced_Ecommerce_For_WooCommerce_Store.php:43
msgid "Blocks enhanced e-commerce for WooCommerce store"
msgstr "Blockiert erweitertes E-Commerce für WooCommerce-Shop"

#: src/addons/controller/addons/google_analytics/Google_Analytics.php:36
msgid ""
"Google Analytics is used to track how visitor interact with website content."
msgstr ""
"Google Analytics wird verwendet, um zu tracken, wie Besucher mit den "
"Inhalten der Website interagieren."

#: src/addons/controller/addons/google_analytics_plus/Google_Analytics_Plus.php:32
msgid ""
"Google Analytics is a simple, easy-to-use tool that helps website owners "
"measure how users interact with website content"
msgstr ""
"Google Analytics ist ein einfaches, benutzerfreundliches Tool, mit dem "
"Website-Besitzer messen können, wie Nutzer mit Inhalten auf der Website "
"interagieren"

#: src/addons/controller/addons/google_site_kit/Google_Site_Kit.php:33
msgid "Blocks Google Analytics scripts"
msgstr "Blockiert Google Analytics Skripte"

#: src/addons/controller/addons/jetpack/widget/Facebook_Jetpack_Widget.php:71
msgid "Facebook widget."
msgstr "Facebook Widget"

#: src/addons/controller/addons/litespeed_cache/Litespeed_Cache.php:51
msgid ""
"Excludes cookiebot javascript files when the Litespeed Cache deter option is "
"enabled."
msgstr ""
"Schließt Cookiebot-Javascript-Dateien aus, wenn die Option „Litespeed Cache "
"deter” aktiviert ist."

#: src/addons/controller/addons/official_facebook_pixel/Official_Facebook_Pixel.php:247
msgid "Blocks Official Meta Pixel scripts"
msgstr "Blockiert offizielle Meta Pixel Skripts"

#: src/addons/controller/addons/optinmonster/Optinmonster.php:32
msgid ""
"OptinMonster API plugin to connect your WordPress site to your OptinMonster "
"account."
msgstr ""
"OptinMonster API-Plug-in verbindet Ihre WordPress-Seite mit Ihrem "
"OptinMonster-Konto."

#: src/addons/controller/addons/simple_share_buttons_adder/Simple_Share_Buttons_Adder.php:32
msgid "Blocks Simple Share Buttons Adder."
msgstr "Blockiert Simple Share Buttons Adder."

#: src/addons/controller/addons/wpforms/Wpforms.php:61
msgid ""
"If the user gives correct consent, IP and Unique User ID will be saved on "
"form submissions, otherwise not."
msgstr ""
"Wenn der Nutzer eine korrekte Einwilligung abgibt, werden IP und Unique User "
"ID bei der Übermittlung von Formularen gespeichert, andernfalls nicht."

#: src/addons/controller/addons/wpforms/Wpforms.php:62
msgid "Increases opt-in rate compared to WPForms \"GDPR mode\"."
msgstr "Erhöht die Opt-in-Rate im Vergleich zum  „DSGVO-Modus” von WPForms."

#: src/addons/controller/addons/wp_google_analytics_events/Wp_Google_Analytics_Events.php:46
msgid ""
"The plugin allows you to fire events whenever someone interacts or views "
"elements on your website."
msgstr ""
"Das Plug-in ermöglicht es Ihnen, Events auszulösen, wenn jemand mit "
"Elementen auf Ihrer Website interagiert oder diese betrachtet."

#: src/addons/controller/addons/wp_rocket/Wp_Rocket.php:48
msgid ""
"Excludes cookiebot javascript files when the WP-Rocket deter option is "
"enabled."
msgstr ""
"Schließt Cookiebot-Javascript-Dateien aus, wenn die Option „WP-Rocket deter“ "
"aktiviert ist."

#: src/addons/controller/addons/wp_seopress/Wp_Seopress.php:41
msgid "Blocks cookies from WP SEOPress' Google Analytics integration."
msgstr "Blockiert Cookies von der WP SEOPress’ Google Analytics-Integration."

#: src/addons/controller/Plugin_Controller.php:50
msgid "You enabled Cookiebot™ auto blocking mode but still using addons"
msgstr ""
"Sie haben den automatischen Blockierungsmodus von Cookiebot aktiviert, "
"verwenden aber immer noch Add-Ons"

#: src/addons/controller/Plugin_Controller.php:55
msgid ""
"In some occasions this may cause client side errors. If you notice any "
"errors please try to disable Cookiebot™ addons or contact Cookiebot™ support."
msgstr ""
"In einigen Fällen kann dies zu clientseitigen Fehlern führen. Wenn Sie "
"Fehler bemerken, versuchen Sie bitte, Cookiebot-Addons zu deaktivieren, oder "
"wenden Sie sich an den Cookiebot-Support"

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:84
msgid "Share your experience"
msgstr "Wir brauchen Ihr Feedback"

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:88
msgid ""
"Hi there! We are thrilled you love the Cookiebot CMP plugin. Could you do us "
"a huge favor and leave a 5-star rating on WordPress? Your support will help "
"us spread the word and empower more WordPress websites to meet GDPR and CCPA "
"compliance standards effortlessly. Thank you for your support!"
msgstr ""
"Hallo! Wir freuen uns sehr, dass Ihnen das Cookiebot CMP-Plugin gefällt. "
"Könnten Sie uns einen großen Gefallen tun und eine 5-Sterne-Bewertung auf "
"WordPress hinterlassen? Mit Ihrer Unterstützung können wir mehr Unternehmen "
"helfen und mehr WordPress-Websites DSGVO- und CCPA-konform machen. Vielen "
"Dank für Ihre Hilfe!"

#: src/admin_notices/Cookiebot_Temp_Notice.php:43
msgid ""
"Cookiebot CMP Plugin will soon no longer support PHP 5. If your website "
"still runs on this version we recommend upgrading so you can continue "
"enjoying the features Cookiebot CMP offers."
msgstr ""
"Das Cookiebot CMP Plugin wird bald nicht mehr PHP 5 unterstützen. Wenn Ihre "
"Website noch mit dieser Version läuft, empfehlen wir Ihnen ein Upgrade, "
"damit Sie weiterhin die Funktionen von Cookiebot CMP nutzen können."

#: src/lib/Cookiebot_Review.php:82
msgid "Sorry you are not allowed to do this."
msgstr "Dazu haben Sie leider keine Befugnis. "

#: src/lib/Cookiebot_Review.php:85
#: src/view/admin/common/templates/extra/review-form.php:94
msgid "Please select one option"
msgstr "Bitte wählen Sie eine Option"

#. translators: The placeholder is for the COOKIEBOT_MIN_PHP_VERSION constant
#: src/lib/Cookiebot_WP.php:66
msgid "The Cookiebot plugin requires PHP version %s or greater."
msgstr "Das Cookiebot-Plugin erfordert die PHP-Version %s oder höher"

#: src/lib/Cookiebot_WP.php:242 src/settings/pages/Dashboard_Page.php:35
#: src/view/admin/common/templates/main-tabs.php:21
msgid "Dashboard"
msgstr "Dashboard"

#: src/lib/helper.php:245 src/lib/helper.php:343
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:91
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:167
#: src/view/admin/cb_frame/settings/gcm-page.php:126
#: src/view/admin/cb_frame/settings/gtm-page.php:113
#: src/view/admin/common/prior-consent/available-addons/tab.php:55
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:88
msgid "marketing"
msgstr "Marketing"

#: src/lib/helper.php:248 src/lib/helper.php:342
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:88
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:134
#: src/view/admin/cb_frame/settings/gcm-page.php:118
#: src/view/admin/cb_frame/settings/gtm-page.php:105
#: src/view/admin/common/prior-consent/available-addons/tab.php:46
msgid "statistics"
msgstr "statistik"

#: src/lib/helper.php:251 src/lib/helper.php:341
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:85
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:118
#: src/view/admin/cb_frame/settings/gcm-page.php:110
#: src/view/admin/cb_frame/settings/gtm-page.php:97
#: src/view/admin/common/prior-consent/available-addons/tab.php:37
msgid "preferences"
msgstr "präferenzen"

#: src/lib/helper.php:254
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:82
msgid "necessary"
msgstr "notwendig"

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:367
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:372
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"tracking."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um das Tracking zu aktivieren."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:377
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Social Share buttons."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um Social Share Buttons zu aktivieren."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:382
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to view "
"this element."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um dieses Element anzusehen."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:387
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to watch "
"this video."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um dieses Video anzusehen."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:392
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Services."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um Google-Dienste zu aktivieren."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:397
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"facebook shopping feature."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um das Facebook-Shopping-Feature zu aktivieren."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:402
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to track "
"for google analytics."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies "
"für das Tracking mit Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:407
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Analytics."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um Google Analytics zu aktivieren."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:412
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"instagram feed."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um den Instagram-Feed zu aktivieren."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:417
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Facebook Pixel."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um Facebook Pixel zu aktivieren."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:422
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to Social "
"Share buttons."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies "
"für Social Share Buttons."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:427
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to allow "
"Matomo statistics."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um Matomo Statistiken zu erlauben."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:432
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"saving user information."
msgstr ""
"Bitte akzeptieren Sie [renew_consent]%cookie_types[/renew_consent] Cookies, "
"um das Speichern von Nutzerinformationen zu aktivieren."

#: src/lib/Supported_Languages.php:8
msgid "Norwegian Bokmål"
msgstr "Norwegisch Bokmål"

#: src/lib/Supported_Languages.php:9
msgid "Turkish"
msgstr "Türkisch"

#: src/lib/Supported_Languages.php:10
msgid "German"
msgstr "Deutsch"

#: src/lib/Supported_Languages.php:11
msgid "Czech"
msgstr "Tschechisch"

#: src/lib/Supported_Languages.php:12
msgid "Danish"
msgstr "Dänisch"

#: src/lib/Supported_Languages.php:13
msgid "Albanian"
msgstr "Albanisch"

#: src/lib/Supported_Languages.php:14
msgid "Hebrew"
msgstr "Hebräisch"

#: src/lib/Supported_Languages.php:15
msgid "Korean"
msgstr "Koreanisch"

#: src/lib/Supported_Languages.php:16
msgid "Italian"
msgstr "Italienisch"

#: src/lib/Supported_Languages.php:17
msgid "Dutch"
msgstr "Niederländisch"

#: src/lib/Supported_Languages.php:18
msgid "Vietnamese"
msgstr "Vietnamesisch"

#: src/lib/Supported_Languages.php:19
msgid "Tamil"
msgstr "Tamilisch"

#: src/lib/Supported_Languages.php:20
msgid "Icelandic"
msgstr "Isländisch"

#: src/lib/Supported_Languages.php:21
msgid "Romanian"
msgstr "Rumänisch"

#: src/lib/Supported_Languages.php:22
msgid "Sinhala"
msgstr "Singhalesisch"

#: src/lib/Supported_Languages.php:23
msgid "Catalan"
msgstr "Katalanisch"

#: src/lib/Supported_Languages.php:24
msgid "Bulgarian"
msgstr "Bulgarisch"

#: src/lib/Supported_Languages.php:25
msgid "Ukrainian"
msgstr "Ukrainisch"

#: src/lib/Supported_Languages.php:26
msgid "Chinese"
msgstr "Chinesisch"

#: src/lib/Supported_Languages.php:27
msgid "English"
msgstr "Englisch"

#: src/lib/Supported_Languages.php:28
msgid "Arabic"
msgstr "Arabisch"

#: src/lib/Supported_Languages.php:29
msgid "Croatian"
msgstr "Kroatisch"

#: src/lib/Supported_Languages.php:30
msgid "Thai"
msgstr "Thailändisch"

#: src/lib/Supported_Languages.php:31
msgid "Greek"
msgstr "Griechisch"

#: src/lib/Supported_Languages.php:32
msgid "Lithuanian"
msgstr "Litauisch"

#: src/lib/Supported_Languages.php:33
msgid "Polish"
msgstr "Polnisch"

#: src/lib/Supported_Languages.php:34
msgid "Latvian"
msgstr "Lettisch"

#: src/lib/Supported_Languages.php:35
msgid "French"
msgstr "Französisch"

#: src/lib/Supported_Languages.php:36
msgid "Indonesian"
msgstr "Indonesisch"

#: src/lib/Supported_Languages.php:37
msgid "Macedonian"
msgstr "Mazedonisch"

#: src/lib/Supported_Languages.php:38
msgid "Estonian"
msgstr "Estnisch"

#: src/lib/Supported_Languages.php:39
msgid "Portuguese"
msgstr "Portugiesisch"

#: src/lib/Supported_Languages.php:40
msgid "Irish"
msgstr "Irisch"

#: src/lib/Supported_Languages.php:41
msgid "Malay"
msgstr "Malaiisch"

#: src/lib/Supported_Languages.php:42
msgid "Slovenian"
msgstr "Slowenisch"

#: src/lib/Supported_Languages.php:43
msgid "Russian"
msgstr "Russisch"

#: src/lib/Supported_Languages.php:44
msgid "Japanese"
msgstr "Japanisch"

#: src/lib/Supported_Languages.php:45
msgid "Hindi"
msgstr "Hindi"

#: src/lib/Supported_Languages.php:46
msgid "Slovak"
msgstr "Slowakisch"

#: src/lib/Supported_Languages.php:47
msgid "Spanish"
msgstr "Spanisch"

#: src/lib/Supported_Languages.php:48
msgid "Swedish"
msgstr "Schwedisch"

#: src/lib/Supported_Languages.php:49
msgid "Serbian"
msgstr "Serbisch"

#: src/lib/Supported_Languages.php:50
msgid "Finnish"
msgstr "Finnisch"

#: src/lib/Supported_Languages.php:51
msgid "Basque"
msgstr "Baskisch"

#: src/lib/Supported_Languages.php:52
msgid "Hungarian"
msgstr "Ungarisch"

#: src/lib/Supported_Regions.php:7
msgid "Afghanistan"
msgstr "Afghanistan"

#: src/lib/Supported_Regions.php:8
msgid "Albania"
msgstr "Albanien"

#: src/lib/Supported_Regions.php:9
msgid "Algeria"
msgstr "Algerien"

#: src/lib/Supported_Regions.php:10
msgid "American Samoa"
msgstr "Amerikanisch-Samoa"

#: src/lib/Supported_Regions.php:11
msgid "Andorra"
msgstr "Andorra"

#: src/lib/Supported_Regions.php:12
msgid "Angola"
msgstr "Angola"

#: src/lib/Supported_Regions.php:13
msgid "Anguilla"
msgstr "Anguilla"

#: src/lib/Supported_Regions.php:14
msgid "Antarctica"
msgstr "Antarktis"

#: src/lib/Supported_Regions.php:15
msgid "Antigua and Barbuda"
msgstr "Antigua und Barbuda"

#: src/lib/Supported_Regions.php:16
msgid "Argentina"
msgstr "Argentinien"

#: src/lib/Supported_Regions.php:17
msgid "Armenia"
msgstr "Armenien"

#: src/lib/Supported_Regions.php:18
msgid "Aruba"
msgstr "Aruba"

#: src/lib/Supported_Regions.php:19
msgid "Australia"
msgstr "Australien"

#: src/lib/Supported_Regions.php:20
msgid "Austria"
msgstr "Österreich"

#: src/lib/Supported_Regions.php:21
msgid "Azerbaijan"
msgstr "Aserbaidschan"

#: src/lib/Supported_Regions.php:22
msgid "Bahamas"
msgstr "Bahamas"

#: src/lib/Supported_Regions.php:23
msgid "Bahrain"
msgstr "Bahrain"

#: src/lib/Supported_Regions.php:24
msgid "Bangladesh"
msgstr "Bangladesch"

#: src/lib/Supported_Regions.php:25
msgid "Barbados"
msgstr "Barbados"

#: src/lib/Supported_Regions.php:26
msgid "Belarus"
msgstr "Belarus"

#: src/lib/Supported_Regions.php:27
msgid "Belgium"
msgstr "Belgien"

#: src/lib/Supported_Regions.php:28
msgid "Belize"
msgstr "Belize"

#: src/lib/Supported_Regions.php:29
msgid "Benin"
msgstr "Benin"

#: src/lib/Supported_Regions.php:30
msgid "Bermuda"
msgstr "Bermuda"

#: src/lib/Supported_Regions.php:31
msgid "Bhutan"
msgstr "Bhutan"

#: src/lib/Supported_Regions.php:32
msgid "Bolivia"
msgstr "Bolivien"

#: src/lib/Supported_Regions.php:33
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius und Saba"

#: src/lib/Supported_Regions.php:34
msgid "Bosnia and Herzegovina"
msgstr "Bosnien und Herzegowina"

#: src/lib/Supported_Regions.php:35
msgid "Botswana"
msgstr "Botswana"

#: src/lib/Supported_Regions.php:36
msgid "Bouvet Island"
msgstr "Bouvetinsel"

#: src/lib/Supported_Regions.php:37
msgid "Brazil"
msgstr "Brasilien"

#: src/lib/Supported_Regions.php:38
msgid "British Indian Ocean Territory"
msgstr "Britisches Territorium im Indischen Ozean"

#: src/lib/Supported_Regions.php:39
msgid "Brunei "
msgstr "Brunei"

#: src/lib/Supported_Regions.php:40
msgid "Bulgaria"
msgstr "Bulgarien"

#: src/lib/Supported_Regions.php:41
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: src/lib/Supported_Regions.php:42
msgid "Burundi"
msgstr "Burundi"

#: src/lib/Supported_Regions.php:43
msgid "Cambodia"
msgstr "Kambodscha"

#: src/lib/Supported_Regions.php:44
msgid "Cameroon"
msgstr "Kamerun"

#: src/lib/Supported_Regions.php:45
msgid "Canada"
msgstr "Kanada"

#: src/lib/Supported_Regions.php:46
msgid "Cape Verde"
msgstr "Kap Verde"

#: src/lib/Supported_Regions.php:47
msgid "Cayman Islands"
msgstr "Kaimaninseln"

#: src/lib/Supported_Regions.php:48
msgid "Central African Republic"
msgstr "Zentralafrikanische Republik"

#: src/lib/Supported_Regions.php:49
msgid "Chad"
msgstr "Tschad"

#: src/lib/Supported_Regions.php:50
msgid "Chile"
msgstr "Chile"

#: src/lib/Supported_Regions.php:51
msgid "China"
msgstr "China"

#: src/lib/Supported_Regions.php:52
msgid "Christmas Island"
msgstr "Weihnachtsinsel"

#: src/lib/Supported_Regions.php:53
msgid "Cocos (Keeling) Islands"
msgstr "Kokosinseln (Keelinginseln)"

#: src/lib/Supported_Regions.php:54
msgid "Colombia"
msgstr "Kolumbien"

#: src/lib/Supported_Regions.php:55
msgid "Comoros"
msgstr "Komoren"

#: src/lib/Supported_Regions.php:56
msgid "Congo"
msgstr "Kongo"

#: src/lib/Supported_Regions.php:57
msgid "Congo, the Democratic Republic of the"
msgstr "Demokratische Republik Kongo"

#: src/lib/Supported_Regions.php:58
msgid "Cook Islands"
msgstr "Cookinseln"

#: src/lib/Supported_Regions.php:59
msgid "Costa Rica"
msgstr "Costa Rica"

#: src/lib/Supported_Regions.php:60
msgid "Croatia"
msgstr "Kroatien"

#: src/lib/Supported_Regions.php:61
msgid "Cuba"
msgstr "Kuba"

#: src/lib/Supported_Regions.php:62
msgid "Curaçao"
msgstr "Curaçao"

#: src/lib/Supported_Regions.php:63
msgid "Cyprus"
msgstr "Zypern"

#: src/lib/Supported_Regions.php:64
msgid "Czech Republic"
msgstr "Tschechische Republik"

#: src/lib/Supported_Regions.php:65
msgid "Côte d'Ivoire"
msgstr "Elfenbeinküste"

#: src/lib/Supported_Regions.php:66
msgid "Denmark"
msgstr "Dänemark"

#: src/lib/Supported_Regions.php:67
msgid "Djibouti"
msgstr "Dschibuti"

#: src/lib/Supported_Regions.php:68
msgid "Dominica"
msgstr "Dominica"

#: src/lib/Supported_Regions.php:69
msgid "Dominican Republic"
msgstr "Dominikanische Republik"

#: src/lib/Supported_Regions.php:70
msgid "Ecuador"
msgstr "Ecuador"

#: src/lib/Supported_Regions.php:71
msgid "Egypt"
msgstr "Ägypten"

#: src/lib/Supported_Regions.php:72
msgid "El Salvador"
msgstr "El Salvador"

#: src/lib/Supported_Regions.php:73
msgid "Equatorial Guinea"
msgstr "Äquatorialguinea"

#: src/lib/Supported_Regions.php:74
msgid "Eritrea"
msgstr "Eritrea"

#: src/lib/Supported_Regions.php:75
msgid "Estonia"
msgstr "Estland"

#: src/lib/Supported_Regions.php:76
msgid "Ethiopia"
msgstr "Äthiopien"

#: src/lib/Supported_Regions.php:77
msgid "Falkland Islands (Malvinas)"
msgstr "Falklandinseln (Malwinen)"

#: src/lib/Supported_Regions.php:78
msgid "Faroe Islands"
msgstr "Färöer"

#: src/lib/Supported_Regions.php:79
msgid "Fiji"
msgstr "Fidschi"

#: src/lib/Supported_Regions.php:80
msgid "Finland"
msgstr "Finnland"

#: src/lib/Supported_Regions.php:81
msgid "France"
msgstr "Frankreich"

#: src/lib/Supported_Regions.php:82
msgid "French Guiana"
msgstr "Französisch-Guyana"

#: src/lib/Supported_Regions.php:83
msgid "French Polynesia"
msgstr "Französisch-Polynesien"

#: src/lib/Supported_Regions.php:84
msgid "French Southern and Antarctic Lands"
msgstr "Französische Süd- und Antarktisgebiete"

#: src/lib/Supported_Regions.php:85
msgid "Gabon"
msgstr "Gabun"

#: src/lib/Supported_Regions.php:86
msgid "Gambia"
msgstr "Gambia"

#: src/lib/Supported_Regions.php:87
msgid "Georgia"
msgstr "Georgien"

#: src/lib/Supported_Regions.php:88
msgid "Germany"
msgstr "Deutschland"

#: src/lib/Supported_Regions.php:89
msgid "Ghana"
msgstr "Ghana"

#: src/lib/Supported_Regions.php:90
msgid "Gibraltar"
msgstr "Gibraltar"

#: src/lib/Supported_Regions.php:91
msgid "Greece"
msgstr "Griechenland"

#: src/lib/Supported_Regions.php:92
msgid "Greenland"
msgstr "Grönland"

#: src/lib/Supported_Regions.php:93
msgid "Grenada"
msgstr "Grenada"

#: src/lib/Supported_Regions.php:94
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: src/lib/Supported_Regions.php:95
msgid "Guam"
msgstr "Guam"

#: src/lib/Supported_Regions.php:96
msgid "Guatemala"
msgstr "Guatemala"

#: src/lib/Supported_Regions.php:97
msgid "Guernsey"
msgstr "Guernsey"

#: src/lib/Supported_Regions.php:98
msgid "Guinea"
msgstr "Guinea"

#: src/lib/Supported_Regions.php:99
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: src/lib/Supported_Regions.php:100
msgid "Guyana"
msgstr "Guyana"

#: src/lib/Supported_Regions.php:101
msgid "Haiti"
msgstr "Haiti"

#: src/lib/Supported_Regions.php:102
msgid "Heard Island and McDonald Islands"
msgstr "Heard und McDonaldinseln"

#: src/lib/Supported_Regions.php:103
msgid "Holy See (Vatican City State)"
msgstr "Heiliger Stuhl (Staat Vatikanstadt)"

#: src/lib/Supported_Regions.php:104
msgid "Honduras"
msgstr "Honduras"

#: src/lib/Supported_Regions.php:105
msgid "Hong Kong"
msgstr "Hong Kong"

#: src/lib/Supported_Regions.php:106
msgid "Hungary"
msgstr "Ungarn"

#: src/lib/Supported_Regions.php:107
msgid "Iceland"
msgstr "Island"

#: src/lib/Supported_Regions.php:108
msgid "India"
msgstr "Indien"

#: src/lib/Supported_Regions.php:109
msgid "Indonesia"
msgstr "Indonesien"

#: src/lib/Supported_Regions.php:110
msgid "Iran"
msgstr "Iran"

#: src/lib/Supported_Regions.php:111
msgid "Iraq"
msgstr "Irak"

#: src/lib/Supported_Regions.php:112
msgid "Ireland"
msgstr "Irland"

#: src/lib/Supported_Regions.php:113
msgid "Isle of Man"
msgstr "Isle of Man"

#: src/lib/Supported_Regions.php:114
msgid "Israel"
msgstr "Israel"

#: src/lib/Supported_Regions.php:115
msgid "Italy"
msgstr "Italien"

#: src/lib/Supported_Regions.php:116
msgid "Jamaica"
msgstr "Jamaika"

#: src/lib/Supported_Regions.php:117
msgid "Japan"
msgstr "Japan"

#: src/lib/Supported_Regions.php:118
msgid "Jersey"
msgstr "Jersey"

#: src/lib/Supported_Regions.php:119
msgid "Jordan"
msgstr "Jordanien"

#: src/lib/Supported_Regions.php:120
msgid "Kazakhstan"
msgstr "Kasachstan"

#: src/lib/Supported_Regions.php:121
msgid "Kenya"
msgstr "Kenia"

#: src/lib/Supported_Regions.php:122
msgid "Kiribati"
msgstr "Kiribati"

#: src/lib/Supported_Regions.php:123
msgid "Kuwait"
msgstr "Kuwait"

#: src/lib/Supported_Regions.php:124
msgid "Kyrgyzstan"
msgstr "Kirgisistan"

#: src/lib/Supported_Regions.php:125
msgid "Laos"
msgstr "Laos"

#: src/lib/Supported_Regions.php:126
msgid "Latvia"
msgstr "Lettland"

#: src/lib/Supported_Regions.php:127
msgid "Lebanon"
msgstr "Libanon"

#: src/lib/Supported_Regions.php:128
msgid "Lesotho"
msgstr "Lesotho"

#: src/lib/Supported_Regions.php:129
msgid "Liberia"
msgstr "Liberia"

#: src/lib/Supported_Regions.php:130
msgid "Libya"
msgstr "Libyen"

#: src/lib/Supported_Regions.php:131
msgid "Liechtenstein"
msgstr "Liechtenstein"

#: src/lib/Supported_Regions.php:132
msgid "Lithuania"
msgstr "Litauen"

#: src/lib/Supported_Regions.php:133
msgid "Luxembourg"
msgstr "Luxemburg"

#: src/lib/Supported_Regions.php:134
msgid "Macao"
msgstr "Macau"

#: src/lib/Supported_Regions.php:135
msgid "North Macedonia"
msgstr "Nordmazedonien"

#: src/lib/Supported_Regions.php:136
msgid "Madagascar"
msgstr "Madagaskar"

#: src/lib/Supported_Regions.php:137
msgid "Malawi"
msgstr "Malawi"

#: src/lib/Supported_Regions.php:138
msgid "Malaysia"
msgstr "Malaysia"

#: src/lib/Supported_Regions.php:139
msgid "Maldives"
msgstr "Malediven"

#: src/lib/Supported_Regions.php:140
msgid "Mali"
msgstr "Mali"

#: src/lib/Supported_Regions.php:141
msgid "Malta"
msgstr "Malta"

#: src/lib/Supported_Regions.php:142
msgid "Marshall Islands"
msgstr "Marshallinseln"

#: src/lib/Supported_Regions.php:143
msgid "Martinique"
msgstr "Martinique"

#: src/lib/Supported_Regions.php:144
msgid "Mauritania"
msgstr "Mauretanien"

#: src/lib/Supported_Regions.php:145
msgid "Mauritius"
msgstr "Mauritius"

#: src/lib/Supported_Regions.php:146
msgid "Mayotte"
msgstr "Mayotte"

#: src/lib/Supported_Regions.php:147
msgid "Mexico"
msgstr "Mexiko"

#: src/lib/Supported_Regions.php:148
msgid "Micronesia, Federated States of"
msgstr "Mikronesien, Föderierten Staaten von"

#: src/lib/Supported_Regions.php:149
msgid "Moldova"
msgstr "Moldau"

#: src/lib/Supported_Regions.php:150
msgid "Monaco"
msgstr "Monaco"

#: src/lib/Supported_Regions.php:151
msgid "Mongolia"
msgstr "Mongolei"

#: src/lib/Supported_Regions.php:152
msgid "Montenegro"
msgstr "Montenegro"

#: src/lib/Supported_Regions.php:153
msgid "Montserrat"
msgstr "Montserrat"

#: src/lib/Supported_Regions.php:154
msgid "Morocco"
msgstr "Marokko"

#: src/lib/Supported_Regions.php:155
msgid "Mozambique"
msgstr "Mosambik"

#: src/lib/Supported_Regions.php:156
msgid "Myanmar"
msgstr "Myanmar"

#: src/lib/Supported_Regions.php:157
msgid "Namibia"
msgstr "Namibia"

#: src/lib/Supported_Regions.php:158
msgid "Nauru"
msgstr "Nauru"

#: src/lib/Supported_Regions.php:159
msgid "Nepal"
msgstr "Nepal"

#: src/lib/Supported_Regions.php:160
msgid "Netherlands"
msgstr "Niederlande"

#: src/lib/Supported_Regions.php:161
msgid "New Caledonia"
msgstr "Neukaledonien"

#: src/lib/Supported_Regions.php:162
msgid "New Zealand"
msgstr "Neuseeland"

#: src/lib/Supported_Regions.php:163
msgid "Nicaragua"
msgstr "Nicaragua"

#: src/lib/Supported_Regions.php:164
msgid "Niger"
msgstr "Niger"

#: src/lib/Supported_Regions.php:165
msgid "Nigeria"
msgstr "Nigeria"

#: src/lib/Supported_Regions.php:166
msgid "Niue"
msgstr "Niue"

#: src/lib/Supported_Regions.php:167
msgid "Norfolk Island"
msgstr "Norfolkinseln"

#: src/lib/Supported_Regions.php:168
msgid "North Korea"
msgstr "Nordkorea"

#: src/lib/Supported_Regions.php:169
msgid "Northern Mariana Islands"
msgstr "Nördliche Marianen"

#: src/lib/Supported_Regions.php:170
msgid "Norway"
msgstr "Norwegen"

#: src/lib/Supported_Regions.php:171
msgid "Oman"
msgstr "Oman"

#: src/lib/Supported_Regions.php:172
msgid "Pakistan"
msgstr "Pakistan"

#: src/lib/Supported_Regions.php:173
msgid "Palau"
msgstr "Palau"

#: src/lib/Supported_Regions.php:174
msgid "Palestinian Territory"
msgstr "Palästinensische Autonomiegebiete"

#: src/lib/Supported_Regions.php:175
msgid "Panama"
msgstr "Panama"

#: src/lib/Supported_Regions.php:176
msgid "Papua New Guinea"
msgstr "Papua-Neuguinea"

#: src/lib/Supported_Regions.php:177
msgid "Paraguay"
msgstr "Paraguay"

#: src/lib/Supported_Regions.php:178
msgid "Peru"
msgstr "Peru"

#: src/lib/Supported_Regions.php:179
msgid "Philippines"
msgstr "Philippinen"

#: src/lib/Supported_Regions.php:180
msgid "Pitcairn"
msgstr "Pitcairninseln"

#: src/lib/Supported_Regions.php:181
msgid "Poland"
msgstr "Polen"

#: src/lib/Supported_Regions.php:182
msgid "Portugal"
msgstr "Portugal"

#: src/lib/Supported_Regions.php:183
msgid "Puerto Rico"
msgstr "Puerto Rico"

#: src/lib/Supported_Regions.php:184
msgid "Qatar"
msgstr "Katar"

#: src/lib/Supported_Regions.php:185
msgid "Romania"
msgstr "Rumänien"

#: src/lib/Supported_Regions.php:186
msgid "Russia"
msgstr "Russland"

#: src/lib/Supported_Regions.php:187
msgid "Rwanda"
msgstr "Ruanda"

#: src/lib/Supported_Regions.php:188
msgid "Réunion"
msgstr "Réunion"

#: src/lib/Supported_Regions.php:189
msgid "Saint Barthélemy"
msgstr "Saint Barthélemy"

#: src/lib/Supported_Regions.php:190
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr "St. Helena, Ascension und Tristan da Cunha"

#: src/lib/Supported_Regions.php:191
msgid "Saint Kitts and Nevis"
msgstr "St. Kitts und Nevis"

#: src/lib/Supported_Regions.php:192
msgid "Saint Lucia"
msgstr "St. Lucia"

#: src/lib/Supported_Regions.php:193
msgid "Saint Martin (French part)"
msgstr "St. Martin (Französischer Teil)"

#: src/lib/Supported_Regions.php:194
msgid "Saint Pierre and Miquelon"
msgstr "Saint-Pierre und Miquelon"

#: src/lib/Supported_Regions.php:195
msgid "Saint Vincent and the Grenadines"
msgstr "St. Vincent und die Grenadinen"

#: src/lib/Supported_Regions.php:196
msgid "Samoa"
msgstr "Samoa"

#: src/lib/Supported_Regions.php:197
msgid "San Marino"
msgstr "San Marino"

#: src/lib/Supported_Regions.php:198
msgid "Sao Tome and Principe"
msgstr "Sao Tome und Principe"

#: src/lib/Supported_Regions.php:199
msgid "Saudi Arabia"
msgstr "Saudi-Arabien"

#: src/lib/Supported_Regions.php:200
msgid "Senegal"
msgstr "Senegal"

#: src/lib/Supported_Regions.php:201
msgid "Serbia"
msgstr "Serbien"

#: src/lib/Supported_Regions.php:202
msgid "Seychelles"
msgstr "Seychellen"

#: src/lib/Supported_Regions.php:203
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: src/lib/Supported_Regions.php:204
msgid "Singapore"
msgstr "Singapur"

#: src/lib/Supported_Regions.php:205
msgid "Sint Maarten (Dutch part)"
msgstr "Sint Maarten (Niederländischer Teil)"

#: src/lib/Supported_Regions.php:206
msgid "Slovakia"
msgstr "Slowakei"

#: src/lib/Supported_Regions.php:207
msgid "Slovenia"
msgstr "Slowenien"

#: src/lib/Supported_Regions.php:208
msgid "Solomon Islands"
msgstr "Salomonen"

#: src/lib/Supported_Regions.php:209
msgid "Somalia"
msgstr "Somalia"

#: src/lib/Supported_Regions.php:210
msgid "South Africa"
msgstr "Südafrika"

#: src/lib/Supported_Regions.php:211
msgid "South Georgia and the South Sandwich Islands"
msgstr "Südgeorgien und die Südlichen Sandwichinseln"

#: src/lib/Supported_Regions.php:212
msgid "South Korea"
msgstr "Südkorea"

#: src/lib/Supported_Regions.php:213
msgid "South Sudan"
msgstr "Südsudan"

#: src/lib/Supported_Regions.php:214
msgid "Spain"
msgstr "Spanien"

#: src/lib/Supported_Regions.php:215
msgid "Sri Lanka"
msgstr "Sri Lanka"

#: src/lib/Supported_Regions.php:216
msgid "Sudan"
msgstr "Sudan"

#: src/lib/Supported_Regions.php:217
msgid "Suriname"
msgstr "Suriname"

#: src/lib/Supported_Regions.php:218
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard und Jan Mayen"

#: src/lib/Supported_Regions.php:219
msgid "Eswatini"
msgstr "Eswatini"

#: src/lib/Supported_Regions.php:220
msgid "Sweden"
msgstr "Schweden"

#: src/lib/Supported_Regions.php:221
msgid "Switzerland"
msgstr "Schweiz"

#: src/lib/Supported_Regions.php:222
msgid "Syria"
msgstr "Syrien"

#: src/lib/Supported_Regions.php:223
msgid "Taiwan"
msgstr "Taiwan"

#: src/lib/Supported_Regions.php:224
msgid "Tajikistan"
msgstr "Tadschikistan"

#: src/lib/Supported_Regions.php:225
msgid "Tanzania"
msgstr "Tansania"

#: src/lib/Supported_Regions.php:226
msgid "Thailand"
msgstr "Thailand"

#: src/lib/Supported_Regions.php:227
msgid "Timor-Leste"
msgstr "Timor-Leste"

#: src/lib/Supported_Regions.php:228
msgid "Togo"
msgstr "Togo"

#: src/lib/Supported_Regions.php:229
msgid "Tokelau"
msgstr "Tokelau"

#: src/lib/Supported_Regions.php:230
msgid "Tonga"
msgstr "Tonga"

#: src/lib/Supported_Regions.php:231
msgid "Trinidad and Tobago"
msgstr "Trinidad und Tobago"

#: src/lib/Supported_Regions.php:232
msgid "Tunisia"
msgstr "Tunesien"

#: src/lib/Supported_Regions.php:233
msgid "Türkiye"
msgstr "Türkei"

#: src/lib/Supported_Regions.php:234
msgid "Turkmenistan"
msgstr "Turkmenistan"

#: src/lib/Supported_Regions.php:235
msgid "Turks and Caicos Islands"
msgstr "Turks- und Caicosinseln"

#: src/lib/Supported_Regions.php:236
msgid "Tuvalu"
msgstr "Tuvalu"

#: src/lib/Supported_Regions.php:237
msgid "Uganda"
msgstr "Uganda"

#: src/lib/Supported_Regions.php:238
msgid "Ukraine"
msgstr "Ukraine"

#: src/lib/Supported_Regions.php:239
msgid "United Arab Emirates"
msgstr "Vereinigte Arabische Emirate"

#: src/lib/Supported_Regions.php:240
msgid "United Kingdom"
msgstr "Vereinigtes Königreich"

#: src/lib/Supported_Regions.php:241
msgid "United States"
msgstr "Vereinigte Staaten"

#: src/lib/Supported_Regions.php:242
msgid "United States - State of California"
msgstr "Vereinigte Staaten - Bundesstaat Kalifornien"

#: src/lib/Supported_Regions.php:243
msgid "United States - State of Colorado"
msgstr "Vereinigte Staaten - Bundesstaat Colorado "

#: src/lib/Supported_Regions.php:244
msgid "United States - State of Connecticut"
msgstr "Vereinigte Staaten - Bundesstaat Connecticut"

#: src/lib/Supported_Regions.php:245
msgid "United States - State of Utah"
msgstr "Vereinigte Staaten - Bundesstaat Utah"

#: src/lib/Supported_Regions.php:246
msgid "United States - State of Virginia"
msgstr "Vereinigte Staaten - Bundesstaat Virginia"

#: src/lib/Supported_Regions.php:247
msgid "United States Minor Outlying Islands"
msgstr "Kleinere Amerikanische Überseeinseln"

#: src/lib/Supported_Regions.php:248
msgid "Uruguay"
msgstr "Uruguay"

#: src/lib/Supported_Regions.php:249
msgid "Uzbekistan"
msgstr "Usbekistan"

#: src/lib/Supported_Regions.php:250
msgid "Vanuatu"
msgstr "Vanuatu"

#: src/lib/Supported_Regions.php:251
msgid "Venezuela"
msgstr "Venezuela"

#: src/lib/Supported_Regions.php:252
msgid "Viet Nam"
msgstr "Vietnam"

#: src/lib/Supported_Regions.php:253
msgid "Virgin Islands, British"
msgstr "Britische Jungferninseln"

#: src/lib/Supported_Regions.php:254
msgid "Virgin Islands, U.S."
msgstr "Amerikanische Jungferninseln"

#: src/lib/Supported_Regions.php:255
msgid "Wallis and Futuna"
msgstr "Wallis und Futuna"

#: src/lib/Supported_Regions.php:256
msgid "Western Sahara"
msgstr "Westsahara"

#: src/lib/Supported_Regions.php:257
msgid "Yemen"
msgstr "Jemen"

#: src/lib/Supported_Regions.php:258
msgid "Zambia"
msgstr "Sambia"

#: src/lib/Supported_Regions.php:259
msgid "Zimbabwe"
msgstr "Simbabwe"

#: src/lib/Supported_Regions.php:260
msgid "Åland Islands"
msgstr "Åland Inseln"

#: src/settings/Network_Menu_Settings.php:36
#: src/settings/pages/Dashboard_Page.php:21
msgid "Cookiebot"
msgstr "Cookiebot"

#: src/settings/Network_Menu_Settings.php:44
#: src/settings/pages/Settings_Page.php:23
msgid "Cookiebot Settings"
msgstr "Cookiebot Einstellungen"

#: src/settings/Network_Menu_Settings.php:45
#: src/settings/pages/Settings_Page.php:24
#: src/view/admin/common/settings-page.php:30
#: src/view/admin/common/templates/main-tabs.php:32
#: src/view/admin/uc_frame/settings-page.php:47
#: src/view/admin/cb_frame/settings-page.php:50
msgid "Settings"
msgstr "Einstellungen"

#: src/settings/Network_Menu_Settings.php:52
#: src/settings/pages/Support_Page.php:18
msgid "Cookiebot Support"
msgstr "Cookiebot Support"

#: src/settings/Network_Menu_Settings.php:53
#: src/settings/pages/Support_Page.php:19
#: src/view/admin/cb_frame/support-page.php:19
#: src/view/admin/common/support-page.php:19
#: src/view/admin/common/templates/main-tabs.php:50
#: src/view/admin/uc_frame/support-page.php:19
msgid "Support"
msgstr "Support"

#: src/settings/pages/Dashboard_Page.php:34
msgid "Cookiebot Dashboard"
msgstr "Cookiebot Dashboard"

#: src/settings/pages/Debug_Page.php:27 src/settings/pages/Debug_Page.php:28
#: src/view/admin/cb_frame/debug-page.php:18
#: src/view/admin/common/templates/main-tabs.php:57
#: src/view/admin/uc_frame/debug-page.php:18
msgid "Debug info"
msgstr "Information zur Fehlerbehebung"

#: src/settings/pages/Iab_Page.php:20 src/settings/pages/Iab_Page.php:21
msgid "IAB"
msgstr "IAB"

#: src/settings/pages/Iab_Page.php:130
msgid "Purposes of data use"
msgstr "Zweck der Datennutzung"

#: src/settings/pages/Iab_Page.php:131
msgid ""
"Inform your users how you’ll use their data. We’ll show this on the second "
"layer of your consent banner, where users interested in more granular detail "
"about data processing can view it."
msgstr ""
"Informieren Sie Ihre Nutzer darüber, wie Sie ihre Daten verwenden werden. "
"Wir zeigen detaillierte Informationen auf dem zweiten Layer Ihres Consent-"
"Banners an, wo Nutzer, die sich genauer über die Datenverarbeitung "
"informieren möchten, dies nachlesen können."

#: src/settings/pages/Iab_Page.php:138
msgid "Special purposes of data use"
msgstr "Besondere Zwecke der Datennutzung"

#: src/settings/pages/Iab_Page.php:139
msgid ""
"Inform your users about special purposes of using their data. We’ll show "
"this on the second layer of your consent banner."
msgstr ""
"Informieren Sie Ihre Nutzer über besondere Nutzungszwecke ihrer Daten. Diese "
"Informationen werden auf dem zweiten Layer Ihres Consent-Banners angezeigt."

#: src/settings/pages/Iab_Page.php:146
msgid "Features required for data processing"
msgstr "Für die Datenverarbeitung erforderliche Features"

#: src/settings/pages/Iab_Page.php:147
msgid ""
"Inform users about the features necessary for processing their personal "
"data. We’ll list the selected features on the second layer of your consent "
"banner."
msgstr ""
"Informieren Sie Ihre Nutzer über die Features, die für die Verarbeitung "
"ihrer personenbezogenen Daten erforderlich sind. Die ausgewählten Features "
"werden auf dem zweiten Layer Ihres Consent-Banners aufgelistet."

#: src/settings/pages/Iab_Page.php:154
msgid "Special features required for data processing"
msgstr "Für die Datenverarbeitung erforderliche besondere Features"

#: src/settings/pages/Iab_Page.php:155
msgid ""
"Inform users about any specially categorized features required for "
"processing their personal data. We’ll list the selected features on the "
"second layer of your consent banner, offering options for users to enable or "
"disable them."
msgstr ""
"Informieren Sie Ihre Nutzer über alle besonderen Features, die für die "
"Verarbeitung ihrer personenbezogenen Daten erforderlich sind. Die "
"ausgewählten Features werden auf dem zweiten Layer Ihres Consent-Banners "
"aufgelistet und bieten Ihren Nutzern die Möglichkeit, diese zu aktivieren "
"oder zu deaktivieren."

#: src/settings/pages/Iab_Page.php:162
msgid "TCF listed vendors"
msgstr "TCF-gelistete Anbieter"

#: src/settings/pages/Iab_Page.php:183
msgid "Store and/or access information on a device"
msgstr "Informationen auf einem Gerät speichern und/oder darauf zugreifen"

#: src/settings/pages/Iab_Page.php:187
msgid "Use limited data to select advertising"
msgstr "Eingeschränkte Daten verwenden, um Werbung auszuwählen"

#: src/settings/pages/Iab_Page.php:191
msgid "Create profiles for personalised advertising"
msgstr "Profile für personalisierte Werbung erstellen"

#: src/settings/pages/Iab_Page.php:195
msgid "Use profiles to select personalised advertising"
msgstr "Profile verwenden, um personalisierte Werbung auszuwählen"

#: src/settings/pages/Iab_Page.php:199
msgid "Create profiles to personalise content"
msgstr "Profile erstellen, um Inhalte zu personalisieren"

#: src/settings/pages/Iab_Page.php:203
msgid "Use profiles to select personalised content"
msgstr "Profile verwenden, um personalisierte Inhalte auszuwählen"

#: src/settings/pages/Iab_Page.php:207
msgid "Measure advertising performance"
msgstr "Performance der Werbung messen"

#: src/settings/pages/Iab_Page.php:211
msgid "Measure content performance"
msgstr "Performance der Inhalte messen"

#: src/settings/pages/Iab_Page.php:215
msgid ""
"Understand audiences through statistics or combinations of data from "
"different sources"
msgstr ""
"Durch Statistiken oder Kombinationen von Daten aus verschiedenen Quellen "
"mehr über Zielgruppen erfahren"

#: src/settings/pages/Iab_Page.php:219
msgid "Develop and improve services"
msgstr "Services entwickeln und verbessern"

#: src/settings/pages/Iab_Page.php:223
msgid "Use limited data to select content"
msgstr "Eingeschränkte Daten verwenden, um Inhalte auszuwählen"

#: src/settings/pages/Iab_Page.php:229
msgid "Ensure security, prevent and detect fraud, and fix errors"
msgstr ""
"Sicherheit gewährleisten, Betrug erkennen und verhindern, und Fehler beheben"

#: src/settings/pages/Iab_Page.php:233
msgid "Deliver and present advertising and content"
msgstr "Werbung und Inhalte bereitstellen und präsentieren"

#: src/settings/pages/Iab_Page.php:239
msgid "Match and combine data from other data sources"
msgstr "Daten aus anderen Datenquellen vergleichen und kombinieren"

#: src/settings/pages/Iab_Page.php:243
msgid "Link different devices"
msgstr "Verschiedene Geräte verknüpfen"

#: src/settings/pages/Iab_Page.php:247
msgid "Identify devices based on information transmitted automatically"
msgstr "Geräte anhand automatisch übermittelter Informationen identifizieren"

#: src/settings/pages/Iab_Page.php:253
msgid "Use precise geolocation data"
msgstr "Präzise Geolokalisierungsdaten verwenden"

#: src/settings/pages/Iab_Page.php:257
msgid "Actively scan device characteristics for identification"
msgstr "Gerätemerkmale aktiv zur Identifizierung scannen"

#: src/shortcode/Cookiebot_Declaration_Shortcode.php:53
msgid "Please add your Cookiebot ID to show Cookie Declarations"
msgstr ""
"Bitte fügen Sie Ihre Cookiebot-ID hinzu, um Cookie-Erklärungen anzuzeigen"

#: src/shortcode/Cookiebot_Embedding_Shortcode.php:28
msgid "Please add a service ID into the shortcode \"service\" parameter."
msgstr ""
"Bitte fügen Sie eine Service-ID in den Parameter des Shortcodes „Service“ "
"ein."

#: src/view/admin/cb_frame/dashboard-page.php:33
msgid "I already have a Cookiebot CMP account"
msgstr "Ich habe bereits ein Cookiebot CMP-Konto"

#: src/view/admin/cb_frame/dashboard-page.php:37
msgid "Connect my existing account"
msgstr "Mit meinem bestehenden Konto verbinden"

#: src/view/admin/cb_frame/dashboard-page.php:42
msgid "Your Cookiebot CMP for WordPress solution"
msgstr "Ihre Cookiebot CMP-Lösung für WordPress"

#: src/view/admin/cb_frame/dashboard-page.php:47
#: src/view/admin/uc_frame/dashboard-page.php:35
msgid "Account added"
msgstr "Konto hinzugefügt"

#: src/view/admin/cb_frame/dashboard-page.php:50
msgid "Congratulations!"
msgstr "Herzlichen Glückwunsch!"

#: src/view/admin/cb_frame/dashboard-page.php:51
msgid "You have added your Domain Group ID to WordPress. You are all set!"
msgstr ""
"Sie haben Ihre Domain Group ID für WordPress hinzugefügt. Sie sind startklar!"

#: src/view/admin/cb_frame/dashboard-page.php:62
#: src/view/admin/uc_frame/dashboard-page.php:48
msgid "Your opinion matters"
msgstr "Ihre Meinung zählt!"

#: src/view/admin/cb_frame/dashboard-page.php:65
msgid ""
"Are you happy with our WordPress plugin? Your feedback will help us make our "
"product better for you."
msgstr ""
"Sind Sie mit unserem WordPress-Plug-in zufrieden? Ihr Feedback wird uns "
"helfen, unser Produkt für Sie zu verbessern."

#: src/view/admin/cb_frame/dashboard-page.php:69
msgid "Write a review"
msgstr "Bewertung hinterlassen"

#: src/view/admin/cb_frame/dashboard-page.php:80
#: src/view/admin/common/dashboard-page.php:45
msgid "Get started"
msgstr "Jetzt starten"

#: src/view/admin/cb_frame/dashboard-page.php:83
msgid "Create a new Cookiebot CMP account"
msgstr "Neues Cookiebot CMP-Konto erstellen"

#: src/view/admin/cb_frame/dashboard-page.php:87
#: src/view/admin/common/dashboard-page.php:52
msgid "Create a new account"
msgstr "Neues Konto erstellen"

#: src/view/admin/cb_frame/dashboard-page.php:92
msgid "Learn more about how to optimize your Cookiebot CMP setup?"
msgstr ""
"Erfahren Sie mehr darüber, wie Sie Ihr Cookiebot CMP Setup optimieren können!"

#: src/view/admin/cb_frame/dashboard-page.php:96
msgid "Visit Help Center"
msgstr "Hilfe-Center besuchen"

#: src/view/admin/cb_frame/dashboard-page.php:113
#: src/view/admin/cb_frame/support-page.php:43
#: src/view/admin/common/dashboard-page.php:63
#: src/view/admin/common/support-page.php:43
#: src/view/admin/uc_frame/dashboard-page.php:66
#: src/view/admin/uc_frame/support-page.php:43
msgid "Video guide"
msgstr "Video-Anleitung"

#: src/view/admin/cb_frame/dashboard-page.php:115
msgid "How to get started with Cookiebot CMP"
msgstr "Mit Cookiebot CMP loslegen - So geht’s!"

#: src/view/admin/cb_frame/dashboard-page.php:119
msgid "Learn more about Cookiebot CMP"
msgstr "Mehr über Cookiebot CMP erfahren"

#: src/view/admin/cb_frame/dashboard-page.php:131
#: src/view/admin/common/dashboard-page.php:81
#: src/view/admin/uc_frame/dashboard-page.php:84
#: src/view/admin/uc_frame/settings/embeddings-page.php:32
msgid "GDPR"
msgstr "DSGVO"

#: src/view/admin/cb_frame/dashboard-page.php:134
#: src/view/admin/common/dashboard-page.php:84
#: src/view/admin/uc_frame/dashboard-page.php:87
msgid "Europe"
msgstr "Europa"

#: src/view/admin/cb_frame/dashboard-page.php:138
#: src/view/admin/cb_frame/dashboard-page.php:140
#: src/view/admin/cb_frame/dashboard-page.php:155
#: src/view/admin/cb_frame/dashboard-page.php:157
#: src/view/admin/common/dashboard-page.php:88
#: src/view/admin/common/dashboard-page.php:90
#: src/view/admin/common/dashboard-page.php:105
#: src/view/admin/common/dashboard-page.php:107
#: src/view/admin/uc_frame/dashboard-page.php:91
#: src/view/admin/uc_frame/dashboard-page.php:93
#: src/view/admin/uc_frame/dashboard-page.php:108
#: src/view/admin/uc_frame/dashboard-page.php:110
msgid "Learn More"
msgstr "Mehr erfahren"

#: src/view/admin/cb_frame/dashboard-page.php:148
#: src/view/admin/common/dashboard-page.php:98
#: src/view/admin/uc_frame/dashboard-page.php:101
msgid "CCPA"
msgstr "CCPA"

#: src/view/admin/cb_frame/dashboard-page.php:151
#: src/view/admin/common/dashboard-page.php:101
#: src/view/admin/uc_frame/dashboard-page.php:104
msgid "North America"
msgstr "Nordamerika"

#: src/view/admin/cb_frame/dashboard-page.php:162
#: src/view/admin/common/dashboard-page.php:112
msgid "See other legislations"
msgstr "Andere Rechtsvorschriften ansehen"

#: src/view/admin/cb_frame/debug-page.php:22
msgid "Debug information"
msgstr "Informationen zur Fehlerbehebung"

#: src/view/admin/cb_frame/debug-page.php:25
msgid ""
"The information below is for debugging purposes. If you have any issues with "
"your Cookiebot CMP integration, this information is the best place to start."
msgstr ""
"Die folgenden Informationen dienen der Fehlersuche und -behebung. Wenn Sie "
"Probleme mit Ihrer Cookiebot CMP-Integration haben, können Ihnen diese "
"Informationen weiterhelfen."

#: src/view/admin/cb_frame/debug-page.php:33
#: src/view/admin/uc_frame/debug-page.php:33
msgid "Copy debug information to clipboard"
msgstr ""
"Informationen zur Fehlersuche und -behebung in die Zwischenablage kopieren"

#: src/view/admin/cb_frame/debug-page.php:42
msgid ""
"If you have any issues with the implemenation of Cookiebot CMP, please visit "
"our Support Center."
msgstr ""
"Wenn Sie Probleme mit der Implementierung Ihrer Cookiebot CMP haben, "
"besuchen Sie gerne unser Support-Center."

#: src/view/admin/cb_frame/debug-page.php:45
#: src/view/admin/uc_frame/debug-page.php:44
msgid "Visit Support Center"
msgstr "Support-Center besuchen"

#: src/view/admin/cb_frame/network-settings-page.php:22
#: src/view/admin/common/network-settings-page.php:23
#: src/view/admin/uc_frame/network-settings-page.php:23
msgid "Network Settings"
msgstr "Netzwerkeinstellungen"

#: src/view/admin/cb_frame/network-settings-page.php:33
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:3
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:3
#: src/view/admin/uc_frame/network-settings-page.php:34
msgid "Are you sure?"
msgstr "Sind Sie sicher? "

#: src/view/admin/cb_frame/network-settings-page.php:36
#: src/view/admin/uc_frame/network-settings-page.php:37
msgid ""
"You will need to add a new ID before updating other network settings. If any "
"subsite is using its own account disconnecting this account won’t affect it."
msgstr ""
"Sie müssen eine neue ID hinzufügen, bevor Sie andere Netzwerkeinstellungen "
"aktualisieren. Wenn eine Unterseite ihr eigenes Konto verwendet, wirkt sich "
"die Abmeldung dieses Kontos nicht auf sie aus."

#: src/view/admin/cb_frame/network-settings-page.php:40
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:10
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:10
#: src/view/admin/uc_frame/network-settings-page.php:41
msgid "Cancel"
msgstr "Abbrechen"

#: src/view/admin/cb_frame/network-settings-page.php:43
#: src/view/admin/cb_frame/network-settings-page.php:65
#: src/view/admin/cb_frame/settings/general-page.php:52
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:13
#: src/view/admin/uc_frame/network-settings-page.php:44
#: src/view/admin/uc_frame/network-settings-page.php:75
#: src/view/admin/uc_frame/settings/general-page.php:45
msgid "Disconnect account"
msgstr "Konto abmelden"

#: src/view/admin/cb_frame/network-settings-page.php:50
msgid "Network Domain Group ID"
msgstr "Network-Domain-Gruppen-ID"

#: src/view/admin/cb_frame/network-settings-page.php:52
msgid ""
"If added this will be the default Cookiebot ID for all subsites. Subsites "
"are able to override the Cookiebot ID."
msgstr ""
"Wenn hinzugefügt, ist dies die Standard-Cookiebot-ID für alle Unterseiten. "
"Unterseiten können die Cookiebot-ID überschreiben"

#: src/view/admin/cb_frame/network-settings-page.php:54
#: src/view/admin/cb_frame/settings/general-page.php:37
msgid "Read more on the Domain Group ID"
msgstr "Lesen Sie mehr über die Domain Group ID"

#: src/view/admin/cb_frame/network-settings-page.php:58
#: src/view/admin/cb_frame/settings/general-page.php:43
msgid "Add your Domain Group ID"
msgstr "Ihre Domain Group ID hinzufügen"

#: src/view/admin/cb_frame/network-settings-page.php:73
#: src/view/admin/cb_frame/settings/general-page.php:146
#: src/view/admin/uc_frame/network-settings-page.php:111
#: src/view/admin/uc_frame/settings/general-page.php:120
msgid "Cookie-blocking"
msgstr "Cookie-Blocking"

#: src/view/admin/cb_frame/network-settings-page.php:75
msgid ""
"Select your cookie-blocking mode here. Auto cookie-blocking mode will "
"automatically block all cookies (except for ‘strictly necessary’ cookies) "
"until a user has given consent. Manual cookie-blocking mode requests manual "
"adjustments to the cookie-setting scripts. Please find our implementation "
"guides below:"
msgstr ""
"Wählen Sie hier Ihren Cookie-Blockierungsmodus. Der automatische Cookie-"
"Blockierungsmodus blockiert automatisch alle Cookies (mit Ausnahme der "
"„unbedingt notwendigen“ Cookies), bis der Nutzer seine Einwilligung gegeben "
"hat. Der manuelle Cookie-Blockierungsmodus erfordert manuelle Anpassungen "
"der Skripte beim Setzen von Cookies. Unsere Implementierungsanleitungen "
"finden Sie weiter unten:"

#: src/view/admin/cb_frame/network-settings-page.php:80
#: src/view/admin/cb_frame/settings/general-page.php:167
#: src/view/admin/uc_frame/network-settings-page.php:118
#: src/view/admin/uc_frame/settings/general-page.php:133
msgid "Select cookie-blocking mode"
msgstr "Cookie-Blocking-Mode auswählen"

#: src/view/admin/cb_frame/network-settings-page.php:87
#: src/view/admin/cb_frame/settings/general-page.php:175
#: src/view/admin/uc_frame/network-settings-page.php:125
#: src/view/admin/uc_frame/settings/general-page.php:141
msgid "Automatic"
msgstr "Cookie-Blocking-Mode auswählen"

#: src/view/admin/cb_frame/network-settings-page.php:88
#: src/view/admin/cb_frame/settings/general-page.php:176
#: src/view/admin/uc_frame/network-settings-page.php:126
#: src/view/admin/uc_frame/settings/general-page.php:142
msgid "Recommended"
msgstr "Empfohlen"

#: src/view/admin/cb_frame/network-settings-page.php:96
#: src/view/admin/cb_frame/network-settings-page.php:146
#: src/view/admin/cb_frame/network-settings-page.php:193
#: src/view/admin/uc_frame/network-settings-page.php:134
msgid "Choose per subsite"
msgstr "Wählen Sie pro Unterseite"

#: src/view/admin/cb_frame/network-settings-page.php:104
#: src/view/admin/cb_frame/settings/general-page.php:206
msgid "Cookiebot™ script tag"
msgstr "Cookiebot™ Skript Tag"

#: src/view/admin/cb_frame/network-settings-page.php:106
msgid ""
"Add async or defer attribute to Cookie banner script tag. Default: Choose "
"per subsite"
msgstr ""
"Fügen Sie dem Skript-Tag des Cookie-Banners das Attribut async oder defer "
"hinzu. Standard: Wählen Sie pro Unterwebsite"

#: src/view/admin/cb_frame/network-settings-page.php:109
msgid "This feature is only available when not using Auto Blocking"
msgstr ""
"Diese Feature ist nur verfügbar, wenn Sie automatische Blockierung nicht "
"verwenden"

#: src/view/admin/cb_frame/network-settings-page.php:112
#: src/view/admin/cb_frame/network-settings-page.php:156
#: src/view/admin/cb_frame/network-settings-page.php:228
#: src/view/admin/uc_frame/network-settings-page.php:169
msgid ""
"Setting will apply for all subsites. Subsites will not be able to override."
msgstr ""
"Die Einstellung gilt für alle Unterseiten. Unterwebsites können nicht "
"überschrieben werden."

#: src/view/admin/cb_frame/network-settings-page.php:125
#: src/view/admin/cb_frame/network-settings-page.php:172
#: src/view/admin/cb_frame/settings/additional-page.php:99
#: src/view/admin/cb_frame/settings/general-page.php:222
msgid "None"
msgstr "Keine"

#: src/view/admin/cb_frame/network-settings-page.php:154
msgid "Cookiebot declaration script tag"
msgstr "Cookie-Erklärung Skript Tag"

#: src/view/admin/cb_frame/network-settings-page.php:159
msgid ""
"Add async or defer attribute to Cookie declaration script tag. Default: "
"Choose per subsite"
msgstr ""
"Fügen Sie dem Skript-Tag der Cookie-Deklaration das Attribut async oder "
"defer hinzu. Standard: Wählen Sie pro Unterwebsite"

#: src/view/admin/cb_frame/network-settings-page.php:201
#: src/view/admin/cb_frame/settings/additional-page.php:10
#: src/view/admin/uc_frame/network-settings-page.php:142
#: src/view/admin/uc_frame/settings/additional-page.php:10
msgid "Automatic updates"
msgstr "Automatische Aktualisierung "

#: src/view/admin/cb_frame/network-settings-page.php:203
#: src/view/admin/cb_frame/settings/additional-page.php:13
#: src/view/admin/uc_frame/network-settings-page.php:144
#: src/view/admin/uc_frame/settings/additional-page.php:13
msgid ""
"Enable automatic updates whenever we release a new version of the plugin."
msgstr ""
"Aktivieren Sie automatische Aktualisierung für jede neue Version des Plugins."

#: src/view/admin/cb_frame/network-settings-page.php:218
#: src/view/admin/cb_frame/settings/additional-page.php:28
#: src/view/admin/uc_frame/network-settings-page.php:159
#: src/view/admin/uc_frame/settings/additional-page.php:28
msgid "Automatically update to new version"
msgstr "Automatisch auf neue Version aktualisieren"

#: src/view/admin/cb_frame/network-settings-page.php:226
#: src/view/admin/cb_frame/settings/general-page.php:248
#: src/view/admin/uc_frame/network-settings-page.php:167
#: src/view/admin/uc_frame/settings/general-page.php:172
msgid "Hide cookie popup"
msgstr "Cookie-Popup ausblenden"

#: src/view/admin/cb_frame/network-settings-page.php:231
#: src/view/admin/cb_frame/settings/general-page.php:254
#: src/view/admin/uc_frame/network-settings-page.php:172
#: src/view/admin/uc_frame/settings/general-page.php:178
msgid ""
"This will remove the cookie consent banner from your website. The cookie "
"declaration shortcode will still be available if you are using Google Tag "
"Manager (or equal), you need to add the Cookiebot script in your Tag Manager."
msgstr ""
"Dadurch wird das Cookie-Consent-Banner von Ihrer Website entfernt. Der "
"Shortcode für die Cookie-Erklärung ist immer noch verfügbar, wenn Sie den "
"Google Tag Manager (oder ein gleichwertiges Programm) verwenden; Sie müssen "
"das Cookiebot-Skript in Ihrem Tag Manager hinzufügen."

#: src/view/admin/cb_frame/network-settings-page.php:246
#: src/view/admin/cb_frame/settings/general-page.php:280
#: src/view/admin/uc_frame/network-settings-page.php:187
#: src/view/admin/uc_frame/settings/general-page.php:204
msgid "Hide the cookie popup banner"
msgstr "Cookie-Popup-Banner ausblenden"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:11
#: src/view/admin/common/prior-consent/available-addons/tab-header.php:4
#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:14
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:14
msgid "Remember to save your changes before switching tabs"
msgstr ""
"Denken Sie daran, Ihre Änderungen zu speichern, bevor Sie die Registerkarte "
"wechseln."

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:20
msgid "Consent Level API Settings"
msgstr "API-Einstellungen für den Consent Level"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:29
msgid ""
"WP Consent Level API and Cookiebot™ categorize cookies a bit differently. "
"The default settings should fit most needs, but if you need to change the "
"mapping you can do so below."
msgstr ""
"WP Consent Level API und Cookiebot™ kategorisieren Cookies ein wenig anders. "
"Die Standardeinstellungen sollten den meisten Anforderungen genügen, aber "
"wenn Sie die Zuordnung ändern müssen, können Sie dies unten tun."

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:79
msgid "Cookiebot™ cookie categories"
msgstr "Cookiebot™ Cookie-Kategorien"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:96
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:51
msgid "WP Consent API cookies categories equivalent"
msgstr "Äquivalent zu den WP Consent API Cookie-Kategorien"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:102
msgid "Functional"
msgstr "Funktional"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:150
msgid "Statistics Anonymous"
msgstr "Anonyme Statistiken"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:179
msgid "Reset to default mapping"
msgstr "Standardzuordnung wiederherstellen"

#: src/view/admin/cb_frame/settings-page.php:48
msgid "Select at least one vendor on TCF tab"
msgstr "Wählen Sie im TCF-Tab mindestens einen Anbieter aus"

#: src/view/admin/cb_frame/settings-page.php:57
#: src/view/admin/uc_frame/settings-page.php:54
msgid "General Settings"
msgstr "Allgemeine Einstellungen"

#: src/view/admin/cb_frame/settings-page.php:61
#: src/view/admin/uc_frame/settings-page.php:58
msgid "Additional Settings"
msgstr "Zusätzliche Einstellungen"

#: src/view/admin/cb_frame/settings-page.php:65
#: src/view/admin/cb_frame/settings/gtm-page.php:27
#: src/view/admin/uc_frame/settings-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:12
#: src/view/admin/uc_frame/settings/gtm-page.php:27
msgid "Google Tag Manager"
msgstr "Google Tag Manager"

#: src/view/admin/cb_frame/settings-page.php:69
#: src/view/admin/cb_frame/settings/gcm-page.php:50
#: src/view/admin/uc_frame/settings-page.php:66
#: src/view/admin/uc_frame/settings/gcm-page.php:28
#: src/view/admin/uc_frame/settings/gcm-page.php:43
msgid "Google Consent Mode"
msgstr "Google Consent Mode"

#: src/view/admin/cb_frame/settings-page.php:73
#: src/view/admin/uc_frame/settings/embeddings-page.php:33
msgid "TCF"
msgstr "TCF"

#: src/view/admin/cb_frame/settings-page.php:77
msgid "Multiple Configurations"
msgstr "Mehrfach-Konfigurationen"

#: src/view/admin/cb_frame/settings/additional-page.php:37
#: src/view/admin/uc_frame/settings/additional-page.php:37
msgid "Show the banner while logged in"
msgstr "Banner während der Anmeldung anzeigen"

#: src/view/admin/cb_frame/settings/additional-page.php:40
#: src/view/admin/uc_frame/settings/additional-page.php:40
msgid ""
"You can choose to display the consent banner on your website while you’re "
"logged in and changing settings or customizing your banner."
msgstr ""
"Sie können das Consent-Banner auf Ihrer Website anzeigen lassen, während Sie "
"eingeloggt sind und Einstellungen ändern oder Ihr Banner anpassen."

#: src/view/admin/cb_frame/settings/additional-page.php:66
#: src/view/admin/uc_frame/settings/additional-page.php:66
msgid "Show banner on website when logged in"
msgstr "Banner während der Anmeldung auf Website anzeigen"

#: src/view/admin/cb_frame/settings/additional-page.php:75
msgid "Cookie declaration script tag:"
msgstr "Cookie-Erklärung Skript Tag"

#: src/view/admin/cb_frame/settings/additional-page.php:78
msgid ""
"If you implemented the declaration on your page through our widget in "
"WordPress, you can choose here how the script should be loaded."
msgstr ""
"Wenn Sie die Cookie-Erklärung auf Ihrer Seite mit unserem Widget in "
"WordPress implementiert haben, können Sie hier auswählen, wie das Skript "
"geladen werden soll."

#: src/view/admin/cb_frame/settings/additional-page.php:84
msgid "Select the cookie declaration script loading setting"
msgstr ""
"Wählen Sie die Einstellung für das Laden von Skripten für die Cookie-"
"Erklärung"

#: src/view/admin/cb_frame/settings/additional-page.php:116
#: src/view/admin/cb_frame/settings/general-page.php:187
#: src/view/admin/cb_frame/settings/general-page.php:239
#: src/view/admin/cb_frame/settings/general-page.php:283
#: src/view/admin/uc_frame/settings/general-page.php:153
#: src/view/admin/uc_frame/settings/general-page.php:207
msgid "Disabled by active setting in Network Settings"
msgstr "Deaktiviert durch aktive Einstellung in den Netzwerkeinstellungen"

#: src/view/admin/cb_frame/settings/additional-page.php:125
msgid "Ignore scripts in queue from Cookiebot CMP scan:"
msgstr "Ignorieren Sie anstehende Skripte vom Cookiebot CMP Scan:"

#: src/view/admin/cb_frame/settings/additional-page.php:128
msgid ""
"List scripts source URL (one per line) from the queue to ignore Cookiebot "
"CMP scan. Partial source URL will also work, e.g. wp-content/plugins/"
"woocommerce will block every WooCommerce script."
msgstr ""
"Listen Sie die Quell-URL der Skripte (eine pro Zeile) aus der Warteschlange "
"auf, um den Cookiebot CMP Scan zu ignorieren. Eine abgekürzte Quell-URL "
"funktioniert auch, z. B. wird die abgekürzte Quell-URL wp-content/plugins/"
"woocommerce jedes WooCommerce Skript blockieren."

#: src/view/admin/cb_frame/settings/additional-page.php:131
msgid ""
"This feature only works for scripts loaded via wp_enqueue_script. Manually "
"added scripts must be manually edited."
msgstr ""
"Dieses Feature funktioniert nur für Skripte, die über wp_enqueue_script "
"geladen werden. Manuell hinzugefügte Skripte müssen manuell bearbeitet werden"

#: src/view/admin/cb_frame/settings/additional-page.php:137
msgid "Script source URL:"
msgstr "Skript der Quell-URL:"

#: src/view/admin/cb_frame/settings/additional-page.php:143
msgid "Add script source URL, one per line"
msgstr "URL der Skriptquelle hinzufügen, eine pro Zeile"

#: src/view/admin/cb_frame/settings/gcm-page.php:14
msgid "What is Google Consent Mode and why should you enable it?"
msgstr "Was ist Google Consent Mode und warum sollten Sie es aktivieren?"

#: src/view/admin/cb_frame/settings/gcm-page.php:17
msgid ""
"Google Consent Mode is a way for your website to measure conversions and get "
"analytics insights while being fully GDPR-compliant when using services like "
"Google Analytics, Google Tag Manager (GTM) and Google Ads."
msgstr ""
"Mit Google Consent Mode kann Ihre Website Conversions messen und Analytics-"
"Insights gewinnen und dabei vollständig DSGVO-konform vorgehen, wenn Sie "
"Dienste wie Google Analytics, Google Tag Manager (GTM) oder Google Ads "
"nutzen."

#: src/view/admin/cb_frame/settings/gcm-page.php:20
msgid ""
"Cookiebot consent managment platform (CMP) and Google Consent Mode integrate "
"seamlessly to offer you plug-and-play compliance and streamlined use of all "
"Google's services in one easy solution."
msgstr ""
"Cookiebot Consent Management Platform (CMP) und Google Consent Mode lassen "
"sich nahtlos integrieren und bieten so eine datenschutzkonforme Plug-and-"
"Play-Lösung sowie eine rationalisierte Nutzung aller Google Dienste - alles "
"in einem!"

#: src/view/admin/cb_frame/settings/gcm-page.php:24
msgid "Read more about Cookiebot CMP and Google Consent Mode"
msgstr "Erfahren Sie mehr über Cookiebot CMP und Google Consent Mode"

#: src/view/admin/cb_frame/settings/gcm-page.php:31
msgid "Google Consent Mode:"
msgstr "Google Consent Mode:"

#: src/view/admin/cb_frame/settings/gcm-page.php:34
msgid ""
"Enable Google Consent Mode with default settings on your WordPress page."
msgstr ""
"Aktivieren Sie Google Consent Mode mit den Standardeinstellungen auf Ihrer "
"WordPress-Seite."

#: src/view/admin/cb_frame/settings/gcm-page.php:38
#: src/view/admin/cb_frame/settings/gcm-page.php:68
#: src/view/admin/cb_frame/settings/gtm-page.php:18
msgid "Read more"
msgstr "Weiter lesen"

#: src/view/admin/cb_frame/settings/gcm-page.php:61
msgid "URL passthrough:"
msgstr "URL passthrough:"

#: src/view/admin/cb_frame/settings/gcm-page.php:64
msgid ""
"This feature will allow you to pass data between pages when not able to use "
"cookies without/prior consent."
msgstr ""
"Diese Funktion ermöglicht es Ihnen, Daten zwischen Seiten zu übertragen, "
"wenn Cookies nicht ohne/vorherige Zustimmung verwendet werden können."

#: src/view/admin/cb_frame/settings/gcm-page.php:80
msgid "URL passthrough"
msgstr "URL passthrough"

#: src/view/admin/cb_frame/settings/gcm-page.php:89
msgid "Google Consent Mode cookies"
msgstr "Google Consent Mode-Cookie"

#: src/view/admin/cb_frame/settings/gcm-page.php:91
msgid ""
"Select the cookie types that need to be consented for the Google Consent "
"Mode script"
msgstr ""
"Wählen Sie die Cookie-Typen aus, für die das Google Consent Mode-Skript eine "
"Einwilligung benötigt"

#: src/view/admin/cb_frame/settings/gcm-page.php:93
#: src/view/admin/cb_frame/settings/general-page.php:212
#: src/view/admin/cb_frame/settings/general-page.php:251
#: src/view/admin/cb_frame/settings/gtm-page.php:80
#: src/view/admin/uc_frame/settings/general-page.php:175
msgid "This feature is only available when using Manual Blocking"
msgstr ""
"Dieses Feature ist nur verfügbar, wenn Sie manuelles Blocking verwenden"

#: src/view/admin/cb_frame/settings/gcm-page.php:95
#: src/view/admin/cb_frame/settings/gtm-page.php:82
msgid ""
"This option may affect the behaviour of your GTM Tags, as the script will "
"run on the selected cookies consent."
msgstr ""
"Diese Option kann sich auf das Verhalten Ihrer GTM-Tags auswirken, da das "
"Skript bei der ausgewählten Cookie-Einwilligung ausgeführt wird."

#: src/view/admin/cb_frame/settings/gcm-page.php:97
#: src/view/admin/cb_frame/settings/gtm-page.php:84
msgid ""
"Please make sure your Tags in Google Tag Manager are triggered correctly."
msgstr ""
"Bitte stellen Sie sicher, dass Ihre Tags in Google Tag Manager korrekt "
"ausgelöst werden."

#: src/view/admin/cb_frame/settings/gcm-page.php:102
#: src/view/admin/cb_frame/settings/gtm-page.php:89
#: src/view/admin/common/prior-consent/available-addons/tab.php:28
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:25
msgid "Check one or multiple cookie types:"
msgstr "Markieren Sie einen oder mehrere Cookie-Arten:"

#: src/view/admin/cb_frame/settings/general-page.php:30
msgid "Connect your Domain Group"
msgstr "Ihre Domain-Gruppe verbinden "

#: src/view/admin/cb_frame/settings/general-page.php:33
msgid ""
"To connect your Domain Group, paste your Domain Group ID here. If you want "
"to connect a second ID for other regions, you can do this under the "
"\"Multiple Configurations\" tab."
msgstr ""
"Um Ihre Domain Group zu verbinden, fügen Sie Ihre Domain Group ID hier ein. "
"Unter „Mehrfach-Konfigurationen“ können Sie eine zweite ID für andere "
"Regionen verbinden."

#: src/view/admin/cb_frame/settings/general-page.php:54
#: src/view/admin/uc_frame/settings/general-page.php:48
msgid "Using network account"
msgstr "Netzwerkkonto verwenden"

#: src/view/admin/cb_frame/settings/general-page.php:64
#: src/view/admin/uc_frame/settings/general-page.php:59
msgid "Do not use Network Settings ID"
msgstr "Nicht die Konto-ID der Netzwerkeinstellungen verwenden"

#: src/view/admin/cb_frame/settings/general-page.php:75
msgid "Language:"
msgstr "Sprache:"

#: src/view/admin/cb_frame/settings/general-page.php:77
msgid "Select your main language here."
msgstr "Wählen Sie Ihre Hauptsprache hier aus."

#: src/view/admin/cb_frame/settings/general-page.php:82
msgid "Select the language"
msgstr "Sprache auswählen"

#: src/view/admin/cb_frame/settings/general-page.php:86
msgid "Default (Autodetect)"
msgstr "Standard (automatische Erkennung)"

#: src/view/admin/cb_frame/settings/general-page.php:90
msgid "Use WordPress Language"
msgstr "WordPress Sprache verwenden"

#: src/view/admin/cb_frame/settings/general-page.php:111
msgid ""
"If enabled, Cookiebot™ will use the current location to set the banner and "
"cookie declaration language."
msgstr ""
"Wenn aktiviert, verwendet die Cookiebot CMP den aktuellen Standort, um die "
"Sprache des Banners und der Cookie-Erklärung festzulegen."

#: src/view/admin/cb_frame/settings/general-page.php:114
msgid ""
"Please make sure that all languages in use have been added in the Cookiebot™ "
"Manager."
msgstr ""
"Bitte stellen Sie sicher, dass alle verwendeten Sprachen auch im Cookiebot™ "
"Manager hinzugefügt wurden."

#: src/view/admin/cb_frame/settings/general-page.php:116
msgid "This feature disables the main language selector."
msgstr "Dieses Feature deaktiviert die allgemeine Sprachauswahl."

#: src/view/admin/cb_frame/settings/general-page.php:117
msgid ""
"If you have already set a language in the cookie declaration shortcode, this "
"feature will not change it."
msgstr ""
"Wenn Sie jedoch bereits eine Sprache im Shortcode der Cookie-Erklärung "
"eingestellt haben, wird dieses Feature die Sprache nicht ändern."

#: src/view/admin/cb_frame/settings/general-page.php:120
msgid "Read more on how to add languages"
msgstr "Erfahren Sie, wie Sie Sprachen hinzufügen können"

#: src/view/admin/cb_frame/settings/general-page.php:135
msgid "Use website location to set language"
msgstr "Website-Standort zum Festlegen der Sprache verwenden"

#: src/view/admin/cb_frame/settings/general-page.php:149
#: src/view/admin/uc_frame/network-settings-page.php:113
#: src/view/admin/uc_frame/settings/general-page.php:123
msgid ""
"Choose the type of your cookie-blocking mode. Select automatic to "
"automatically block all cookies except those strictly necessary to use "
"before user gives consent. Manual mode lets you adjust your cookie settings "
"within your website’s HTML."
msgstr ""
"Wählen Sie die Art Ihres Cookie-Blockierungsmodus. Wählen Sie „Automatisch“, "
"um automatisch alle Cookies zu blockieren, außer denjenigen, die für die "
"Verwendung unbedingt erforderlich sind, bevor der Nutzer seine Einwilligung "
"erteilt. Im manuellen Modus können Sie Ihre Cookie-Einstellungen im HTML-"
"Code Ihrer Website anpassen."

#: src/view/admin/cb_frame/settings/general-page.php:154
msgid "Guide to auto cookie-blocking"
msgstr "Anleitung zur automatischen Cookie-Blockierung"

#: src/view/admin/cb_frame/settings/general-page.php:160
msgid "Guide to manual cookie-blocking"
msgstr "Anleitung zur manuellen Cookie-Blockierung"

#: src/view/admin/cb_frame/settings/general-page.php:184
#: src/view/admin/uc_frame/settings/general-page.php:150
msgid "Manual"
msgstr "Manuell"

#: src/view/admin/cb_frame/settings/general-page.php:209
msgid "Add async or defer attribute to cookie declaration script tag"
msgstr ""
"Die Eigenschaft „async“ oder „defer“ zum Skript Tag der Cookie-Erklärung "
"hinzufügen"

#: src/view/admin/cb_frame/settings/gtm-page.php:12
msgid "Google Tag Manager:"
msgstr "Google Tag Manager:"

#: src/view/admin/cb_frame/settings/gtm-page.php:14
msgid "For more details about Cookiebot CMP and Google Tag Manager."
msgstr "Für weitere Informationen über Cookiebot CMP und Google Tag Manager."

#: src/view/admin/cb_frame/settings/gtm-page.php:35
#: src/view/admin/cb_frame/settings/gtm-page.php:46
#: src/view/admin/uc_frame/settings/gtm-page.php:35
#: src/view/admin/uc_frame/settings/gtm-page.php:46
msgid "Google Tag Manager ID"
msgstr "Google Tag Manager ID"

#: src/view/admin/cb_frame/settings/gtm-page.php:37
msgid "Paste your Tag Manager ID into the field on the right."
msgstr "Fügen Sie Ihre Tag Manager ID in das rechte Feld ein"

#: src/view/admin/cb_frame/settings/gtm-page.php:41
#: src/view/admin/uc_frame/settings/gtm-page.php:41
msgid "How to find the GTM ID"
msgstr "So finden Sie die GTM ID"

#: src/view/admin/cb_frame/settings/gtm-page.php:48
msgid "Enter GTM ID"
msgstr "GTM ID eingeben"

#: src/view/admin/cb_frame/settings/gtm-page.php:56
msgid "Data Layer Name (optional)"
msgstr "Data Layer Name (optional)"

#: src/view/admin/cb_frame/settings/gtm-page.php:58
msgid ""
"You can also paste your Data Layer Name here. This is optional information."
msgstr ""
"Sie können Ihren Data Layer Namen hier einfügen. Diese Angabe ist optional."

#: src/view/admin/cb_frame/settings/gtm-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:62
msgid "How to find the Data Layer Name"
msgstr "So finden Sie den Data Layer Namen"

#: src/view/admin/cb_frame/settings/gtm-page.php:67
#: src/view/admin/uc_frame/settings/gtm-page.php:67
msgid "Name of your Data Layer"
msgstr "Name Ihres Data Layers"

#: src/view/admin/cb_frame/settings/gtm-page.php:76
msgid "Google Tag Manager cookies"
msgstr "Google Tag Manager-Cookie"

#: src/view/admin/cb_frame/settings/gtm-page.php:78
msgid ""
"Select the cookie types that need to be consented for the Google Tag Manager "
"script"
msgstr ""
"Wählen Sie die Cookie-Typen aus, für die das Google Tag Manager-Skript eine "
"Einwilligung benötigt"

#: src/view/admin/cb_frame/settings/iab-page.php:26
msgid "IAB Integration:"
msgstr "IAB-Integration:"

#: src/view/admin/cb_frame/settings/iab-page.php:28
msgid ""
"If you want to use the IAB Framework TCF within your Consent Management "
"Platform (CMP) you can enable it on the right. Be aware that activating this "
"could override some of the configurations you made with the default setup "
"defined by the IAB."
msgstr ""
"Wenn Sie IAB Framwork TCF in Ihrer Consent Management Platform (CMP) nutzen "
"möchten, können Sie es rechts aktivieren. Beachten Sie, dass durch die "
"Aktivierung einige der Konfigurationen, die Sie mit der vom IAB definierten "
"Standardkonfiguration vorgenommen haben, außer Kraft gesetzt werden können."

#: src/view/admin/cb_frame/settings/iab-page.php:32
msgid "Read more on IAB with Cookiebot CMP here"
msgstr "Erfahren Sie hier mehr über IAB mit Cookiebot CMP"

#: src/view/admin/cb_frame/settings/iab-page.php:41
msgid "IAB TCF V2.2 integration"
msgstr "IAB TCF V2.2 integration"

#: src/view/admin/cb_frame/settings/iab-page.php:83
msgid ""
"IAB vendor list is temporarily offline. Please try refreshing the page after "
"a couple of minutes."
msgstr ""
"Die IAB Vendor-Liste ist vorübergehend offline. Bitte versuchen Sie, die "
"Seite nach ein paar Minuten wieder neu zu laden."

#: src/view/admin/cb_frame/settings/iab-page.php:85
msgid ""
"If you had previously saved configurations, don’t worry, they will continue "
"to work."
msgstr ""
"Wenn Sie zuvor Konfigurationen gespeichert hatten, können Sie diese "
"weiterhin verwenden."

#: src/view/admin/cb_frame/settings/iab-page.php:108
msgid "Sharing data with third-party vendors"
msgstr "Weitergabe von Daten an Drittanbieter"

#: src/view/admin/cb_frame/settings/iab-page.php:112
msgid ""
"Select vendors with whom you’ll share users’ data. We’ll include this "
"information on the second layer of your consent banner, where users "
"interested in more granular detail about who will access their data can view "
"it."
msgstr ""
"Wählen Sie Anbieter aus, an die Sie die Daten Ihrer Nutzer weitergeben "
"werden. Diese Informationen werden auf dem zweiten Layer Ihres Consent-"
"Banners angezeigt, wo Nutzer, die sich genauer darüber informieren möchten, "
"wer Zugriff auf ihre Daten erhält, dies nachsehen können."

#: src/view/admin/cb_frame/settings/iab-page.php:130
#: src/view/admin/cb_frame/settings/iab-page.php:162
#: src/view/admin/cb_frame/settings/iab-page.php:218
msgid "Search"
msgstr "Suchen"

#: src/view/admin/cb_frame/settings/iab-page.php:131
#: src/view/admin/cb_frame/settings/iab-page.php:163
msgid "Select All"
msgstr "Alle auswählen"

#: src/view/admin/cb_frame/settings/iab-page.php:132
#: src/view/admin/cb_frame/settings/iab-page.php:164
msgid "Deselect All"
msgstr "Auswahl aufheben"

#: src/view/admin/cb_frame/settings/iab-page.php:133
msgid "Select at least one vendor"
msgstr "Wählen Sie mindestens einen Anbieter aus"

#: src/view/admin/cb_frame/settings/iab-page.php:157
msgid "Google Ads certified external vendors"
msgstr "Von Google Ads zertifizierte externe Anbieter"

#: src/view/admin/cb_frame/settings/iab-page.php:188
msgid "Restrictions of data use purposes for vendors"
msgstr "Einschränkungen der Datennutzungszwecke für Anbieter"

#: src/view/admin/cb_frame/settings/iab-page.php:192
msgid ""
"Set restrictions on data use purposes for specific vendors. Add vendors and "
"the data use purposes that each vendor is allowed. We’ll share this "
"information with users within your consent banner."
msgstr ""
"Legen Sie Einschränkungen für die Datennutzungszwecke für bestimmte Anbieter "
"fest. Fügen Sie Anbieter und die Zwecke hinzu, für die der jeweilige "
"Anbieter die Daten nutzen darf. Wir geben diese Informationen in Ihrem "
"Consent-Banner an Ihre Nutzer weiter."

#: src/view/admin/cb_frame/settings/iab-page.php:198
msgid "Add Vendor"
msgstr "Anbieter hinzufügen"

#: src/view/admin/cb_frame/settings/iab-page.php:204
#: src/view/admin/cb_frame/settings/iab-page.php:215
msgid "Select Vendor"
msgstr "Anbieter auswählen"

#: src/view/admin/cb_frame/settings/iab-page.php:228
msgid "Set Purposes"
msgstr "Zwecke festlegen"

#: src/view/admin/cb_frame/settings/multiple-configuration/extra/region-item.php:18
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:75
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:101
msgid "Select region"
msgstr "Region auswählen"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:16
msgid "Additional configurations:"
msgstr "Zusätzliche Konfigurationen:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:18
msgid ""
"You can add a second alternative banner or configuration to your website by "
"creating a second Domain Group and specify it on a region."
msgstr ""
"Sie können ein zweites alternatives Banner oder eine zweite Konfiguration zu "
"Ihrer Website hinzufügen, indem Sie eine zweite Domain Group erstellen und "
"diese in einer Region angeben."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:22
msgid "Read more about multiple configurations here"
msgstr "Erfahren Sie hier mehr über Mehrfach-Konfigurationen "

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:34
msgid "Multiple configurations"
msgstr "Mehrfach-Konfigurationen"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:44
msgid "Set up your additional banner configuration:"
msgstr "Richten Sie Ihre zusätzliche Banner-Konfiguration ein:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:47
msgid ""
"To enable a different configuration, create a separate DomainGroup without "
"adding the domain to it and paste the ID below. Then select the countries in "
"which you want to show this configuration. For example, if your main Domain "
"Group is defined as a banner matching GDPR requirements, you might want to "
"add another Domain Group for visitors from California. The number of "
"additional configurations is restricted to one at the moment."
msgstr ""
"Um eine andere Konfiguration zu aktivieren, erstellen Sie eine separate "
"Domain Group, ohne die Domain hinzuzufügen, und fügen Sie die ID unten ein. "
"Wählen Sie dann die Länder aus, in denen Sie diese Konfiguration anzeigen "
"möchten. Wenn Ihre primäre Domain Group beispielsweise als ein DSGVO-"
"konformes Banner definiert ist, möchten Sie vielleicht eine weitere Domain "
"Group für Besucher aus Kalifornien hinzufügen. Die Anzahl der zusätzlichen "
"Konfigurationen ist im Moment auf eine beschränkt."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:57
msgid "Domain Group ID"
msgstr "Domain Group ID"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:61
msgid "Region"
msgstr "Region"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:71
msgid "Primary domain group"
msgstr "Primäre Domain Group"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:138
msgid "Add banner"
msgstr "Banner hinzufügen"

#: src/view/admin/cb_frame/support-page.php:23
msgid "Need help with your configuration?"
msgstr "Brauchen Sie Hilfe bei Ihrer Konfiguration?"

#: src/view/admin/cb_frame/support-page.php:26
msgid ""
"In our Help Center you find all the answers to your questions. If you have "
"additional questions, create a support request and our Support Team will "
"help out as soon as possible."
msgstr ""
"In unserem Hilfe-Center finden Sie alle Antworten auf Ihre Fragen. Wenn Sie "
"weitere Fragen haben, erstellen Sie eine Support-Anfrage und unser Support-"
"Team wird Ihnen dann so schnell wie möglich weiterhelfen."

#: src/view/admin/cb_frame/support-page.php:34
msgid "Visit Cookiebot CMP Help Center"
msgstr "Cookiebot CMP Hilfe-Center besuchen"

#: src/view/admin/cb_frame/support-page.php:58
msgid "How to find my Cookiebot™ ID"
msgstr "Wie finde ich meine Cookiebot™ ID"

#. translators: the first placeholder string will be replaced with a html anchor open tag and the second placeholder string will be replaced by the html anchor closing tag
#: src/view/admin/cb_frame/support-page.php:66
msgid "Log in to your %1$sCookiebot CMP account%2$s."
msgstr "Melden Sie sich in Ihrem %1$sCookiebot CMP-Konto%2$s an."

#. translators: the placeholder strings denote the positions of <b>, </b>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:76
msgid "Go to %1$s\"Settings\"%2$s and setup your Cookiebot CMP"
msgstr ""
"Gehen Sie zu %1$s„Einstellungen”%2$s und richten Sie Ihre Cookiebot CMP ein."

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:86
msgid "Go to the %1$s\"Your scripts\"%2$s tab"
msgstr "Gehen Sie dann auf die Registerkarte %1$s„Ihre Skripte”%2$s ."

#: src/view/admin/cb_frame/support-page.php:92
msgid ""
"Copy the value inside the data-cid parameter - eg.: abcdef12-3456-7890-abcd-"
"ef1234567890"
msgstr ""
"Kopieren Sie den Wert in den Parameter „data-cid”: z. B. abcdef12-3456-7890-"
"abcd-ef1234567890"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:97
msgid ""
"Add %1$s[cookie_declaration]%2$s shortcode to a page to show the declaration"
msgstr ""
"Fügen Sie den Shortcode %1$s[cookie_declaration]%2$s zu einer Seite hinzu, "
"um die Erklärung anzuzeigen."

#: src/view/admin/cb_frame/support-page.php:103
msgid "Remember to change your scripts as described below"
msgstr "Denken Sie daran, Ihre Skripte wie unten beschrieben zu ändern"

#: src/view/admin/cb_frame/support-page.php:110
msgid "Add the Cookie Declaration to your website"
msgstr "Fügen Sie die Cookie-Erklärung zu Ihrer Website hinzu"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:116
msgid ""
"Use the shortcode %1$s[cookie_declaration]%2$s to add the cookie declaration "
"to a page or post. The cookie declaration will always show the latest "
"version from Cookiebot CMP."
msgstr ""
"Verwenden Sie den Shortcode %1$s[cookie_declaration]%2$s, um die Cookie-"
"Erklärung zu einer Seite oder einem Beitrag hinzuzufügen. Die Cookie-"
"Erklärung zeigt immer die neueste Version von Cookiebot CMP."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:126
msgid ""
"If you want to show the cookie declaration in a specific language, you can "
"add the %1$s\"lang\"%2$s attribute, e.g. %3$s[cookie_declaration "
"lang=\"de\"]%4$s."
msgstr ""
"Wenn Sie die Cookie-Erklärung in einer bestimmten Sprache anzeigen möchten, "
"können Sie das Attribut %1$s„lang”%2$s hinzufügen, z. B. "
"%3$s[cookie_declaration lang=“de”]%4$s."

#: src/view/admin/cb_frame/support-page.php:138
msgid "Update your script tags"
msgstr "Aktualisieren Sie Ihre Skript Tags "

#: src/view/admin/cb_frame/support-page.php:142
msgid ""
"To enable prior consent, apply the attribute \"data-cookieconsent\" to "
"cookie-setting script tags on your website. Set the comma-separated value to "
"one or more of the cookie categories \"preferences\", \"statistics\" and/or "
"\"marketing\" in accordance with the types of cookies being set by each "
"script. Finally, change the attribute \"type\" from \"text/javascript\" to "
"\"text/plain\"."
msgstr ""
"Um eine vorherige Einwilligung zu ermöglichen, verwenden Sie das Attribut "
"„data-cookieconsent” für Cookie Skript Tags auf Ihrer Website. Setzen Sie "
"den durch Komma getrennten Wert auf eine oder mehrere der Cookie-Kategorien "
"„Präferenzen”, „Statistik” und/oder „Marketing”, je nachdem, welche Arten "
"von Cookies von jedem Skript gesetzt werden. Ändern Sie schließlich das "
"Attribut „type” von „text/javascript” in „text/plain”."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:152
msgid ""
"Example on modifying an existing Google Analytics Universal script tag can "
"be found %1$shere in step 4%2$s."
msgstr ""
"Ein Beispiel für die Änderung eines bestehenden Google Analytics Universal "
"Skript Tags finden Sie %1$shier in Schritt 4%2$s."

#: src/view/admin/cb_frame/support-page.php:172
msgid "Helper function to update your scripts"
msgstr "Hilfsfunktion zur Aktualisierung Ihrer Skripte"

#: src/view/admin/cb_frame/support-page.php:176
msgid ""
"You can update your scripts yourself. However, Cookiebot CMP also offers a "
"small helper function that can make the work easier."
msgstr ""
"Sie können Ihre Skripte selbst aktualisieren. Cookiebot CMP bietet jedoch "
"auch eine kleine Hilfsfunktion, die Ihnen die Arbeit erleichtern kann."

#: src/view/admin/cb_frame/support-page.php:183
msgid "Update your script tags this way:"
msgstr "Aktualisieren Sie Ihre Skript Tags wie folgt:"

#. translators: %1$s refers to the original script tag HTML, and %2$s refers to its replacement
#: src/view/admin/cb_frame/support-page.php:188
msgid "%1$s to %2$s"
msgstr "%1$s zu %2$s"

#: src/view/admin/common/dashboard-page.php:31
msgid "I already have an account"
msgstr "Ich habe bereits ein Konto"

#: src/view/admin/common/dashboard-page.php:35
msgid "Connect my account"
msgstr "Mein Konto verbinden"

#: src/view/admin/common/dashboard-page.php:48
#: src/view/admin/common/network-settings-page.php:33
#: src/view/admin/common/settings-page.php:37
msgid "New to our solutions? Create your account. "
msgstr "Neu bei uns? Erstellen Sie ein Konto."

#: src/view/admin/common/dashboard-page.php:65
msgid "How to get started"
msgstr "So starten Sie"

#: src/view/admin/common/dashboard-page.php:69
msgid "Learn more about your CMP"
msgstr "Mehr über Ihre CMP erfahren"

#: src/view/admin/common/network-settings-page.php:35
#: src/view/admin/common/settings-page.php:39
msgid ""
"If you’re new to our solutions, create an account first to obtain your "
"settings ID."
msgstr "Erstellen Sie ein Konto, um Ihre Konto-ID zu erhalten. "

#: src/view/admin/common/network-settings-page.php:40
#: src/view/admin/common/settings-page.php:44
msgid "Create your account"
msgstr "Ihr Konto erstellen"

#: src/view/admin/common/network-settings-page.php:49
#: src/view/admin/common/settings-page.php:52
#: src/view/admin/uc_frame/network-settings-page.php:51
msgid "Connect your account"
msgstr "Ihr Konto verbinden"

#: src/view/admin/common/network-settings-page.php:52
#: src/view/admin/common/settings-page.php:55
msgid "Enter the ID of your account to quickly connect it with the plugin."
msgstr "Geben Sie die ID Ihres Kontos ein, um es schnell mit dem Plugin zu verbinden."

#: src/view/admin/uc_frame/network-settings-page.php:54
msgid "Enter your settings ID to quickly link your account with the plugin."
msgstr "Konto-ID eingeben, um Sie direkt mit dem Plugin zu verbinden."

#: src/view/admin/common/network-settings-page.php:55
#: src/view/admin/uc_frame/network-settings-page.php:57
msgid ""
"If added this will be the default account for all subsites. Subsites are "
"able to override this and use their own account."
msgstr ""
"Wenn dieses Konto hinzugefügt wird, ist es das Standardkonto für alle "
"Unterseiten. Unterseiten können dies außer Kraft setzen und ihr eigenes "
"Konto verwenden."

#: src/view/admin/uc_frame/network-settings-page.php:61
#: src/view/admin/uc_frame/settings/general-page.php:29
msgid "Where to find settings ID"
msgstr "Wo finde ich die Konto-ID"

#: src/view/admin/common/network-settings-page.php:63
#: src/view/admin/common/settings-page.php:63
msgid "How to find your Usercentrics Settings ID"
msgstr "Wie man seine Usercentrics Settings-ID findet"

#: src/view/admin/common/network-settings-page.php:70
#: src/view/admin/common/settings-page.php:70
msgid "How to find your Cookiebot CMP Domain Group ID"
msgstr "Wie man seine Cookiebot CMP Domain Group-ID findet"

#: src/view/admin/common/network-settings-page.php:65
#: src/view/admin/common/settings-page.php:65
msgid "Settings ID or Domain Group ID"
msgstr "Settings-ID oder Domain Group-ID"

#: src/view/admin/uc_frame/network-settings-page.php:67
#: src/view/admin/uc_frame/settings/general-page.php:35
msgid "Your settings ID"
msgstr "Ihre Konto-ID"

#: src/view/admin/common/network-settings-page.php:82
#: src/view/admin/common/settings-page.php:82
#: src/view/admin/uc_frame/network-settings-page.php:83
#: src/view/admin/uc_frame/settings/general-page.php:70
msgid ""
"Let us know if your account is set for compliance with a single privacy law "
"(e.g. GDPR) or multiple laws (e.g. GDPR and CCPA) based on user’s location. "
"The default is a single privacy law, so this is likely your setting unless "
"modified."
msgstr ""
"Teilen Sie uns mit, ob Ihr Konto für die Einhaltung eines einzigen "
"Datenschutzgesetzes (z. B. DSGVO) oder mehrerer Gesetze (z. B. DSGVO und "
"CCPA) je nach Standort des Nutzers eingestellt ist. Die Standardeinstellung "
"ist ein einziges Datenschutzgesetz, also ist dies wahrscheinlich Ihre "
"Einstellung, sofern sie nicht geändert wurde."

#: src/view/admin/common/network-settings-page.php:88
#: src/view/admin/common/settings-page.php:88
#: src/view/admin/uc_frame/network-settings-page.php:89
#: src/view/admin/uc_frame/settings/general-page.php:76
msgid "Your current account setup:"
msgstr "Ihre aktuelle Kontoeinrichtung:"

#: src/view/admin/common/network-settings-page.php:95
#: src/view/admin/common/settings-page.php:95
#: src/view/admin/uc_frame/network-settings-page.php:96
#: src/view/admin/uc_frame/settings/general-page.php:83
msgid "Compliance with one privacy law"
msgstr "Konformität mit einem Datenschutzgesetz"

#: src/view/admin/common/network-settings-page.php:102
#: src/view/admin/common/settings-page.php:102
#: src/view/admin/uc_frame/network-settings-page.php:103
#: src/view/admin/uc_frame/settings/general-page.php:90
msgid "Compliance with multiple privacy laws (geolocation)"
msgstr "Konformität mit mehreren Datenschutzgesetzen (Geolokalisierung)"

#: src/view/admin/common/prior-consent/available-addons/tab.php:25
msgid "Plugin enabled"
msgstr "Plug-in aktiviert"

#: src/view/admin/common/prior-consent/available-addons/tab.php:58
msgid "Placeholder text:"
msgstr "Platzhalter-Text:"

#: src/view/admin/common/prior-consent/available-addons/tab.php:67
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:53
msgid "Display a placeholder"
msgstr "Platzhalter anzeigen"

#: src/view/admin/common/prior-consent/available-addons/tab.php:78
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:71
msgid "+ Add language"
msgstr "+ Sprache hinzufügen"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:7
msgid "Jetpack settings"
msgstr "Jetpack Einstellungen"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:8
msgid "Enable Jetpack on \"Available Addons\" to see this page options."
msgstr ""
"Aktivieren Sie Jetpack unter „Verfügbare Add-ons”, um die Optionen dieser "
"Seite zu sehen."

#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:15
msgid "Enable"
msgstr "Aktivieren"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:11
msgid "Show advanced options"
msgstr "Erweiterte Optionen anzeigen"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:17
msgid "This is for more advanced users."
msgstr "Dies ist für fortgeschrittene Benutzer"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:23
msgid "Regex:"
msgstr "Regex:"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:35
msgid "Edit regex"
msgstr "Regex bearbeiten"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:49
msgid "Reset to default regex"
msgstr "Auf Standard-Regex zurücksetzen"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:4
msgid "Information"
msgstr "Informationen"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:5
msgid ""
"These add-ons are created by a dedicated open-source community to make it "
"easier for you to manage cookie and tracker consent on your WordPress site. "
"They’re designed to help you ensure ‘prior consent’ even for plugins that "
"don’t include this feature."
msgstr ""
"Diese Add-ons wurden von einer engagierten Open-Source-Community entwickelt, "
"um Ihnen die Verwaltung der Cookie- und Tracker-Einwilligung auf Ihrer "
"WordPress-Website zu erleichtern. Sie wurden entwickelt, um Ihnen zu helfen, "
"die „vorherige Einwilligung“ auch für Plugins zu gewährleisten, die diese "
"Funktion nicht enthalten."

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:6
msgid ""
"Right now, these add-ons are the best way for you to signal user consent to "
"other plugins. While we don’t know if or when WordPress Core will add this "
"functionality, these tools are here to support you and work seamlessly with "
"Usercentrics solution."
msgstr ""
"Im Moment sind diese Add-ons die beste Möglichkeit für Sie, anderen Plugins "
"die Einwilligung der Nutzer zu signalisieren. Wir wissen zwar nicht, ob oder "
"wann WordPress Core diese Funktionalität hinzufügen wird, aber diese Tools "
"dienen zu Ihrer Unterstützung und arbeiten optimal mit der Usercentrics-"
"Lösung zusammen."

#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:10
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:19
#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:26
msgid "Language"
msgstr "Sprache"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:4
msgid "Unavailable plugins"
msgstr "Nicht verfügbare Plug-ins"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:5
msgid ""
"The following addons are unavailable. This is because the corresponding "
"plugin is not installed or activated."
msgstr ""
"Die folgenden Add-ons sind nicht verfügbar. Dies liegt daran, dass das "
"entsprechende Plug-in nicht installiert oder aktiviert ist."

#: src/view/admin/common/support-page.php:23
#: src/view/admin/uc_frame/support-page.php:23
msgid "Need help?"
msgstr "Brauchen Sie Hilfe?"

#: src/view/admin/common/support-page.php:26
#: src/view/admin/uc_frame/support-page.php:26
msgid ""
"Visit our Support Center to find answers to your questions or get help with "
"configuration. If you need further assistance, use the Contact Support "
"button in the top navigation to create a support request. We’ll respond as "
"soon as possible."
msgstr ""
"Besuchen Sie unser Support Center, um Antworten auf Ihre Fragen zu finden "
"oder Hilfe bei der Konfiguration zu erhalten. Wenn Sie weitere Unterstützung "
"benötigen, verwenden Sie die Schaltfläche „Support kontaktieren“ in der "
"oberen Navigation, um eine Support-Anfrage zu erstellen. Wir werden Ihnen so "
"schnell wie möglich antworten."

#: src/view/admin/common/support-page.php:34
#: src/view/admin/uc_frame/support-page.php:34
msgid "Go to Support Center"
msgstr "Zum Support Center"

#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:6
msgid "You will need to add a new ID before updating other settings"
msgstr ""
"Sie müssen eine neue ID hinzufügen, bevor Sie andere Einstellungen "
"aktualisieren können."

#: src/view/admin/common/templates/extra/review-form.php:10
msgid "Cookiebot CMP Deactivation"
msgstr "Cookiebot CMP Deaktivierung"

#: src/view/admin/common/templates/extra/review-form.php:14
msgid "We are sad to lose you. Take a moment to help us improve?"
msgstr "Schade, dass Sie uns verlassen. Haben Sie Feedback für uns?"

#: src/view/admin/common/templates/extra/review-form.php:19
msgid "The installation is too complicated"
msgstr "Die Installation ist zu kompliziert"

#: src/view/admin/common/templates/extra/review-form.php:26
msgid "I found a plugin that better serves my needs"
msgstr ""
"Ich habe ein Plugin gefunden, dass meinen Bedürfnissen besser entspricht. "

#: src/view/admin/common/templates/extra/review-form.php:33
msgid "Missing features / did not meet my expectations"
msgstr "Fehlende Funktion / hat meine Erwartungen nicht erfüllt"

#: src/view/admin/common/templates/extra/review-form.php:40
msgid "I need more customization options"
msgstr "Ich brauche mehr Anpassungsmöglichkeiten"

#: src/view/admin/common/templates/extra/review-form.php:47
msgid "The premium plan is too expensive"
msgstr "Das Premium-Paket ist zu teuer"

#: src/view/admin/common/templates/extra/review-form.php:54
msgid "I’m only deactivating the plugin temporarily"
msgstr "Die Deaktivierung des Plugins ist nur temporär"

#: src/view/admin/common/templates/extra/review-form.php:61
msgid "Other"
msgstr "Sonstiges"

#: src/view/admin/common/templates/extra/review-form.php:65
msgid "Please specify here"
msgstr "Bitte hier angeben"

#: src/lib/Cookiebot_Review.php:105
msgid "(Optional)"
msgstr "(Optional)"

#: src/lib/Cookiebot_Review.php:106
msgid " By checking this box, you agree to submit troubleshooting information and allow us to contact you regarding the problem if necessary."
msgstr " Wenn Sie dieses Kästchen ankreuzen, erklären Sie sich damit einverstanden, uns Informationen zur Fehlerbehebung zu übermitteln und erlauben uns, Sie bei Bedarf zu kontaktieren, um das Problem zu lösen."

#: src/lib/Cookiebot_Review.php:110
msgid "The information will be kept for no longer than 90 days. You may revoke this consent at any time, e.g. by sending an email to "
msgstr "Die Informationen werden nicht länger als 90 Tage gespeichert. Sie können diese Einwilligung jederzeit widerrufen, z. B. durch eine E-Mail an "

#: src/lib/Cookiebot_Review.php:117
msgid "Skip and Deactivate"
msgstr "Überspringen und Deaktivieren"

#: src/lib/Cookiebot_Review.php:118
msgid "Submit and Deactivate"
msgstr "Einreichen und Deaktivieren"

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:6
msgid ""
"If there is a network settings ID connected it will be used for this subsite, "
"if not you will need to add a new ID before updating other settings"
msgstr ""
"Wenn eine Netzwerk-Konto-ID verbunden ist, wird sie für diese Unterseite "
"verwendet. Wenn nicht, müssen Sie eine neue ID hinzufügen, bevor Sie andere "
"Einstellungen aktualisieren."

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:13
msgid "Disconnect subsite account"
msgstr "Unterseiten-Konto abmelden"

#: src/view/admin/common/templates/header.php:10
msgid "Changes has been saved"
msgstr "Änderungen gespeichert"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:13
msgid "Absolutely, you deserve it!"
msgstr "Auf jeden Fall, Sie haben es verdient."

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:24
msgid "Maybe later?"
msgstr "Vielleicht später?"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:35
msgid "I`ve already left my feedback"
msgstr "Ich habe mein Feedback bereits abgegeben."

#: src/view/admin/uc_frame/dashboard-page.php:30
msgid "Welcome to Usercentrics Cookiebot WordPress Plugin"
msgstr "Willkommen beim Usercentrics Cookiebot WordPress Plugin"

#: src/view/admin/uc_frame/dashboard-page.php:38
msgid ""
"You’ve added your settings ID to your Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Sie haben Ihre Konto-ID zu Ihrem Usercentrics Cookiebot WordPress Plugin "
"hinzugefügt."

#: src/view/admin/uc_frame/dashboard-page.php:51
msgid ""
"Are you happy with Usercentrics Cookiebot WordPress Plugin? Your feedback "
"helps us improve it."
msgstr ""
"Sind Sie zufrieden mit dem Usercentrics Cookiebot WordPress Plugin? Ihr "
"Feedback hilft uns, uns zu verbessern."

#: src/view/admin/uc_frame/dashboard-page.php:55
msgid "Share feedback"
msgstr "Feedback teilen"

#: src/view/admin/uc_frame/dashboard-page.php:68
msgid "How to set up Usercentrics Cookiebot WordPress Plugin"
msgstr "So richten Sie das Usercentrics Cookiebot WordPress Plugin ein"

#: src/view/admin/uc_frame/dashboard-page.php:72
#: src/view/admin/uc_frame/settings/gcm-page.php:21
#: src/view/admin/uc_frame/settings/general-page.php:127
#: src/view/admin/uc_frame/settings/gtm-page.php:18
msgid "Learn more"
msgstr "Mehr erfahren"

#: src/view/admin/uc_frame/dashboard-page.php:115
msgid "Visit our blog to learn about other legislations"
msgstr ""
"Besuchen Sie unseren Blog, um mehr über andere Rechtsvorschriften zu erfahren"

#: src/view/admin/uc_frame/debug-page.php:22
msgid "Debug your plugin"
msgstr "Bugs aus Ihrem Plugin entfernen"

#: src/view/admin/uc_frame/debug-page.php:25
msgid ""
"If you encounter any issues with your Usercentrics Cookiebot WordPress "
"Plugin, provide the information below to help us assist you. Visit our "
"Support Center and send us a copy of what is displayed in the window below."
msgstr ""
"Wenn Sie Probleme mit Ihrem Usercentrics Cookiebot WordPress Plugin haben, "
"geben Sie uns bitte die unten stehenden Informationen, damit wir Ihnen "
"helfen können. Besuchen Sie unser Support Center und senden Sie uns eine "
"Kopie dessen, was in dem Fenster unten angezeigt wird."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:23
msgid "WP Consent API Settings"
msgstr "WP Consent API-Einstellungen"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:32
msgid ""
"WP Consent API and Usercentrics Cookiebot WordPress Plugin categorize "
"cookies a bit differently. The default settings should fit most needs, but "
"if you need to change the mapping you can do so below."
msgstr ""
"WP Consent API und Usercentrics Cookiebot WordPress Plugin kategorisieren "
"Cookies ein wenig anders. Die Standardeinstellungen sollten den meisten "
"Anforderungen genügen, aber wenn Sie die Zuordnung ändern müssen, können Sie "
"dies unten tun."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:48
msgid "Usercentrics Cookiebot cookie categories"
msgstr "Usercentrics Cookiebot Cookie-Kategorien"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:58
msgid "essential"
msgstr "Essentiell"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:72
msgid "functional"
msgstr "Funktional"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:106
msgid "Reset to default categories"
msgstr "Zurücksetzen auf Standardkategorien"

#: src/view/admin/uc_frame/settings-page.php:48
msgid "Save changes"
msgstr "Änderungen speichern"

#: src/view/admin/uc_frame/settings-page.php:70
#: src/view/admin/uc_frame/settings/embeddings-page.php:4
msgid "Privacy Policy Sync"
msgstr "Synchronisation der Datenschutzerklärung"

#: src/view/admin/uc_frame/settings/embeddings-page.php:7
msgid ""
"Use our pre-defined, automatically generated embeddings to help you keep "
"your Privacy Policy page in sync with your consent banner settings. This "
"feature saves you time by automatically updating legally required "
"information, so you don’t need to manually copy data into your Privacy "
"Policy page. Once you’re done setting the options below, simply copy the "
"code and paste it into your Privacy Policy page."
msgstr ""
"Nutzen Sie unsere vordefinierten, automatisch generierten Einbettungen, um "
"die Seite mit Ihrer Datenschutzerklärung mit Ihren Einstellungen für das "
"Consent-Banner synchron zu halten. Diese Funktion spart Ihnen Zeit, da die "
"gesetzlich vorgeschriebenen Informationen automatisch aktualisiert werden, "
"sodass Sie die Daten nicht manuell in Ihre Datenschutzerklärung kopieren "
"müssen. Sobald Sie die unten stehenden Optionen eingestellt haben, kopieren "
"Sie einfach den Code und fügen ihn in Ihre Seite mit der Erklärung ein."

#: src/view/admin/uc_frame/settings/embeddings-page.php:12
msgid "Copy shortcode"
msgstr "Shortcode kopieren"

#: src/view/admin/uc_frame/settings/embeddings-page.php:23
msgid "Sync options for privacy legislations"
msgstr "Synchronisierungsoptionen für Datenschutzgesetze"

#: src/view/admin/uc_frame/settings/embeddings-page.php:26
msgid ""
"Select the legislation you want to automatically sync with your Privacy "
"Policy page."
msgstr ""
"Wählen Sie die Rechtsvorschriften aus, die Sie automatisch mit Ihrer "
"Datenschutzerklärung synchronisieren möchten."

#: src/view/admin/uc_frame/settings/embeddings-page.php:41
msgid "Sync options for data processing services (DPS) "
msgstr "Synchronisierungsoptionen für Data Processing Services (DPS)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:44
msgid ""
"Define what to include on your Privacy Policy page: DPS categories only, "
"categories with their services, a single service, or detailed information on "
"both categories and services. Choose based on the level of detail you want "
"to display."
msgstr ""
"Legen Sie fest, was in Ihrer Datenschutzerklärung enthalten sein soll: Nur "
"DPS-Kategorien, Kategorien mit ihren Services, ein einzelner Service oder "
"detaillierte Informationen zu Kategorien und Services. Wählen Sie das Detail-"
"Level, das Sie anzeigen möchten."

#: src/view/admin/uc_frame/settings/embeddings-page.php:50
msgid "Services (Default)"
msgstr "Services (Standard)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:51
msgid "Categories and services"
msgstr "Kategorien und Services "

#: src/view/admin/uc_frame/settings/embeddings-page.php:52
msgid "Categories only"
msgstr "Nur Kategorien"

#: src/view/admin/uc_frame/settings/embeddings-page.php:53
msgid "Single service"
msgstr "Einzelner Service"

#: src/view/admin/uc_frame/settings/embeddings-page.php:54
msgid "Purposes"
msgstr "Verwendungszwecke"

#: src/view/admin/uc_frame/settings/embeddings-page.php:55
msgid "Vendors"
msgstr "Anbieter"

#: src/view/admin/uc_frame/settings/embeddings-page.php:63
msgid "Single Service ID"
msgstr "Einzelne Service-ID"

#: src/view/admin/uc_frame/settings/embeddings-page.php:66
msgid "Add the service ID that you want to display."
msgstr "Fügen Sie die Service-ID hinzu, die Sie anzeigen wollen."

#: src/view/admin/uc_frame/settings/embeddings-page.php:69
msgid "This feature is required."
msgstr "Diese Funktion ist erforderlich."

#: src/view/admin/uc_frame/settings/embeddings-page.php:81
msgid "Privacy toggles"
msgstr "Datenschutz-Toggles"

#: src/view/admin/uc_frame/settings/embeddings-page.php:84
msgid ""
"Define whether you want the privacy toggles to be enabled and displayed on "
"your Privacy Policy page."
msgstr ""
"Legen Sie fest, ob die Toggles für den Datenschutz aktiviert und auf der "
"Seite Ihrer Datenschutzerklärung angezeigt werden sollen."

#: src/view/admin/uc_frame/settings/embeddings-page.php:92
msgid "Enable privacy toggles"
msgstr "Datenschutz-Toggles aktivieren"

#: src/view/admin/uc_frame/settings/gcm-page.php:14
msgid "Integration with Google Consent Mode"
msgstr "Integration in dem Google Consent Mode"

#: src/view/admin/uc_frame/settings/gcm-page.php:17
msgid ""
"The Usercentrics Cookiebot WordPress Plugin and Google Consent Mode "
"integrate seamlessly, providing plug-and-play privacy compliance and "
"effortless use of all Google services in one solution."
msgstr ""
"Das Usercentrics Cookiebot WordPress Plugin und der Google Consent Mode "
"lassen sich nahtlos integrieren und bieten Plug-and-Play "
"Datenschutzkonformität und mühelose Nutzung aller Google-Dienste in einer "
"Lösung."

#: src/view/admin/uc_frame/settings/gcm-page.php:31
msgid ""
"Enable Google Consent Mode integration within your Usercentrics Cookiebot "
"WordPress Plugin."
msgstr ""
"Aktivieren Sie die Integration des Google Consent Mode in Ihrem Usercentrics "
"Cookiebot WordPress Plugin."

#: src/view/admin/uc_frame/settings/general-page.php:22
msgid "Settings ID"
msgstr "Konto-ID"

#: src/view/admin/uc_frame/settings/general-page.php:25
msgid ""
"To disconnect your account, enter your settings ID into the field and confirm "
"with the button."
msgstr ""
"Um Ihr Konto abzumelden, geben Sie Ihre Konto-ID in das Feld ein und "
"bestätigen Sie mit der Schaltfläche."

#: src/view/admin/uc_frame/settings/general-page.php:99
msgid "TCF integration"
msgstr "TCF Integration"

#: src/view/admin/uc_frame/settings/general-page.php:102
msgid "Enable the integration with the latest version of IAB TCF."
msgstr "Aktivieren Sie die Integration mit der neuesten IAB TCF Version."

#: src/view/admin/uc_frame/settings/general-page.php:111
msgid "IAB TCF integration"
msgstr "IAB TCF Integration"

#: src/view/admin/uc_frame/settings/gtm-page.php:14
msgid ""
"Enable Google Tag Manager integration to streamline tracking tags with your "
"Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Aktivieren Sie die Integration des Google Tag Manager, um Tracking-Tags mit "
"Ihrem Usercentrics Cookiebot WordPress Plugin zu optimieren."

#: src/view/admin/uc_frame/settings/gtm-page.php:37
msgid "Enter your Google Tag Manager ID for seamless integration."
msgstr ""
"Geben Sie Ihre Google Tag Manager ID für eine optimale Integration ein."

#: src/view/admin/uc_frame/settings/gtm-page.php:48
msgid "GTM-XXXXXXX"
msgstr "GTM-XXXXXXX"

#: src/view/admin/uc_frame/settings/gtm-page.php:56
msgid "Data layer name (only if changed)"
msgstr "Name der Datenebene (nur bei Änderung)"

#: src/view/admin/uc_frame/settings/gtm-page.php:58
msgid ""
"The default name for the data layer in Google Tag Manager is ‘dataLayer’. If "
"you renamed it, enter the new name. Otherwise, leave this field empty."
msgstr ""
"Der Standardname für die Datenebene im Google Tag Manager lautet "
"„dataLayer“. Wenn Sie ihn umbenannt haben, geben Sie den neuen Namen ein. "
"Andernfalls lassen Sie dieses Feld leer."

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:14
msgid "Title"
msgstr "Titel"

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:33
msgid "- Default -"
msgstr "- Standard -"

#: src/widgets/Cookiebot_Declaration_Widget.php:17
msgid "Cookiebot - Cookie Declaration"
msgstr "Cookiebot - Cookie-Erklärung"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:23
msgid "Cookiebot Status"
msgstr "Cookiebot Status"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:39
msgid "You need to enter your Cookiebot ID."
msgstr "Sie müssen Ihre Cookiebot-ID eingeben."

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:41
msgid "Update your Cookiebot ID"
msgstr "Aktualisieren Sie Ihre Cookiebot-ID"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:44
msgid "Your Cookiebot is working!"
msgstr "Ihr Cookiebot funktioniert!"

#~ msgid ""
#~ "We hope you enjoy using WordPress Cookiebot! Would you consider leaving "
#~ "us a review on WordPress.org?"
#~ msgstr ""
#~ "Wir wünschen Ihnen viel Spaß mit WordPress Cookiebot! Würden Sie uns "
#~ "vielleicht eine Bewertung auf WordPress.org hinterlassen?"

#~ msgid "Legislations"
#~ msgstr "Rechtsvorschriften"

#~ msgid "Sure! I'd love to!"
#~ msgstr "Klar! Sehr gerne!"

#~ msgid "I've already left a review"
#~ msgstr "Ich habe bereits eine Bewertung hinterlassen"

#~ msgid "Maybe Later"
#~ msgstr "Vielleicht später"

#~ msgid "Never show again"
#~ msgstr "Nicht mehr anzeigen"

#~ msgid "TCF version:"
#~ msgstr "TCF-Version"

#~ msgid ""
#~ "In May 2023 The Interactive Advertising Bureau (IAB) announced the latest "
#~ "version of its Transparency and Consent Framework (TCF), or TCF v2.2, "
#~ "which must be implemented by all consent management platforms (CMPs) by "
#~ "November 20, 2023. We will migrate you automatically on November 20,2023, "
#~ "but we recommend to do it manually before. To manually switch the version "
#~ "before please select it on the right."
#~ msgstr ""
#~ "Im Mai 2023 kündigte das Interactive Advertising Bureau (IAB) die neueste "
#~ "Version seines Transparency and Consent Frameworks (TCF), oder TCF v2.2, "
#~ "an, die von allen Consent Management Platforms (CMPs) bis zum 20. "
#~ "November 2023 eingeführt werden muss. Wir werden automatisch eine "
#~ "Umstellung zum 20. November 2023 für Sie vornehmen, aber wir empfehlen "
#~ "Ihnen, dies vorher manuell zu tun. Wählen Sie bitte die Option auf der "
#~ "rechten Seite aus, um manuell eine Umstellung auf die neue Version "
#~ "vorzunehmen."

#~ msgid "Select the TCF Version below"
#~ msgstr "Wählen Sie unten die TCF-Version aus"

#~ msgid "New"
#~ msgstr "Neu"

#~ msgid "Create a new Account"
#~ msgstr "Neues Konto erstellen"

#~ msgid "Get help with connecting your account"
#~ msgstr "Hilfe bei der Verbindung Ihres Kontos erhalten"

#~ msgid "Select the Cookie-blocking mode"
#~ msgstr "Cookie-Blockierungsmodus auswählen"

#~ msgid "Automatic cookie-blocking mode"
#~ msgstr "Automatischer Cookie-Blockierungsmodus"

#~ msgid "Manual cookie-blocking mode"
#~ msgstr "Manueller Cookie-Blockierungsmodus"

#~ msgid "Depending on Cookie-blocking mode"
#~ msgstr "Abhängig vom Cookie-Blockierungsmodus"

#~ msgid "Auto-update Cookiebot™ Plugin:"
#~ msgstr "Cookiebot™-Plugin automatisch aktualisieren:"

#~ msgid ""
#~ "Automatically update your Cookiebot™ plugin when new releases becomes "
#~ "available."
#~ msgstr ""
#~ "Aktualisieren Sie Ihr Cookiebot™-Plugin automatisch, wenn neue Versionen "
#~ "verfügbar sind."

#~ msgid ""
#~ "These add-ons are produced by an open-source community of developers. "
#~ "This is done to help make it easier for WordPress users to implement "
#~ "‘prior consent’ for cookies and trackers set by plugins that do not offer "
#~ "this as a built-in function. The add-ons are currently the best "
#~ "alternative to a WordPress Core framework that can signal the user’s "
#~ "consent state to other plugins (if and when this will be implemented is "
#~ "unknown) and to those plugins that do not yet offer native support for "
#~ "Cookiebot CMP. "
#~ msgstr ""
#~ "Diese Add-ons werden von einer Open-Source-Community von Entwicklern "
#~ "erstellt. Damit soll es für WordPress-Nutzer einfacher werden, eine "
#~ "„vorherige Einwilligung“ für Cookies und Tracking-Technologien zu "
#~ "implementieren, die von Plug-ins gesetzt werden, die dies nicht als "
#~ "integrierte Funktion anbieten. Die Add-ons sind derzeit die beste "
#~ "Alternative zu einem WordPress-Core-Framework, das anderen Plug-ins (ob "
#~ "und wann dies implementiert wird, ist unbekannt) und den Plug-ins, die "
#~ "noch keine native Unterstützung für Cookiebot CMP bieten, den "
#~ "Einwilligungsstatus des Nutzers signalisieren kann."

#~ msgid "Do you not have an account yet?"
#~ msgstr "Haben Sie noch kein Konto?"

#~ msgid ""
#~ "Before you can get started with Cookiebot CMP for WordPress, you need to "
#~ "create an account on our website by clicking on \"Create a new account\" "
#~ "below. After you have signed up, you can configure your banner in the "
#~ "Cookiebot Manager and then place the Cookiebot Domain Group ID in the "
#~ "designated field below. You can find your ID in the Cookiebot Manager by "
#~ "navigating to \"Settings\" and \"Your Scripts\"."
#~ msgstr ""
#~ "Bevor Sie mit Cookiebot CMP für WordPress starten können, müssen Sie ein "
#~ "Konto auf unserer Website erstellen, indem Sie unten auf „Neues Konto "
#~ "erstellen“ klicken. Nachdem Sie sich angemeldet haben, können Sie Ihr "
#~ "Banner im Cookiebot Manager konfigurieren und dann die Cookiebot Domain "
#~ "Group ID in das dafür vorgesehene Feld eintragen. Sie finden Ihre ID im "
#~ "Cookiebot Manager, indem Sie auf „Einstellungen“ und anschließend auf "
#~ "„Ihre Skripte“ klicken."

#~ msgid "Depending on cookie-blocking mode"
#~ msgstr "Abhängig vom Cookie-Blockierungsmodus"

#~ msgid "Cookiebot CMP in WP Admin:"
#~ msgstr "Cookiebot CMP in WP Admin:"

#~ msgid ""
#~ "This checkbox will disable Cookiebot CMP to act within the WordPress "
#~ "Admin area"
#~ msgstr ""
#~ "Dieses Kontrollkästchen deaktiviert Cookiebot CMP, um im WordPress-Admin-"
#~ "Bereich zu agieren"

#~ msgid "Disable Cookiebot CMP in the WordPress Admin area"
#~ msgstr "Cookiebot CMP im WordPress-Admin-Bereich deaktivieren"

#~ msgid "Cookiebot CMP on front-end while logged in:"
#~ msgstr "Während Sie angemeldet sind, ist Cookiebot CMP auf dem Frontend:"

#~ msgid ""
#~ "This setting will enable Cookiebot CMP on the front-end while you're "
#~ "logged in."
#~ msgstr ""
#~ "Mit dieser Einstellung wird Cookiebot CMP auf dem Frontend aktiviert, "
#~ "während Sie angemeldet sind."

#~ msgid "Render Cookiebot CMP on front-end while logged in"
#~ msgstr ""
#~ "Cookiebot CMP auf dem Frontend aktivieren, während Sie angemeldet sind"

#~ msgid "Watch video demonstration"
#~ msgstr "Video-Demo ansehen"
