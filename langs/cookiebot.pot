# Copyright (C) 2024 Usercentrics A/S
# This file is distributed under the same license as the Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics plugin.
msgid ""
msgstr ""
"Project-Id-Version: Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics 4.4.0\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/cookiebot\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"POT-Creation-Date: 2024-12-18T18:13:49+00:00\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"X-Generator: WP-CLI 2.10.0\n"
"X-Domain: cookiebot\n"

#. Plugin Name of the plugin
#: cookiebot.php
msgid "Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: cookiebot.php
msgid "https://www.cookiebot.com/"
msgstr ""

#. Description of the plugin
#: cookiebot.php
msgid "The Cookiebot CMP WordPress cookie banner and cookie policy help you comply with the major data protection laws (GDPR, ePrivacy, CCPA, LGPD, etc.) in a simple and fully automated way. Secure your website and get peace of mind."
msgstr ""

#. Author of the plugin
#: cookiebot.php
msgid "Usercentrics A/S"
msgstr ""

#: src/addons/config/Settings_Config.php:89
#: src/addons/config/Settings_Config.php:90
#: src/view/admin/common/prior-consent/page.php:24
#: src/view/admin/common/templates/main-tabs.php:39
msgid "Plugins"
msgstr ""

#: src/addons/config/Settings_Config.php:130
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:25
msgid "Remove language"
msgstr ""

#: src/addons/config/Settings_Config.php:602
msgid "The plugin is not installed."
msgstr ""

#: src/addons/config/Settings_Config.php:605
msgid "The theme is not installed."
msgstr ""

#: src/addons/config/Settings_Config.php:609
msgid "The plugin is not activated."
msgstr ""

#: src/addons/config/Settings_Config.php:612
msgid "The theme is not activated."
msgstr ""

#: src/addons/config/Settings_Config.php:672
#: src/lib/Cookiebot_Admin_Links.php:54
#: src/lib/Cookiebot_Admin_Links.php:56
#: src/lib/Cookiebot_Admin_Links.php:124
msgid "%s"
msgstr ""

#: src/addons/config/Settings_Config.php:708
#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:18
#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:19
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:40
msgid "Info"
msgstr ""

#: src/addons/config/Settings_Config.php:715
msgid "Available Add-ons"
msgstr ""

#: src/addons/config/Settings_Config.php:721
msgid "Unavailable Add-ons"
msgstr ""

#: src/addons/config/Settings_Config.php:738
msgid "Jetpack"
msgstr ""

#: src/addons/config/Settings_Config.php:749
msgid "WP Consent API"
msgstr ""

#: src/addons/controller/addons/add_to_any/Add_To_Any.php:86
#: src/addons/controller/addons/embed_autocorrect/Embed_Autocorrect.php:443
msgid "Blocks embedded videos from Youtube, Twitter, Vimeo and Facebook."
msgstr ""

#: src/addons/controller/addons/enfold/Enfold.php:29
msgid "Blocks cookies created by Enfold theme's Google Services."
msgstr ""

#: src/addons/controller/addons/enhanced_ecommerce_for_woocommerce_store/Enhanced_Ecommerce_For_WooCommerce_Store.php:43
msgid "Blocks enhanced e-commerce for WooCommerce store"
msgstr ""

#: src/addons/controller/addons/google_analytics/Google_Analytics.php:36
msgid "Google Analytics is used to track how visitor interact with website content."
msgstr ""

#: src/addons/controller/addons/google_analytics_plus/Google_Analytics_Plus.php:32
msgid "Google Analytics is a simple, easy-to-use tool that helps website owners measure how users interact with website content"
msgstr ""

#: src/addons/controller/addons/google_site_kit/Google_Site_Kit.php:33
msgid "Blocks Google Analytics scripts"
msgstr ""

#: src/addons/controller/addons/jetpack/widget/Facebook_Jetpack_Widget.php:71
msgid "Facebook widget."
msgstr ""

#: src/addons/controller/addons/litespeed_cache/Litespeed_Cache.php:51
msgid "Excludes cookiebot javascript files when the Litespeed Cache deter option is enabled."
msgstr ""

#: src/addons/controller/addons/official_facebook_pixel/Official_Facebook_Pixel.php:247
msgid "Blocks Official Meta Pixel scripts"
msgstr ""

#: src/addons/controller/addons/optinmonster/Optinmonster.php:32
msgid "OptinMonster API plugin to connect your WordPress site to your OptinMonster account."
msgstr ""

#: src/addons/controller/addons/simple_share_buttons_adder/Simple_Share_Buttons_Adder.php:32
msgid "Blocks Simple Share Buttons Adder."
msgstr ""

#: src/addons/controller/addons/wpforms/Wpforms.php:61
msgid "If the user gives correct consent, IP and Unique User ID will be saved on form submissions, otherwise not."
msgstr ""

#: src/addons/controller/addons/wpforms/Wpforms.php:62
msgid "Increases opt-in rate compared to WPForms \"GDPR mode\"."
msgstr ""

#: src/addons/controller/addons/wp_google_analytics_events/Wp_Google_Analytics_Events.php:46
msgid "The plugin allows you to fire events whenever someone interacts or views elements on your website."
msgstr ""

#: src/addons/controller/addons/wp_rocket/Wp_Rocket.php:48
msgid "Excludes cookiebot javascript files when the WP-Rocket deter option is enabled."
msgstr ""

#: src/addons/controller/addons/wp_seopress/Wp_Seopress.php:41
msgid "Blocks cookies from WP SEOPress' Google Analytics integration."
msgstr ""

#: src/addons/controller/Plugin_Controller.php:50
msgid "You enabled Cookiebot™ auto blocking mode but still using addons"
msgstr ""

#: src/addons/controller/Plugin_Controller.php:55
msgid "In some occasions this may cause client side errors. If you notice any errors please try to disable Cookiebot™ addons or contact Cookiebot™ support."
msgstr ""

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:84
msgid "Share your experience"
msgstr ""

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:88
msgid "Hi there! We are thrilled you love the Cookiebot CMP plugin. Could you do us a huge favor and leave a 5-star rating on WordPress? Your support will help us spread the word and empower more WordPress websites to meet GDPR and CCPA compliance standards effortlessly. Thank you for your support!"
msgstr ""

#: src/admin_notices/Cookiebot_Temp_Notice.php:43
msgid "Cookiebot CMP Plugin will soon no longer support PHP 5. If your website still runs on this version we recommend upgrading so you can continue enjoying the features Cookiebot CMP offers."
msgstr ""

#: src/lib/Cookiebot_Review.php:82
msgid "Sorry you are not allowed to do this."
msgstr ""

#: src/lib/Cookiebot_Review.php:85
#: src/view/admin/common/templates/extra/review-form.php:94
msgid "Please select one option"
msgstr ""

#. translators: The placeholder is for the COOKIEBOT_MIN_PHP_VERSION constant
#: src/lib/Cookiebot_WP.php:66
msgid "The Cookiebot plugin requires PHP version %s or greater."
msgstr ""

#: src/lib/Cookiebot_WP.php:242
#: src/settings/pages/Dashboard_Page.php:35
#: src/view/admin/common/templates/main-tabs.php:21
msgid "Dashboard"
msgstr ""

#: src/lib/helper.php:245
#: src/lib/helper.php:343
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:91
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:167
#: src/view/admin/cb_frame/settings/gcm-page.php:126
#: src/view/admin/cb_frame/settings/gtm-page.php:113
#: src/view/admin/common/prior-consent/available-addons/tab.php:55
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:88
msgid "marketing"
msgstr ""

#: src/lib/helper.php:248
#: src/lib/helper.php:342
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:88
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:134
#: src/view/admin/cb_frame/settings/gcm-page.php:118
#: src/view/admin/cb_frame/settings/gtm-page.php:105
#: src/view/admin/common/prior-consent/available-addons/tab.php:46
msgid "statistics"
msgstr ""

#: src/lib/helper.php:251
#: src/lib/helper.php:341
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:85
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:118
#: src/view/admin/cb_frame/settings/gcm-page.php:110
#: src/view/admin/cb_frame/settings/gtm-page.php:97
#: src/view/admin/common/prior-consent/available-addons/tab.php:37
msgid "preferences"
msgstr ""

#: src/lib/helper.php:254
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:82
msgid "necessary"
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:367
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:372
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable tracking."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:377
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable Social Share buttons."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:382
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to view this element."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:387
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to watch this video."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:392
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable Google Services."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:397
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable facebook shopping feature."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:402
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to track for google analytics."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:407
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable Google Analytics."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:412
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable instagram feed."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:417
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable Facebook Pixel."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:422
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to Social Share buttons."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:427
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to allow Matomo statistics."
msgstr ""

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:432
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable saving user information."
msgstr ""

#: src/lib/Supported_Languages.php:8
msgid "Norwegian Bokmål"
msgstr ""

#: src/lib/Supported_Languages.php:9
msgid "Turkish"
msgstr ""

#: src/lib/Supported_Languages.php:10
msgid "German"
msgstr ""

#: src/lib/Supported_Languages.php:11
msgid "Czech"
msgstr ""

#: src/lib/Supported_Languages.php:12
msgid "Danish"
msgstr ""

#: src/lib/Supported_Languages.php:13
msgid "Albanian"
msgstr ""

#: src/lib/Supported_Languages.php:14
msgid "Hebrew"
msgstr ""

#: src/lib/Supported_Languages.php:15
msgid "Korean"
msgstr ""

#: src/lib/Supported_Languages.php:16
msgid "Italian"
msgstr ""

#: src/lib/Supported_Languages.php:17
msgid "Dutch"
msgstr ""

#: src/lib/Supported_Languages.php:18
msgid "Vietnamese"
msgstr ""

#: src/lib/Supported_Languages.php:19
msgid "Tamil"
msgstr ""

#: src/lib/Supported_Languages.php:20
msgid "Icelandic"
msgstr ""

#: src/lib/Supported_Languages.php:21
msgid "Romanian"
msgstr ""

#: src/lib/Supported_Languages.php:22
msgid "Sinhala"
msgstr ""

#: src/lib/Supported_Languages.php:23
msgid "Catalan"
msgstr ""

#: src/lib/Supported_Languages.php:24
msgid "Bulgarian"
msgstr ""

#: src/lib/Supported_Languages.php:25
msgid "Ukrainian"
msgstr ""

#: src/lib/Supported_Languages.php:26
msgid "Chinese"
msgstr ""

#: src/lib/Supported_Languages.php:27
msgid "English"
msgstr ""

#: src/lib/Supported_Languages.php:28
msgid "Arabic"
msgstr ""

#: src/lib/Supported_Languages.php:29
msgid "Croatian"
msgstr ""

#: src/lib/Supported_Languages.php:30
msgid "Thai"
msgstr ""

#: src/lib/Supported_Languages.php:31
msgid "Greek"
msgstr ""

#: src/lib/Supported_Languages.php:32
msgid "Lithuanian"
msgstr ""

#: src/lib/Supported_Languages.php:33
msgid "Polish"
msgstr ""

#: src/lib/Supported_Languages.php:34
msgid "Latvian"
msgstr ""

#: src/lib/Supported_Languages.php:35
msgid "French"
msgstr ""

#: src/lib/Supported_Languages.php:36
msgid "Indonesian"
msgstr ""

#: src/lib/Supported_Languages.php:37
msgid "Macedonian"
msgstr ""

#: src/lib/Supported_Languages.php:38
msgid "Estonian"
msgstr ""

#: src/lib/Supported_Languages.php:39
msgid "Portuguese"
msgstr ""

#: src/lib/Supported_Languages.php:40
msgid "Irish"
msgstr ""

#: src/lib/Supported_Languages.php:41
msgid "Malay"
msgstr ""

#: src/lib/Supported_Languages.php:42
msgid "Slovenian"
msgstr ""

#: src/lib/Supported_Languages.php:43
msgid "Russian"
msgstr ""

#: src/lib/Supported_Languages.php:44
msgid "Japanese"
msgstr ""

#: src/lib/Supported_Languages.php:45
msgid "Hindi"
msgstr ""

#: src/lib/Supported_Languages.php:46
msgid "Slovak"
msgstr ""

#: src/lib/Supported_Languages.php:47
msgid "Spanish"
msgstr ""

#: src/lib/Supported_Languages.php:48
msgid "Swedish"
msgstr ""

#: src/lib/Supported_Languages.php:49
msgid "Serbian"
msgstr ""

#: src/lib/Supported_Languages.php:50
msgid "Finnish"
msgstr ""

#: src/lib/Supported_Languages.php:51
msgid "Basque"
msgstr ""

#: src/lib/Supported_Languages.php:52
msgid "Hungarian"
msgstr ""

#: src/lib/Supported_Regions.php:7
msgid "Afghanistan"
msgstr ""

#: src/lib/Supported_Regions.php:8
msgid "Albania"
msgstr ""

#: src/lib/Supported_Regions.php:9
msgid "Algeria"
msgstr ""

#: src/lib/Supported_Regions.php:10
msgid "American Samoa"
msgstr ""

#: src/lib/Supported_Regions.php:11
msgid "Andorra"
msgstr ""

#: src/lib/Supported_Regions.php:12
msgid "Angola"
msgstr ""

#: src/lib/Supported_Regions.php:13
msgid "Anguilla"
msgstr ""

#: src/lib/Supported_Regions.php:14
msgid "Antarctica"
msgstr ""

#: src/lib/Supported_Regions.php:15
msgid "Antigua and Barbuda"
msgstr ""

#: src/lib/Supported_Regions.php:16
msgid "Argentina"
msgstr ""

#: src/lib/Supported_Regions.php:17
msgid "Armenia"
msgstr ""

#: src/lib/Supported_Regions.php:18
msgid "Aruba"
msgstr ""

#: src/lib/Supported_Regions.php:19
msgid "Australia"
msgstr ""

#: src/lib/Supported_Regions.php:20
msgid "Austria"
msgstr ""

#: src/lib/Supported_Regions.php:21
msgid "Azerbaijan"
msgstr ""

#: src/lib/Supported_Regions.php:22
msgid "Bahamas"
msgstr ""

#: src/lib/Supported_Regions.php:23
msgid "Bahrain"
msgstr ""

#: src/lib/Supported_Regions.php:24
msgid "Bangladesh"
msgstr ""

#: src/lib/Supported_Regions.php:25
msgid "Barbados"
msgstr ""

#: src/lib/Supported_Regions.php:26
msgid "Belarus"
msgstr ""

#: src/lib/Supported_Regions.php:27
msgid "Belgium"
msgstr ""

#: src/lib/Supported_Regions.php:28
msgid "Belize"
msgstr ""

#: src/lib/Supported_Regions.php:29
msgid "Benin"
msgstr ""

#: src/lib/Supported_Regions.php:30
msgid "Bermuda"
msgstr ""

#: src/lib/Supported_Regions.php:31
msgid "Bhutan"
msgstr ""

#: src/lib/Supported_Regions.php:32
msgid "Bolivia"
msgstr ""

#: src/lib/Supported_Regions.php:33
msgid "Bonaire, Sint Eustatius and Saba"
msgstr ""

#: src/lib/Supported_Regions.php:34
msgid "Bosnia and Herzegovina"
msgstr ""

#: src/lib/Supported_Regions.php:35
msgid "Botswana"
msgstr ""

#: src/lib/Supported_Regions.php:36
msgid "Bouvet Island"
msgstr ""

#: src/lib/Supported_Regions.php:37
msgid "Brazil"
msgstr ""

#: src/lib/Supported_Regions.php:38
msgid "British Indian Ocean Territory"
msgstr ""

#: src/lib/Supported_Regions.php:39
msgid "Brunei "
msgstr ""

#: src/lib/Supported_Regions.php:40
msgid "Bulgaria"
msgstr ""

#: src/lib/Supported_Regions.php:41
msgid "Burkina Faso"
msgstr ""

#: src/lib/Supported_Regions.php:42
msgid "Burundi"
msgstr ""

#: src/lib/Supported_Regions.php:43
msgid "Cambodia"
msgstr ""

#: src/lib/Supported_Regions.php:44
msgid "Cameroon"
msgstr ""

#: src/lib/Supported_Regions.php:45
msgid "Canada"
msgstr ""

#: src/lib/Supported_Regions.php:46
msgid "Cape Verde"
msgstr ""

#: src/lib/Supported_Regions.php:47
msgid "Cayman Islands"
msgstr ""

#: src/lib/Supported_Regions.php:48
msgid "Central African Republic"
msgstr ""

#: src/lib/Supported_Regions.php:49
msgid "Chad"
msgstr ""

#: src/lib/Supported_Regions.php:50
msgid "Chile"
msgstr ""

#: src/lib/Supported_Regions.php:51
msgid "China"
msgstr ""

#: src/lib/Supported_Regions.php:52
msgid "Christmas Island"
msgstr ""

#: src/lib/Supported_Regions.php:53
msgid "Cocos (Keeling) Islands"
msgstr ""

#: src/lib/Supported_Regions.php:54
msgid "Colombia"
msgstr ""

#: src/lib/Supported_Regions.php:55
msgid "Comoros"
msgstr ""

#: src/lib/Supported_Regions.php:56
msgid "Congo"
msgstr ""

#: src/lib/Supported_Regions.php:57
msgid "Congo, the Democratic Republic of the"
msgstr ""

#: src/lib/Supported_Regions.php:58
msgid "Cook Islands"
msgstr ""

#: src/lib/Supported_Regions.php:59
msgid "Costa Rica"
msgstr ""

#: src/lib/Supported_Regions.php:60
msgid "Croatia"
msgstr ""

#: src/lib/Supported_Regions.php:61
msgid "Cuba"
msgstr ""

#: src/lib/Supported_Regions.php:62
msgid "Curaçao"
msgstr ""

#: src/lib/Supported_Regions.php:63
msgid "Cyprus"
msgstr ""

#: src/lib/Supported_Regions.php:64
msgid "Czech Republic"
msgstr ""

#: src/lib/Supported_Regions.php:65
msgid "Côte d'Ivoire"
msgstr ""

#: src/lib/Supported_Regions.php:66
msgid "Denmark"
msgstr ""

#: src/lib/Supported_Regions.php:67
msgid "Djibouti"
msgstr ""

#: src/lib/Supported_Regions.php:68
msgid "Dominica"
msgstr ""

#: src/lib/Supported_Regions.php:69
msgid "Dominican Republic"
msgstr ""

#: src/lib/Supported_Regions.php:70
msgid "Ecuador"
msgstr ""

#: src/lib/Supported_Regions.php:71
msgid "Egypt"
msgstr ""

#: src/lib/Supported_Regions.php:72
msgid "El Salvador"
msgstr ""

#: src/lib/Supported_Regions.php:73
msgid "Equatorial Guinea"
msgstr ""

#: src/lib/Supported_Regions.php:74
msgid "Eritrea"
msgstr ""

#: src/lib/Supported_Regions.php:75
msgid "Estonia"
msgstr ""

#: src/lib/Supported_Regions.php:76
msgid "Ethiopia"
msgstr ""

#: src/lib/Supported_Regions.php:77
msgid "Falkland Islands (Malvinas)"
msgstr ""

#: src/lib/Supported_Regions.php:78
msgid "Faroe Islands"
msgstr ""

#: src/lib/Supported_Regions.php:79
msgid "Fiji"
msgstr ""

#: src/lib/Supported_Regions.php:80
msgid "Finland"
msgstr ""

#: src/lib/Supported_Regions.php:81
msgid "France"
msgstr ""

#: src/lib/Supported_Regions.php:82
msgid "French Guiana"
msgstr ""

#: src/lib/Supported_Regions.php:83
msgid "French Polynesia"
msgstr ""

#: src/lib/Supported_Regions.php:84
msgid "French Southern and Antarctic Lands"
msgstr ""

#: src/lib/Supported_Regions.php:85
msgid "Gabon"
msgstr ""

#: src/lib/Supported_Regions.php:86
msgid "Gambia"
msgstr ""

#: src/lib/Supported_Regions.php:87
msgid "Georgia"
msgstr ""

#: src/lib/Supported_Regions.php:88
msgid "Germany"
msgstr ""

#: src/lib/Supported_Regions.php:89
msgid "Ghana"
msgstr ""

#: src/lib/Supported_Regions.php:90
msgid "Gibraltar"
msgstr ""

#: src/lib/Supported_Regions.php:91
msgid "Greece"
msgstr ""

#: src/lib/Supported_Regions.php:92
msgid "Greenland"
msgstr ""

#: src/lib/Supported_Regions.php:93
msgid "Grenada"
msgstr ""

#: src/lib/Supported_Regions.php:94
msgid "Guadeloupe"
msgstr ""

#: src/lib/Supported_Regions.php:95
msgid "Guam"
msgstr ""

#: src/lib/Supported_Regions.php:96
msgid "Guatemala"
msgstr ""

#: src/lib/Supported_Regions.php:97
msgid "Guernsey"
msgstr ""

#: src/lib/Supported_Regions.php:98
msgid "Guinea"
msgstr ""

#: src/lib/Supported_Regions.php:99
msgid "Guinea-Bissau"
msgstr ""

#: src/lib/Supported_Regions.php:100
msgid "Guyana"
msgstr ""

#: src/lib/Supported_Regions.php:101
msgid "Haiti"
msgstr ""

#: src/lib/Supported_Regions.php:102
msgid "Heard Island and McDonald Islands"
msgstr ""

#: src/lib/Supported_Regions.php:103
msgid "Holy See (Vatican City State)"
msgstr ""

#: src/lib/Supported_Regions.php:104
msgid "Honduras"
msgstr ""

#: src/lib/Supported_Regions.php:105
msgid "Hong Kong"
msgstr ""

#: src/lib/Supported_Regions.php:106
msgid "Hungary"
msgstr ""

#: src/lib/Supported_Regions.php:107
msgid "Iceland"
msgstr ""

#: src/lib/Supported_Regions.php:108
msgid "India"
msgstr ""

#: src/lib/Supported_Regions.php:109
msgid "Indonesia"
msgstr ""

#: src/lib/Supported_Regions.php:110
msgid "Iran"
msgstr ""

#: src/lib/Supported_Regions.php:111
msgid "Iraq"
msgstr ""

#: src/lib/Supported_Regions.php:112
msgid "Ireland"
msgstr ""

#: src/lib/Supported_Regions.php:113
msgid "Isle of Man"
msgstr ""

#: src/lib/Supported_Regions.php:114
msgid "Israel"
msgstr ""

#: src/lib/Supported_Regions.php:115
msgid "Italy"
msgstr ""

#: src/lib/Supported_Regions.php:116
msgid "Jamaica"
msgstr ""

#: src/lib/Supported_Regions.php:117
msgid "Japan"
msgstr ""

#: src/lib/Supported_Regions.php:118
msgid "Jersey"
msgstr ""

#: src/lib/Supported_Regions.php:119
msgid "Jordan"
msgstr ""

#: src/lib/Supported_Regions.php:120
msgid "Kazakhstan"
msgstr ""

#: src/lib/Supported_Regions.php:121
msgid "Kenya"
msgstr ""

#: src/lib/Supported_Regions.php:122
msgid "Kiribati"
msgstr ""

#: src/lib/Supported_Regions.php:123
msgid "Kuwait"
msgstr ""

#: src/lib/Supported_Regions.php:124
msgid "Kyrgyzstan"
msgstr ""

#: src/lib/Supported_Regions.php:125
msgid "Laos"
msgstr ""

#: src/lib/Supported_Regions.php:126
msgid "Latvia"
msgstr ""

#: src/lib/Supported_Regions.php:127
msgid "Lebanon"
msgstr ""

#: src/lib/Supported_Regions.php:128
msgid "Lesotho"
msgstr ""

#: src/lib/Supported_Regions.php:129
msgid "Liberia"
msgstr ""

#: src/lib/Supported_Regions.php:130
msgid "Libya"
msgstr ""

#: src/lib/Supported_Regions.php:131
msgid "Liechtenstein"
msgstr ""

#: src/lib/Supported_Regions.php:132
msgid "Lithuania"
msgstr ""

#: src/lib/Supported_Regions.php:133
msgid "Luxembourg"
msgstr ""

#: src/lib/Supported_Regions.php:134
msgid "Macao"
msgstr ""

#: src/lib/Supported_Regions.php:135
msgid "North Macedonia"
msgstr ""

#: src/lib/Supported_Regions.php:136
msgid "Madagascar"
msgstr ""

#: src/lib/Supported_Regions.php:137
msgid "Malawi"
msgstr ""

#: src/lib/Supported_Regions.php:138
msgid "Malaysia"
msgstr ""

#: src/lib/Supported_Regions.php:139
msgid "Maldives"
msgstr ""

#: src/lib/Supported_Regions.php:140
msgid "Mali"
msgstr ""

#: src/lib/Supported_Regions.php:141
msgid "Malta"
msgstr ""

#: src/lib/Supported_Regions.php:142
msgid "Marshall Islands"
msgstr ""

#: src/lib/Supported_Regions.php:143
msgid "Martinique"
msgstr ""

#: src/lib/Supported_Regions.php:144
msgid "Mauritania"
msgstr ""

#: src/lib/Supported_Regions.php:145
msgid "Mauritius"
msgstr ""

#: src/lib/Supported_Regions.php:146
msgid "Mayotte"
msgstr ""

#: src/lib/Supported_Regions.php:147
msgid "Mexico"
msgstr ""

#: src/lib/Supported_Regions.php:148
msgid "Micronesia, Federated States of"
msgstr ""

#: src/lib/Supported_Regions.php:149
msgid "Moldova"
msgstr ""

#: src/lib/Supported_Regions.php:150
msgid "Monaco"
msgstr ""

#: src/lib/Supported_Regions.php:151
msgid "Mongolia"
msgstr ""

#: src/lib/Supported_Regions.php:152
msgid "Montenegro"
msgstr ""

#: src/lib/Supported_Regions.php:153
msgid "Montserrat"
msgstr ""

#: src/lib/Supported_Regions.php:154
msgid "Morocco"
msgstr ""

#: src/lib/Supported_Regions.php:155
msgid "Mozambique"
msgstr ""

#: src/lib/Supported_Regions.php:156
msgid "Myanmar"
msgstr ""

#: src/lib/Supported_Regions.php:157
msgid "Namibia"
msgstr ""

#: src/lib/Supported_Regions.php:158
msgid "Nauru"
msgstr ""

#: src/lib/Supported_Regions.php:159
msgid "Nepal"
msgstr ""

#: src/lib/Supported_Regions.php:160
msgid "Netherlands"
msgstr ""

#: src/lib/Supported_Regions.php:161
msgid "New Caledonia"
msgstr ""

#: src/lib/Supported_Regions.php:162
msgid "New Zealand"
msgstr ""

#: src/lib/Supported_Regions.php:163
msgid "Nicaragua"
msgstr ""

#: src/lib/Supported_Regions.php:164
msgid "Niger"
msgstr ""

#: src/lib/Supported_Regions.php:165
msgid "Nigeria"
msgstr ""

#: src/lib/Supported_Regions.php:166
msgid "Niue"
msgstr ""

#: src/lib/Supported_Regions.php:167
msgid "Norfolk Island"
msgstr ""

#: src/lib/Supported_Regions.php:168
msgid "North Korea"
msgstr ""

#: src/lib/Supported_Regions.php:169
msgid "Northern Mariana Islands"
msgstr ""

#: src/lib/Supported_Regions.php:170
msgid "Norway"
msgstr ""

#: src/lib/Supported_Regions.php:171
msgid "Oman"
msgstr ""

#: src/lib/Supported_Regions.php:172
msgid "Pakistan"
msgstr ""

#: src/lib/Supported_Regions.php:173
msgid "Palau"
msgstr ""

#: src/lib/Supported_Regions.php:174
msgid "Palestinian Territory"
msgstr ""

#: src/lib/Supported_Regions.php:175
msgid "Panama"
msgstr ""

#: src/lib/Supported_Regions.php:176
msgid "Papua New Guinea"
msgstr ""

#: src/lib/Supported_Regions.php:177
msgid "Paraguay"
msgstr ""

#: src/lib/Supported_Regions.php:178
msgid "Peru"
msgstr ""

#: src/lib/Supported_Regions.php:179
msgid "Philippines"
msgstr ""

#: src/lib/Supported_Regions.php:180
msgid "Pitcairn"
msgstr ""

#: src/lib/Supported_Regions.php:181
msgid "Poland"
msgstr ""

#: src/lib/Supported_Regions.php:182
msgid "Portugal"
msgstr ""

#: src/lib/Supported_Regions.php:183
msgid "Puerto Rico"
msgstr ""

#: src/lib/Supported_Regions.php:184
msgid "Qatar"
msgstr ""

#: src/lib/Supported_Regions.php:185
msgid "Romania"
msgstr ""

#: src/lib/Supported_Regions.php:186
msgid "Russia"
msgstr ""

#: src/lib/Supported_Regions.php:187
msgid "Rwanda"
msgstr ""

#: src/lib/Supported_Regions.php:188
msgid "Réunion"
msgstr ""

#: src/lib/Supported_Regions.php:189
msgid "Saint Barthélemy"
msgstr ""

#: src/lib/Supported_Regions.php:190
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr ""

#: src/lib/Supported_Regions.php:191
msgid "Saint Kitts and Nevis"
msgstr ""

#: src/lib/Supported_Regions.php:192
msgid "Saint Lucia"
msgstr ""

#: src/lib/Supported_Regions.php:193
msgid "Saint Martin (French part)"
msgstr ""

#: src/lib/Supported_Regions.php:194
msgid "Saint Pierre and Miquelon"
msgstr ""

#: src/lib/Supported_Regions.php:195
msgid "Saint Vincent and the Grenadines"
msgstr ""

#: src/lib/Supported_Regions.php:196
msgid "Samoa"
msgstr ""

#: src/lib/Supported_Regions.php:197
msgid "San Marino"
msgstr ""

#: src/lib/Supported_Regions.php:198
msgid "Sao Tome and Principe"
msgstr ""

#: src/lib/Supported_Regions.php:199
msgid "Saudi Arabia"
msgstr ""

#: src/lib/Supported_Regions.php:200
msgid "Senegal"
msgstr ""

#: src/lib/Supported_Regions.php:201
msgid "Serbia"
msgstr ""

#: src/lib/Supported_Regions.php:202
msgid "Seychelles"
msgstr ""

#: src/lib/Supported_Regions.php:203
msgid "Sierra Leone"
msgstr ""

#: src/lib/Supported_Regions.php:204
msgid "Singapore"
msgstr ""

#: src/lib/Supported_Regions.php:205
msgid "Sint Maarten (Dutch part)"
msgstr ""

#: src/lib/Supported_Regions.php:206
msgid "Slovakia"
msgstr ""

#: src/lib/Supported_Regions.php:207
msgid "Slovenia"
msgstr ""

#: src/lib/Supported_Regions.php:208
msgid "Solomon Islands"
msgstr ""

#: src/lib/Supported_Regions.php:209
msgid "Somalia"
msgstr ""

#: src/lib/Supported_Regions.php:210
msgid "South Africa"
msgstr ""

#: src/lib/Supported_Regions.php:211
msgid "South Georgia and the South Sandwich Islands"
msgstr ""

#: src/lib/Supported_Regions.php:212
msgid "South Korea"
msgstr ""

#: src/lib/Supported_Regions.php:213
msgid "South Sudan"
msgstr ""

#: src/lib/Supported_Regions.php:214
msgid "Spain"
msgstr ""

#: src/lib/Supported_Regions.php:215
msgid "Sri Lanka"
msgstr ""

#: src/lib/Supported_Regions.php:216
msgid "Sudan"
msgstr ""

#: src/lib/Supported_Regions.php:217
msgid "Suriname"
msgstr ""

#: src/lib/Supported_Regions.php:218
msgid "Svalbard and Jan Mayen"
msgstr ""

#: src/lib/Supported_Regions.php:219
msgid "Eswatini"
msgstr ""

#: src/lib/Supported_Regions.php:220
msgid "Sweden"
msgstr ""

#: src/lib/Supported_Regions.php:221
msgid "Switzerland"
msgstr ""

#: src/lib/Supported_Regions.php:222
msgid "Syria"
msgstr ""

#: src/lib/Supported_Regions.php:223
msgid "Taiwan"
msgstr ""

#: src/lib/Supported_Regions.php:224
msgid "Tajikistan"
msgstr ""

#: src/lib/Supported_Regions.php:225
msgid "Tanzania"
msgstr ""

#: src/lib/Supported_Regions.php:226
msgid "Thailand"
msgstr ""

#: src/lib/Supported_Regions.php:227
msgid "Timor-Leste"
msgstr ""

#: src/lib/Supported_Regions.php:228
msgid "Togo"
msgstr ""

#: src/lib/Supported_Regions.php:229
msgid "Tokelau"
msgstr ""

#: src/lib/Supported_Regions.php:230
msgid "Tonga"
msgstr ""

#: src/lib/Supported_Regions.php:231
msgid "Trinidad and Tobago"
msgstr ""

#: src/lib/Supported_Regions.php:232
msgid "Tunisia"
msgstr ""

#: src/lib/Supported_Regions.php:233
msgid "Türkiye"
msgstr ""

#: src/lib/Supported_Regions.php:234
msgid "Turkmenistan"
msgstr ""

#: src/lib/Supported_Regions.php:235
msgid "Turks and Caicos Islands"
msgstr ""

#: src/lib/Supported_Regions.php:236
msgid "Tuvalu"
msgstr ""

#: src/lib/Supported_Regions.php:237
msgid "Uganda"
msgstr ""

#: src/lib/Supported_Regions.php:238
msgid "Ukraine"
msgstr ""

#: src/lib/Supported_Regions.php:239
msgid "United Arab Emirates"
msgstr ""

#: src/lib/Supported_Regions.php:240
msgid "United Kingdom"
msgstr ""

#: src/lib/Supported_Regions.php:241
msgid "United States"
msgstr ""

#: src/lib/Supported_Regions.php:242
msgid "United States - State of California"
msgstr ""

#: src/lib/Supported_Regions.php:243
msgid "United States - State of Colorado"
msgstr ""

#: src/lib/Supported_Regions.php:244
msgid "United States - State of Connecticut"
msgstr ""

#: src/lib/Supported_Regions.php:245
msgid "United States - State of Utah"
msgstr ""

#: src/lib/Supported_Regions.php:246
msgid "United States - State of Virginia"
msgstr ""

#: src/lib/Supported_Regions.php:247
msgid "United States Minor Outlying Islands"
msgstr ""

#: src/lib/Supported_Regions.php:248
msgid "Uruguay"
msgstr ""

#: src/lib/Supported_Regions.php:249
msgid "Uzbekistan"
msgstr ""

#: src/lib/Supported_Regions.php:250
msgid "Vanuatu"
msgstr ""

#: src/lib/Supported_Regions.php:251
msgid "Venezuela"
msgstr ""

#: src/lib/Supported_Regions.php:252
msgid "Viet Nam"
msgstr ""

#: src/lib/Supported_Regions.php:253
msgid "Virgin Islands, British"
msgstr ""

#: src/lib/Supported_Regions.php:254
msgid "Virgin Islands, U.S."
msgstr ""

#: src/lib/Supported_Regions.php:255
msgid "Wallis and Futuna"
msgstr ""

#: src/lib/Supported_Regions.php:256
msgid "Western Sahara"
msgstr ""

#: src/lib/Supported_Regions.php:257
msgid "Yemen"
msgstr ""

#: src/lib/Supported_Regions.php:258
msgid "Zambia"
msgstr ""

#: src/lib/Supported_Regions.php:259
msgid "Zimbabwe"
msgstr ""

#: src/lib/Supported_Regions.php:260
msgid "Åland Islands"
msgstr ""

#: src/settings/Network_Menu_Settings.php:36
#: src/settings/pages/Dashboard_Page.php:21
msgid "Cookiebot"
msgstr ""

#: src/settings/Network_Menu_Settings.php:44
#: src/settings/pages/Settings_Page.php:23
msgid "Cookiebot Settings"
msgstr ""

#: src/settings/Network_Menu_Settings.php:45
#: src/settings/pages/Settings_Page.php:24
#: src/view/admin/common/settings-page.php:30
#: src/view/admin/common/templates/main-tabs.php:32
#: src/view/admin/uc_frame/settings-page.php:47
#: src/view/admin/cb_frame/settings-page.php:50
msgid "Settings"
msgstr ""

#: src/settings/Network_Menu_Settings.php:52
#: src/settings/pages/Support_Page.php:18
msgid "Cookiebot Support"
msgstr ""

#: src/settings/Network_Menu_Settings.php:53
#: src/settings/pages/Support_Page.php:19
#: src/view/admin/cb_frame/support-page.php:19
#: src/view/admin/common/support-page.php:19
#: src/view/admin/common/templates/main-tabs.php:50
#: src/view/admin/uc_frame/support-page.php:19
msgid "Support"
msgstr ""

#: src/settings/pages/Dashboard_Page.php:34
msgid "Cookiebot Dashboard"
msgstr ""

#: src/settings/pages/Debug_Page.php:27
#: src/settings/pages/Debug_Page.php:28
#: src/view/admin/cb_frame/debug-page.php:18
#: src/view/admin/common/templates/main-tabs.php:57
#: src/view/admin/uc_frame/debug-page.php:18
msgid "Debug info"
msgstr ""

#: src/settings/pages/Iab_Page.php:20
#: src/settings/pages/Iab_Page.php:21
msgid "IAB"
msgstr ""

#: src/settings/pages/Iab_Page.php:130
msgid "Purposes of data use"
msgstr ""

#: src/settings/pages/Iab_Page.php:131
msgid "Inform your users how you’ll use their data. We’ll show this on the second layer of your consent banner, where users interested in more granular detail about data processing can view it."
msgstr ""

#: src/settings/pages/Iab_Page.php:138
msgid "Special purposes of data use"
msgstr ""

#: src/settings/pages/Iab_Page.php:139
msgid "Inform your users about special purposes of using their data. We’ll show this on the second layer of your consent banner."
msgstr ""

#: src/settings/pages/Iab_Page.php:146
msgid "Features required for data processing"
msgstr ""

#: src/settings/pages/Iab_Page.php:147
msgid "Inform users about the features necessary for processing their personal data. We’ll list the selected features on the second layer of your consent banner."
msgstr ""

#: src/settings/pages/Iab_Page.php:154
msgid "Special features required for data processing"
msgstr ""

#: src/settings/pages/Iab_Page.php:155
msgid "Inform users about any specially categorized features required for processing their personal data. We’ll list the selected features on the second layer of your consent banner, offering options for users to enable or disable them."
msgstr ""

#: src/settings/pages/Iab_Page.php:162
msgid "TCF listed vendors"
msgstr ""

#: src/settings/pages/Iab_Page.php:183
msgid "Store and/or access information on a device"
msgstr ""

#: src/settings/pages/Iab_Page.php:187
msgid "Use limited data to select advertising"
msgstr ""

#: src/settings/pages/Iab_Page.php:191
msgid "Create profiles for personalised advertising"
msgstr ""

#: src/settings/pages/Iab_Page.php:195
msgid "Use profiles to select personalised advertising"
msgstr ""

#: src/settings/pages/Iab_Page.php:199
msgid "Create profiles to personalise content"
msgstr ""

#: src/settings/pages/Iab_Page.php:203
msgid "Use profiles to select personalised content"
msgstr ""

#: src/settings/pages/Iab_Page.php:207
msgid "Measure advertising performance"
msgstr ""

#: src/settings/pages/Iab_Page.php:211
msgid "Measure content performance"
msgstr ""

#: src/settings/pages/Iab_Page.php:215
msgid "Understand audiences through statistics or combinations of data from different sources"
msgstr ""

#: src/settings/pages/Iab_Page.php:219
msgid "Develop and improve services"
msgstr ""

#: src/settings/pages/Iab_Page.php:223
msgid "Use limited data to select content"
msgstr ""

#: src/settings/pages/Iab_Page.php:229
msgid "Ensure security, prevent and detect fraud, and fix errors"
msgstr ""

#: src/settings/pages/Iab_Page.php:233
msgid "Deliver and present advertising and content"
msgstr ""

#: src/settings/pages/Iab_Page.php:239
msgid "Match and combine data from other data sources"
msgstr ""

#: src/settings/pages/Iab_Page.php:243
msgid "Link different devices"
msgstr ""

#: src/settings/pages/Iab_Page.php:247
msgid "Identify devices based on information transmitted automatically"
msgstr ""

#: src/settings/pages/Iab_Page.php:253
msgid "Use precise geolocation data"
msgstr ""

#: src/settings/pages/Iab_Page.php:257
msgid "Actively scan device characteristics for identification"
msgstr ""

#: src/shortcode/Cookiebot_Declaration_Shortcode.php:53
msgid "Please add your Cookiebot ID to show Cookie Declarations"
msgstr ""

#: src/shortcode/Cookiebot_Embedding_Shortcode.php:28
msgid "Please add a service ID into the shortcode \"service\" parameter."
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:33
msgid "I already have a Cookiebot CMP account"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:37
msgid "Connect my existing account"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:42
msgid "Your Cookiebot CMP for WordPress solution"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:47
#: src/view/admin/uc_frame/dashboard-page.php:35
msgid "Account added"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:50
msgid "Congratulations!"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:51
msgid "You have added your Domain Group ID to WordPress. You are all set!"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:62
#: src/view/admin/uc_frame/dashboard-page.php:48
msgid "Your opinion matters"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:65
msgid "Are you happy with our WordPress plugin? Your feedback will help us make our product better for you."
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:69
msgid "Write a review"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:80
#: src/view/admin/common/dashboard-page.php:45
msgid "Get started"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:83
msgid "Create a new Cookiebot CMP account"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:87
#: src/view/admin/common/dashboard-page.php:52
msgid "Create a new account"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:92
msgid "Learn more about how to optimize your Cookiebot CMP setup?"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:96
msgid "Visit Help Center"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:113
#: src/view/admin/cb_frame/support-page.php:43
#: src/view/admin/common/dashboard-page.php:63
#: src/view/admin/common/support-page.php:43
#: src/view/admin/uc_frame/dashboard-page.php:66
#: src/view/admin/uc_frame/support-page.php:43
msgid "Video guide"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:115
msgid "How to get started with Cookiebot CMP"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:119
msgid "Learn more about Cookiebot CMP"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:131
#: src/view/admin/common/dashboard-page.php:81
#: src/view/admin/uc_frame/dashboard-page.php:84
#: src/view/admin/uc_frame/settings/embeddings-page.php:32
msgid "GDPR"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:134
#: src/view/admin/common/dashboard-page.php:84
#: src/view/admin/uc_frame/dashboard-page.php:87
msgid "Europe"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:138
#: src/view/admin/cb_frame/dashboard-page.php:140
#: src/view/admin/cb_frame/dashboard-page.php:155
#: src/view/admin/cb_frame/dashboard-page.php:157
#: src/view/admin/common/dashboard-page.php:88
#: src/view/admin/common/dashboard-page.php:90
#: src/view/admin/common/dashboard-page.php:105
#: src/view/admin/common/dashboard-page.php:107
#: src/view/admin/uc_frame/dashboard-page.php:91
#: src/view/admin/uc_frame/dashboard-page.php:93
#: src/view/admin/uc_frame/dashboard-page.php:108
#: src/view/admin/uc_frame/dashboard-page.php:110
msgid "Learn More"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:148
#: src/view/admin/common/dashboard-page.php:98
#: src/view/admin/uc_frame/dashboard-page.php:101
msgid "CCPA"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:151
#: src/view/admin/common/dashboard-page.php:101
#: src/view/admin/uc_frame/dashboard-page.php:104
msgid "North America"
msgstr ""

#: src/view/admin/cb_frame/dashboard-page.php:162
#: src/view/admin/common/dashboard-page.php:112
msgid "See other legislations"
msgstr ""

#: src/view/admin/cb_frame/debug-page.php:22
msgid "Debug information"
msgstr ""

#: src/view/admin/cb_frame/debug-page.php:25
msgid "The information below is for debugging purposes. If you have any issues with your Cookiebot CMP integration, this information is the best place to start."
msgstr ""

#: src/view/admin/cb_frame/debug-page.php:33
#: src/view/admin/uc_frame/debug-page.php:33
msgid "Copy debug information to clipboard"
msgstr ""

#: src/view/admin/cb_frame/debug-page.php:42
msgid "If you have any issues with the implemenation of Cookiebot CMP, please visit our Support Center."
msgstr ""

#: src/view/admin/cb_frame/debug-page.php:45
#: src/view/admin/uc_frame/debug-page.php:44
msgid "Visit Support Center"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:22
#: src/view/admin/common/network-settings-page.php:23
#: src/view/admin/uc_frame/network-settings-page.php:23
msgid "Network Settings"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:33
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:3
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:3
#: src/view/admin/uc_frame/network-settings-page.php:34
msgid "Are you sure?"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:36
#: src/view/admin/uc_frame/network-settings-page.php:37
msgid "You will need to add a new ID before updating other network settings. If any subsite is using its own account disconnecting this account won’t affect it."
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:40
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:10
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:10
#: src/view/admin/uc_frame/network-settings-page.php:41
msgid "Cancel"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:43
#: src/view/admin/cb_frame/network-settings-page.php:65
#: src/view/admin/cb_frame/settings/general-page.php:52
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:13
#: src/view/admin/uc_frame/network-settings-page.php:44
#: src/view/admin/uc_frame/network-settings-page.php:75
#: src/view/admin/uc_frame/settings/general-page.php:45
msgid "Disconnect account"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:50
msgid "Network Domain Group ID"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:52
msgid "If added this will be the default Cookiebot ID for all subsites. Subsites are able to override the Cookiebot ID."
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:54
#: src/view/admin/cb_frame/settings/general-page.php:37
msgid "Read more on the Domain Group ID"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:58
#: src/view/admin/cb_frame/settings/general-page.php:43
msgid "Add your Domain Group ID"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:73
#: src/view/admin/cb_frame/settings/general-page.php:146
#: src/view/admin/uc_frame/network-settings-page.php:111
#: src/view/admin/uc_frame/settings/general-page.php:120
msgid "Cookie-blocking"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:75
msgid "Select your cookie-blocking mode here. Auto cookie-blocking mode will automatically block all cookies (except for ‘strictly necessary’ cookies) until a user has given consent. Manual cookie-blocking mode requests manual adjustments to the cookie-setting scripts. Please find our implementation guides below:"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:80
#: src/view/admin/cb_frame/settings/general-page.php:167
#: src/view/admin/uc_frame/network-settings-page.php:118
#: src/view/admin/uc_frame/settings/general-page.php:133
msgid "Select cookie-blocking mode"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:87
#: src/view/admin/cb_frame/settings/general-page.php:175
#: src/view/admin/uc_frame/network-settings-page.php:125
#: src/view/admin/uc_frame/settings/general-page.php:141
msgid "Automatic"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:88
#: src/view/admin/cb_frame/settings/general-page.php:176
#: src/view/admin/uc_frame/network-settings-page.php:126
#: src/view/admin/uc_frame/settings/general-page.php:142
msgid "Recommended"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:96
#: src/view/admin/cb_frame/network-settings-page.php:146
#: src/view/admin/cb_frame/network-settings-page.php:193
#: src/view/admin/uc_frame/network-settings-page.php:134
msgid "Choose per subsite"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:104
#: src/view/admin/cb_frame/settings/general-page.php:206
msgid "Cookiebot™ script tag"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:106
msgid "Add async or defer attribute to Cookie banner script tag. Default: Choose per subsite"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:109
msgid "This feature is only available when not using Auto Blocking"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:112
#: src/view/admin/cb_frame/network-settings-page.php:156
#: src/view/admin/cb_frame/network-settings-page.php:228
#: src/view/admin/uc_frame/network-settings-page.php:169
msgid "Setting will apply for all subsites. Subsites will not be able to override."
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:125
#: src/view/admin/cb_frame/network-settings-page.php:172
#: src/view/admin/cb_frame/settings/additional-page.php:99
#: src/view/admin/cb_frame/settings/general-page.php:222
msgid "None"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:154
msgid "Cookiebot declaration script tag"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:159
msgid "Add async or defer attribute to Cookie declaration script tag. Default: Choose per subsite"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:201
#: src/view/admin/cb_frame/settings/additional-page.php:10
#: src/view/admin/uc_frame/network-settings-page.php:142
#: src/view/admin/uc_frame/settings/additional-page.php:10
msgid "Automatic updates"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:203
#: src/view/admin/cb_frame/settings/additional-page.php:13
#: src/view/admin/uc_frame/network-settings-page.php:144
#: src/view/admin/uc_frame/settings/additional-page.php:13
msgid "Enable automatic updates whenever we release a new version of the plugin."
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:218
#: src/view/admin/cb_frame/settings/additional-page.php:28
#: src/view/admin/uc_frame/network-settings-page.php:159
#: src/view/admin/uc_frame/settings/additional-page.php:28
msgid "Automatically update to new version"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:226
#: src/view/admin/cb_frame/settings/general-page.php:248
#: src/view/admin/uc_frame/network-settings-page.php:167
#: src/view/admin/uc_frame/settings/general-page.php:172
msgid "Hide cookie popup"
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:231
#: src/view/admin/cb_frame/settings/general-page.php:254
#: src/view/admin/uc_frame/network-settings-page.php:172
#: src/view/admin/uc_frame/settings/general-page.php:178
msgid "This will remove the cookie consent banner from your website. The cookie declaration shortcode will still be available if you are using Google Tag Manager (or equal), you need to add the Cookiebot script in your Tag Manager."
msgstr ""

#: src/view/admin/cb_frame/network-settings-page.php:246
#: src/view/admin/cb_frame/settings/general-page.php:280
#: src/view/admin/uc_frame/network-settings-page.php:187
#: src/view/admin/uc_frame/settings/general-page.php:204
msgid "Hide the cookie popup banner"
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:11
#: src/view/admin/common/prior-consent/available-addons/tab-header.php:4
#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:14
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:14
msgid "Remember to save your changes before switching tabs"
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:20
msgid "Consent Level API Settings"
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:29
msgid "WP Consent Level API and Cookiebot™ categorize cookies a bit differently. The default settings should fit most needs, but if you need to change the mapping you can do so below."
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:79
msgid "Cookiebot™ cookie categories"
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:96
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:51
msgid "WP Consent API cookies categories equivalent"
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:102
msgid "Functional"
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:150
msgid "Statistics Anonymous"
msgstr ""

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:179
msgid "Reset to default mapping"
msgstr ""

#: src/view/admin/cb_frame/settings-page.php:48
msgid "Select at least one vendor on TCF tab"
msgstr ""

#: src/view/admin/cb_frame/settings-page.php:57
#: src/view/admin/uc_frame/settings-page.php:54
msgid "General Settings"
msgstr ""

#: src/view/admin/cb_frame/settings-page.php:61
#: src/view/admin/uc_frame/settings-page.php:58
msgid "Additional Settings"
msgstr ""

#: src/view/admin/cb_frame/settings-page.php:65
#: src/view/admin/cb_frame/settings/gtm-page.php:27
#: src/view/admin/uc_frame/settings-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:12
#: src/view/admin/uc_frame/settings/gtm-page.php:27
msgid "Google Tag Manager"
msgstr ""

#: src/view/admin/cb_frame/settings-page.php:69
#: src/view/admin/cb_frame/settings/gcm-page.php:50
#: src/view/admin/uc_frame/settings-page.php:66
#: src/view/admin/uc_frame/settings/gcm-page.php:28
#: src/view/admin/uc_frame/settings/gcm-page.php:43
msgid "Google Consent Mode"
msgstr ""

#: src/view/admin/cb_frame/settings-page.php:73
#: src/view/admin/uc_frame/settings/embeddings-page.php:33
msgid "TCF"
msgstr ""

#: src/view/admin/cb_frame/settings-page.php:77
msgid "Multiple Configurations"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:37
#: src/view/admin/uc_frame/settings/additional-page.php:37
msgid "Show the banner while logged in"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:40
#: src/view/admin/uc_frame/settings/additional-page.php:40
msgid "You can choose to display the consent banner on your website while you’re logged in and changing settings or customizing your banner."
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:66
#: src/view/admin/uc_frame/settings/additional-page.php:66
msgid "Show banner on website when logged in"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:75
msgid "Cookie declaration script tag:"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:78
msgid "If you implemented the declaration on your page through our widget in WordPress, you can choose here how the script should be loaded."
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:84
msgid "Select the cookie declaration script loading setting"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:116
#: src/view/admin/cb_frame/settings/general-page.php:187
#: src/view/admin/cb_frame/settings/general-page.php:239
#: src/view/admin/cb_frame/settings/general-page.php:283
#: src/view/admin/uc_frame/settings/general-page.php:153
#: src/view/admin/uc_frame/settings/general-page.php:207
msgid "Disabled by active setting in Network Settings"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:125
msgid "Ignore scripts in queue from Cookiebot CMP scan:"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:128
msgid "List scripts source URL (one per line) from the queue to ignore Cookiebot CMP scan. Partial source URL will also work, e.g. wp-content/plugins/woocommerce will block every WooCommerce script."
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:131
msgid "This feature only works for scripts loaded via wp_enqueue_script. Manually added scripts must be manually edited."
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:137
msgid "Script source URL:"
msgstr ""

#: src/view/admin/cb_frame/settings/additional-page.php:143
msgid "Add script source URL, one per line"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:14
msgid "What is Google Consent Mode and why should you enable it?"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:17
msgid "Google Consent Mode is a way for your website to measure conversions and get analytics insights while being fully GDPR-compliant when using services like Google Analytics, Google Tag Manager (GTM) and Google Ads."
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:20
msgid "Cookiebot consent managment platform (CMP) and Google Consent Mode integrate seamlessly to offer you plug-and-play compliance and streamlined use of all Google's services in one easy solution."
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:24
msgid "Read more about Cookiebot CMP and Google Consent Mode"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:31
msgid "Google Consent Mode:"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:34
msgid "Enable Google Consent Mode with default settings on your WordPress page."
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:38
#: src/view/admin/cb_frame/settings/gcm-page.php:68
#: src/view/admin/cb_frame/settings/gtm-page.php:18
msgid "Read more"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:61
msgid "URL passthrough:"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:64
msgid "This feature will allow you to pass data between pages when not able to use cookies without/prior consent."
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:80
msgid "URL passthrough"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:89
msgid "Google Consent Mode cookies"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:91
msgid "Select the cookie types that need to be consented for the Google Consent Mode script"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:93
#: src/view/admin/cb_frame/settings/general-page.php:212
#: src/view/admin/cb_frame/settings/general-page.php:251
#: src/view/admin/cb_frame/settings/gtm-page.php:80
#: src/view/admin/uc_frame/settings/general-page.php:175
msgid "This feature is only available when using Manual Blocking"
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:95
#: src/view/admin/cb_frame/settings/gtm-page.php:82
msgid "This option may affect the behaviour of your GTM Tags, as the script will run on the selected cookies consent."
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:97
#: src/view/admin/cb_frame/settings/gtm-page.php:84
msgid "Please make sure your Tags in Google Tag Manager are triggered correctly."
msgstr ""

#: src/view/admin/cb_frame/settings/gcm-page.php:102
#: src/view/admin/cb_frame/settings/gtm-page.php:89
#: src/view/admin/common/prior-consent/available-addons/tab.php:28
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:25
msgid "Check one or multiple cookie types:"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:30
msgid "Connect your Domain Group"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:33
msgid "To connect your Domain Group, paste your Domain Group ID here. If you want to connect a second ID for other regions, you can do this under the \"Multiple Configurations\" tab."
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:54
#: src/view/admin/uc_frame/settings/general-page.php:48
msgid "Using network account"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:64
#: src/view/admin/uc_frame/settings/general-page.php:59
msgid "Do not use Network Settings ID"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:75
msgid "Language:"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:77
msgid "Select your main language here."
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:82
msgid "Select the language"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:86
msgid "Default (Autodetect)"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:90
msgid "Use WordPress Language"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:111
msgid "If enabled, Cookiebot™ will use the current location to set the banner and cookie declaration language."
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:114
msgid "Please make sure that all languages in use have been added in the Cookiebot™ Manager."
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:116
msgid "This feature disables the main language selector."
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:117
msgid "If you have already set a language in the cookie declaration shortcode, this feature will not change it."
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:120
msgid "Read more on how to add languages"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:135
msgid "Use website location to set language"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:149
#: src/view/admin/uc_frame/network-settings-page.php:113
#: src/view/admin/uc_frame/settings/general-page.php:123
msgid "Choose the type of your cookie-blocking mode. Select automatic to automatically block all cookies except those strictly necessary to use before user gives consent. Manual mode lets you adjust your cookie settings within your website’s HTML."
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:154
msgid "Guide to auto cookie-blocking"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:160
msgid "Guide to manual cookie-blocking"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:184
#: src/view/admin/uc_frame/settings/general-page.php:150
msgid "Manual"
msgstr ""

#: src/view/admin/cb_frame/settings/general-page.php:209
msgid "Add async or defer attribute to cookie declaration script tag"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:12
msgid "Google Tag Manager:"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:14
msgid "For more details about Cookiebot CMP and Google Tag Manager."
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:35
#: src/view/admin/cb_frame/settings/gtm-page.php:46
#: src/view/admin/uc_frame/settings/gtm-page.php:35
#: src/view/admin/uc_frame/settings/gtm-page.php:46
msgid "Google Tag Manager ID"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:37
msgid "Paste your Tag Manager ID into the field on the right."
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:41
#: src/view/admin/uc_frame/settings/gtm-page.php:41
msgid "How to find the GTM ID"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:48
msgid "Enter GTM ID"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:56
msgid "Data Layer Name (optional)"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:58
msgid "You can also paste your Data Layer Name here. This is optional information."
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:62
msgid "How to find the Data Layer Name"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:67
#: src/view/admin/uc_frame/settings/gtm-page.php:67
msgid "Name of your Data Layer"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:76
msgid "Google Tag Manager cookies"
msgstr ""

#: src/view/admin/cb_frame/settings/gtm-page.php:78
msgid "Select the cookie types that need to be consented for the Google Tag Manager script"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:26
msgid "IAB Integration:"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:28
msgid "If you want to use the IAB Framework TCF within your Consent Management Platform (CMP) you can enable it on the right. Be aware that activating this could override some of the configurations you made with the default setup defined by the IAB."
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:32
msgid "Read more on IAB with Cookiebot CMP here"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:41
msgid "IAB TCF V2.2 integration"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:83
msgid "IAB vendor list is temporarily offline. Please try refreshing the page after a couple of minutes."
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:85
msgid "If you had previously saved configurations, don’t worry, they will continue to work."
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:108
msgid "Sharing data with third-party vendors"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:112
msgid "Select vendors with whom you’ll share users’ data. We’ll include this information on the second layer of your consent banner, where users interested in more granular detail about who will access their data can view it."
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:130
#: src/view/admin/cb_frame/settings/iab-page.php:162
#: src/view/admin/cb_frame/settings/iab-page.php:218
msgid "Search"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:131
#: src/view/admin/cb_frame/settings/iab-page.php:163
msgid "Select All"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:132
#: src/view/admin/cb_frame/settings/iab-page.php:164
msgid "Deselect All"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:133
msgid "Select at least one vendor"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:157
msgid "Google Ads certified external vendors"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:188
msgid "Restrictions of data use purposes for vendors"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:192
msgid "Set restrictions on data use purposes for specific vendors. Add vendors and the data use purposes that each vendor is allowed. We’ll share this information with users within your consent banner."
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:198
msgid "Add Vendor"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:204
#: src/view/admin/cb_frame/settings/iab-page.php:215
msgid "Select Vendor"
msgstr ""

#: src/view/admin/cb_frame/settings/iab-page.php:228
msgid "Set Purposes"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/extra/region-item.php:18
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:75
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:101
msgid "Select region"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:16
msgid "Additional configurations:"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:18
msgid "You can add a second alternative banner or configuration to your website by creating a second Domain Group and specify it on a region."
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:22
msgid "Read more about multiple configurations here"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:34
msgid "Multiple configurations"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:44
msgid "Set up your additional banner configuration:"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:47
msgid "To enable a different configuration, create a separate DomainGroup without adding the domain to it and paste the ID below. Then select the countries in which you want to show this configuration. For example, if your main Domain Group is defined as a banner matching GDPR requirements, you might want to add another Domain Group for visitors from California. The number of additional configurations is restricted to one at the moment."
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:57
msgid "Domain Group ID"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:61
msgid "Region"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:71
msgid "Primary domain group"
msgstr ""

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:138
msgid "Add banner"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:23
msgid "Need help with your configuration?"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:26
msgid "In our Help Center you find all the answers to your questions. If you have additional questions, create a support request and our Support Team will help out as soon as possible."
msgstr ""

#: src/view/admin/cb_frame/support-page.php:34
msgid "Visit Cookiebot CMP Help Center"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:58
msgid "How to find my Cookiebot™ ID"
msgstr ""

#. translators: the first placeholder string will be replaced with a html anchor open tag and the second placeholder string will be replaced by the html anchor closing tag
#: src/view/admin/cb_frame/support-page.php:66
msgid "Log in to your %1$sCookiebot CMP account%2$s."
msgstr ""

#. translators: the placeholder strings denote the positions of <b>, </b>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:76
msgid "Go to %1$s\"Settings\"%2$s and setup your Cookiebot CMP"
msgstr ""

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:86
msgid "Go to the %1$s\"Your scripts\"%2$s tab"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:92
msgid "Copy the value inside the data-cid parameter - eg.: abcdef12-3456-7890-abcd-ef1234567890"
msgstr ""

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:97
msgid "Add %1$s[cookie_declaration]%2$s shortcode to a page to show the declaration"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:103
msgid "Remember to change your scripts as described below"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:110
msgid "Add the Cookie Declaration to your website"
msgstr ""

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:116
msgid "Use the shortcode %1$s[cookie_declaration]%2$s to add the cookie declaration to a page or post. The cookie declaration will always show the latest version from Cookiebot CMP."
msgstr ""

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:126
msgid "If you want to show the cookie declaration in a specific language, you can add the %1$s\"lang\"%2$s attribute, e.g. %3$s[cookie_declaration lang=\"de\"]%4$s."
msgstr ""

#: src/view/admin/cb_frame/support-page.php:138
msgid "Update your script tags"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:142
msgid "To enable prior consent, apply the attribute \"data-cookieconsent\" to cookie-setting script tags on your website. Set the comma-separated value to one or more of the cookie categories \"preferences\", \"statistics\" and/or \"marketing\" in accordance with the types of cookies being set by each script. Finally, change the attribute \"type\" from \"text/javascript\" to \"text/plain\"."
msgstr ""

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:152
msgid "Example on modifying an existing Google Analytics Universal script tag can be found %1$shere in step 4%2$s."
msgstr ""

#: src/view/admin/cb_frame/support-page.php:172
msgid "Helper function to update your scripts"
msgstr ""

#: src/view/admin/cb_frame/support-page.php:176
msgid "You can update your scripts yourself. However, Cookiebot CMP also offers a small helper function that can make the work easier."
msgstr ""

#: src/view/admin/cb_frame/support-page.php:183
msgid "Update your script tags this way:"
msgstr ""

#. translators: %1$s refers to the original script tag HTML, and %2$s refers to its replacement
#: src/view/admin/cb_frame/support-page.php:188
msgid "%1$s to %2$s"
msgstr ""

#: src/view/admin/common/dashboard-page.php:31
msgid "I already have an account"
msgstr ""

#: src/view/admin/common/dashboard-page.php:35
msgid "Connect my account"
msgstr ""

#: src/view/admin/common/dashboard-page.php:48
#: src/view/admin/common/network-settings-page.php:33
#: src/view/admin/common/settings-page.php:37
msgid "New to our solutions? Create your account. "
msgstr ""

#: src/view/admin/common/dashboard-page.php:65
msgid "How to get started"
msgstr ""

#: src/view/admin/common/dashboard-page.php:69
msgid "Learn more about your CMP"
msgstr ""

#: src/view/admin/common/network-settings-page.php:35
#: src/view/admin/common/settings-page.php:39
msgid "If you’re new to our solutions, create an account first to obtain your settings ID."
msgstr ""

#: src/view/admin/common/network-settings-page.php:40
#: src/view/admin/common/settings-page.php:44
msgid "Create your account"
msgstr ""

#: src/view/admin/common/network-settings-page.php:49
#: src/view/admin/common/settings-page.php:52
#: src/view/admin/uc_frame/network-settings-page.php:51
msgid "Connect your account"
msgstr ""

#: src/view/admin/common/network-settings-page.php:52
#: src/view/admin/common/settings-page.php:55
msgid "Enter the ID of your account to quickly connect it with the plugin."
msgstr ""

#: src/view/admin/uc_frame/network-settings-page.php:54
msgid "Enter your settings ID to quickly link your account with the plugin."
msgstr ""

#: src/view/admin/common/network-settings-page.php:55
#: src/view/admin/uc_frame/network-settings-page.php:57
msgid "If added this will be the default account for all subsites. Subsites are able to override this and use their own account."
msgstr ""

#: src/view/admin/uc_frame/network-settings-page.php:61
#: src/view/admin/uc_frame/settings/general-page.php:29
msgid "Where to find settings ID"
msgstr ""

#: src/view/admin/common/network-settings-page.php:63
#: src/view/admin/common/settings-page.php:63
msgid "How to find your Usercentrics Settings ID"
msgstr ""

#: src/view/admin/common/network-settings-page.php:70
#: src/view/admin/common/settings-page.php:70
msgid "How to find your Cookiebot CMP Domain Group ID"
msgstr ""

#: src/view/admin/common/network-settings-page.php:65
#: src/view/admin/common/settings-page.php:65
msgid "Settings ID or Domain Group ID"
msgstr ""

#: src/view/admin/uc_frame/network-settings-page.php:67
#: src/view/admin/uc_frame/settings/general-page.php:35
msgid "Your settings ID"
msgstr ""

#: src/view/admin/common/network-settings-page.php:82
#: src/view/admin/common/settings-page.php:82
#: src/view/admin/uc_frame/network-settings-page.php:83
#: src/view/admin/uc_frame/settings/general-page.php:70
msgid "Let us know if your account is set for compliance with a single privacy law (e.g. GDPR) or multiple laws (e.g. GDPR and CCPA) based on user’s location. The default is a single privacy law, so this is likely your setting unless modified."
msgstr ""

#: src/view/admin/common/network-settings-page.php:88
#: src/view/admin/common/settings-page.php:88
#: src/view/admin/uc_frame/network-settings-page.php:89
#: src/view/admin/uc_frame/settings/general-page.php:76
msgid "Your current account setup:"
msgstr ""

#: src/view/admin/common/network-settings-page.php:95
#: src/view/admin/common/settings-page.php:95
#: src/view/admin/uc_frame/network-settings-page.php:96
#: src/view/admin/uc_frame/settings/general-page.php:83
msgid "Compliance with one privacy law"
msgstr ""

#: src/view/admin/common/network-settings-page.php:102
#: src/view/admin/common/settings-page.php:102
#: src/view/admin/uc_frame/network-settings-page.php:103
#: src/view/admin/uc_frame/settings/general-page.php:90
msgid "Compliance with multiple privacy laws (geolocation)"
msgstr ""

#: src/view/admin/common/prior-consent/available-addons/tab.php:25
msgid "Plugin enabled"
msgstr ""

#: src/view/admin/common/prior-consent/available-addons/tab.php:58
msgid "Placeholder text:"
msgstr ""

#: src/view/admin/common/prior-consent/available-addons/tab.php:67
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:53
msgid "Display a placeholder"
msgstr ""

#: src/view/admin/common/prior-consent/available-addons/tab.php:78
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:71
msgid "+ Add language"
msgstr ""

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:7
msgid "Jetpack settings"
msgstr ""

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:8
msgid "Enable Jetpack on \"Available Addons\" to see this page options."
msgstr ""

#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:15
msgid "Enable"
msgstr ""

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:11
msgid "Show advanced options"
msgstr ""

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:17
msgid "This is for more advanced users."
msgstr ""

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:23
msgid "Regex:"
msgstr ""

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:35
msgid "Edit regex"
msgstr ""

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:49
msgid "Reset to default regex"
msgstr ""

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:4
msgid "Information"
msgstr ""

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:5
msgid "These add-ons are created by a dedicated open-source community to make it easier for you to manage cookie and tracker consent on your WordPress site. They’re designed to help you ensure ‘prior consent’ even for plugins that don’t include this feature."
msgstr ""

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:6
msgid "Right now, these add-ons are the best way for you to signal user consent to other plugins. While we don’t know if or when WordPress Core will add this functionality, these tools are here to support you and work seamlessly with Usercentrics solution."
msgstr ""

#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:10
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:19
#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:26
msgid "Language"
msgstr ""

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:4
msgid "Unavailable plugins"
msgstr ""

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:5
msgid "The following addons are unavailable. This is because the corresponding plugin is not installed or activated."
msgstr ""

#: src/view/admin/common/support-page.php:23
#: src/view/admin/uc_frame/support-page.php:23
msgid "Need help?"
msgstr ""

#: src/view/admin/common/support-page.php:26
#: src/view/admin/uc_frame/support-page.php:26
msgid "Visit our Support Center to find answers to your questions or get help with configuration. If you need further assistance, use the Contact Support button in the top navigation to create a support request. We’ll respond as soon as possible."
msgstr ""

#: src/view/admin/common/support-page.php:34
#: src/view/admin/uc_frame/support-page.php:34
msgid "Go to Support Center"
msgstr ""

#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:6
msgid "You will need to add a new ID before updating other settings"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:10
msgid "Cookiebot CMP Deactivation"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:14
msgid "We are sad to lose you. Take a moment to help us improve?"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:19
msgid "The installation is too complicated"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:26
msgid "I found a plugin that better serves my needs"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:33
msgid "Missing features / did not meet my expectations"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:40
msgid "I need more customization options"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:47
msgid "The premium plan is too expensive"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:54
msgid "I’m only deactivating the plugin temporarily"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:61
msgid "Other"
msgstr ""

#: src/view/admin/common/templates/extra/review-form.php:65
msgid "Please specify here"
msgstr ""

#: src/lib/Cookiebot_Review.php:105
msgid "(Optional)"
msgstr ""

#: src/lib/Cookiebot_Review.php:106
msgid " By checking this box, you agree to submit troubleshooting information and allow us to contact you regarding the problem if necessary."
msgstr ""

#: src/lib/Cookiebot_Review.php:110
msgid "The information will be kept for no longer than 90 days. You may revoke this consent at any time, e.g. by sending an email to "
msgstr ""

#: src/lib/Cookiebot_Review.php:117
msgid "Skip and Deactivate"
msgstr ""

#: src/lib/Cookiebot_Review.php:118
msgid "Submit and Deactivate"
msgstr ""

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:6
msgid "If there is a network settings ID connected it will be used for this subsite, if not you will need to add a new ID before updating other settings"
msgstr ""

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:13
msgid "Disconnect subsite account"
msgstr ""

#: src/view/admin/common/templates/header.php:10
msgid "Changes has been saved"
msgstr ""

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:13
msgid "Absolutely, you deserve it!"
msgstr ""

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:24
msgid "Maybe later?"
msgstr ""

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:35
msgid "I`ve already left my feedback"
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:30
msgid "Welcome to Usercentrics Cookiebot WordPress Plugin"
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:38
msgid "You’ve added your settings ID to your Usercentrics Cookiebot WordPress Plugin."
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:51
msgid "Are you happy with Usercentrics Cookiebot WordPress Plugin? Your feedback helps us improve it."
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:55
msgid "Share feedback"
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:68
msgid "How to set up Usercentrics Cookiebot WordPress Plugin"
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:72
#: src/view/admin/uc_frame/settings/gcm-page.php:21
#: src/view/admin/uc_frame/settings/general-page.php:127
#: src/view/admin/uc_frame/settings/gtm-page.php:18
msgid "Learn more"
msgstr ""

#: src/view/admin/uc_frame/dashboard-page.php:115
msgid "Visit our blog to learn about other legislations"
msgstr ""

#: src/view/admin/uc_frame/debug-page.php:22
msgid "Debug your plugin"
msgstr ""

#: src/view/admin/uc_frame/debug-page.php:25
msgid "If you encounter any issues with your Usercentrics Cookiebot WordPress Plugin, provide the information below to help us assist you. Visit our Support Center and send us a copy of what is displayed in the window below."
msgstr ""

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:23
msgid "WP Consent API Settings"
msgstr ""

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:32
msgid "WP Consent API and Usercentrics Cookiebot WordPress Plugin categorize cookies a bit differently. The default settings should fit most needs, but if you need to change the mapping you can do so below."
msgstr ""

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:48
msgid "Usercentrics Cookiebot cookie categories"
msgstr ""

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:58
msgid "essential"
msgstr ""

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:72
msgid "functional"
msgstr ""

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:106
msgid "Reset to default categories"
msgstr ""

#: src/view/admin/uc_frame/settings-page.php:48
msgid "Save changes"
msgstr ""

#: src/view/admin/uc_frame/settings-page.php:70
#: src/view/admin/uc_frame/settings/embeddings-page.php:4
msgid "Privacy Policy Sync"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:7
msgid "Use our pre-defined, automatically generated embeddings to help you keep your Privacy Policy page in sync with your consent banner settings. This feature saves you time by automatically updating legally required information, so you don’t need to manually copy data into your Privacy Policy page. Once you’re done setting the options below, simply copy the code and paste it into your Privacy Policy page."
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:12
msgid "Copy shortcode"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:23
msgid "Sync options for privacy legislations"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:26
msgid "Select the legislation you want to automatically sync with your Privacy Policy page."
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:41
msgid "Sync options for data processing services (DPS) "
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:44
msgid "Define what to include on your Privacy Policy page: DPS categories only, categories with their services, a single service, or detailed information on both categories and services. Choose based on the level of detail you want to display."
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:50
msgid "Services (Default)"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:51
msgid "Categories and services"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:52
msgid "Categories only"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:53
msgid "Single service"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:54
msgid "Purposes"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:55
msgid "Vendors"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:63
msgid "Single Service ID"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:66
msgid "Add the service ID that you want to display."
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:69
msgid "This feature is required."
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:81
msgid "Privacy toggles"
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:84
msgid "Define whether you want the privacy toggles to be enabled and displayed on your Privacy Policy page."
msgstr ""

#: src/view/admin/uc_frame/settings/embeddings-page.php:92
msgid "Enable privacy toggles"
msgstr ""

#: src/view/admin/uc_frame/settings/gcm-page.php:14
msgid "Integration with Google Consent Mode"
msgstr ""

#: src/view/admin/uc_frame/settings/gcm-page.php:17
msgid "The Usercentrics Cookiebot WordPress Plugin and Google Consent Mode integrate seamlessly, providing plug-and-play privacy compliance and effortless use of all Google services in one solution."
msgstr ""

#: src/view/admin/uc_frame/settings/gcm-page.php:31
msgid "Enable Google Consent Mode integration within your Usercentrics Cookiebot WordPress Plugin."
msgstr ""

#: src/view/admin/uc_frame/settings/general-page.php:22
msgid "Settings ID"
msgstr ""

#: src/view/admin/uc_frame/settings/general-page.php:25
msgid "To disconnect your account, enter your settings ID into the field and confirm with the button."
msgstr ""

#: src/view/admin/uc_frame/settings/general-page.php:99
msgid "TCF integration"
msgstr ""

#: src/view/admin/uc_frame/settings/general-page.php:102
msgid "Enable the integration with the latest version of IAB TCF."
msgstr ""

#: src/view/admin/uc_frame/settings/general-page.php:111
msgid "IAB TCF integration"
msgstr ""

#: src/view/admin/uc_frame/settings/gtm-page.php:14
msgid "Enable Google Tag Manager integration to streamline tracking tags with your Usercentrics Cookiebot WordPress Plugin."
msgstr ""

#: src/view/admin/uc_frame/settings/gtm-page.php:37
msgid "Enter your Google Tag Manager ID for seamless integration."
msgstr ""

#: src/view/admin/uc_frame/settings/gtm-page.php:48
msgid "GTM-XXXXXXX"
msgstr ""

#: src/view/admin/uc_frame/settings/gtm-page.php:56
msgid "Data layer name (only if changed)"
msgstr ""

#: src/view/admin/uc_frame/settings/gtm-page.php:58
msgid "The default name for the data layer in Google Tag Manager is ‘dataLayer’. If you renamed it, enter the new name. Otherwise, leave this field empty."
msgstr ""

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:14
msgid "Title"
msgstr ""

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:33
msgid "- Default -"
msgstr ""

#: src/widgets/Cookiebot_Declaration_Widget.php:17
msgid "Cookiebot - Cookie Declaration"
msgstr ""

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:23
msgid "Cookiebot Status"
msgstr ""

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:39
msgid "You need to enter your Cookiebot ID."
msgstr ""

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:41
msgid "Update your Cookiebot ID"
msgstr ""

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:44
msgid "Your Cookiebot is working!"
msgstr ""
