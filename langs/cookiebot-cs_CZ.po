msgid ""
msgstr ""
"Project-Id-Version: Cookiebot | GDPR/CCPA Compliant <PERSON><PERSON> and "
"Control\n"
"Report-Msgid-Bugs-To: https://wordpress.org/support/plugin/cookiebot\n"
"POT-Creation-Date: 2024-12-18T18:13:49+00:00\n"
"PO-Revision-Date: 2025-01-22 16:40+0100\n"
"Last-Translator: \n"
"Language-Team: \n"
"Language: cs_CZ\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n>=2 && n<=4 ? 1 : 2);\n"
"X-Generator: Poedit 3.4.4\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-Flags-xgettext: --add-comments=translators:\n"
"X-Poedit-WPHeader: cookiebot.php\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.min.js\n"
"X-Poedit-SearchPathExcluded-1: vendor\n"

#. Plugin Name of the plugin
#: cookiebot.php
msgid "Cookie banner plugin for WordPress – Cookiebot CMP by Usercentrics"
msgstr ""

#. Plugin URI of the plugin
#. Author URI of the plugin
#: cookiebot.php
msgid "https://www.cookiebot.com/"
msgstr ""

#. Description of the plugin
#: cookiebot.php
msgid ""
"The Cookiebot CMP WordPress cookie banner and cookie policy help you comply "
"with the major data protection laws (GDPR, ePrivacy, CCPA, LGPD, etc.) in a "
"simple and fully automated way. Secure your website and get peace of mind."
msgstr ""

#. Author of the plugin
#: cookiebot.php
msgid "Usercentrics A/S"
msgstr ""

#: src/addons/config/Settings_Config.php:89
#: src/addons/config/Settings_Config.php:90
#: src/view/admin/common/prior-consent/page.php:24
#: src/view/admin/common/templates/main-tabs.php:39
msgid "Plugins"
msgstr "Pluginy"

#: src/addons/config/Settings_Config.php:130
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:25
msgid "Remove language"
msgstr "Odstranit jazyk"

#: src/addons/config/Settings_Config.php:602
msgid "The plugin is not installed."
msgstr "Plugin není nainstalován."

#: src/addons/config/Settings_Config.php:605
msgid "The theme is not installed."
msgstr "Šablona není nainstalována."

#: src/addons/config/Settings_Config.php:609
msgid "The plugin is not activated."
msgstr "Plugin není aktivován."

#: src/addons/config/Settings_Config.php:612
msgid "The theme is not activated."
msgstr "Šablona není aktivována."

#: src/addons/config/Settings_Config.php:672
#: src/lib/Cookiebot_Admin_Links.php:54 src/lib/Cookiebot_Admin_Links.php:56
#: src/lib/Cookiebot_Admin_Links.php:124
msgid "%s"
msgstr "%s"

#: src/addons/config/Settings_Config.php:708
#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:18
#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:19
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:40
msgid "Info"
msgstr "Informace"

#: src/addons/config/Settings_Config.php:715
msgid "Available Add-ons"
msgstr "Dostupné doplňky"

#: src/addons/config/Settings_Config.php:721
msgid "Unavailable Add-ons"
msgstr "Nedostupné doplňky"

#: src/addons/config/Settings_Config.php:738
msgid "Jetpack"
msgstr "Jetpack"

#: src/addons/config/Settings_Config.php:749
msgid "WP Consent API"
msgstr "WP Consent API"

#: src/addons/controller/addons/add_to_any/Add_To_Any.php:86
#: src/addons/controller/addons/embed_autocorrect/Embed_Autocorrect.php:443
msgid "Blocks embedded videos from Youtube, Twitter, Vimeo and Facebook."
msgstr "Blokuje vložená videa ze služeb YouTube, Twitter, Vimeo a Facebook."

#: src/addons/controller/addons/enfold/Enfold.php:29
msgid "Blocks cookies created by Enfold theme's Google Services."
msgstr ""
"Blokuje soubory cookie vytvořené šablonou Enfold využivající služby Google."

#: src/addons/controller/addons/enhanced_ecommerce_for_woocommerce_store/Enhanced_Ecommerce_For_WooCommerce_Store.php:43
msgid "Blocks enhanced e-commerce for WooCommerce store"
msgstr "Blokuje enhanced e-commerce v obchodě WooCommerce"

#: src/addons/controller/addons/google_analytics/Google_Analytics.php:36
msgid ""
"Google Analytics is used to track how visitor interact with website content."
msgstr ""
"Služba Google Analytics slouží k měření interakcí návštěvníků s obsahem "
"webových stránek."

#: src/addons/controller/addons/google_analytics_plus/Google_Analytics_Plus.php:32
msgid ""
"Google Analytics is a simple, easy-to-use tool that helps website owners "
"measure how users interact with website content"
msgstr ""
"Google Analytics je jednoduchý, snadno použitelný nástroj, který pomáhá "
"majitelům webových stránek měřit, jak uživatelé interagují s obsahem těchto "
"stránek"

#: src/addons/controller/addons/google_site_kit/Google_Site_Kit.php:33
msgid "Blocks Google Analytics scripts"
msgstr "Blokuje skripty Google Analytics"

#: src/addons/controller/addons/jetpack/widget/Facebook_Jetpack_Widget.php:71
msgid "Facebook widget."
msgstr "Widget pro Facebook."

#: src/addons/controller/addons/litespeed_cache/Litespeed_Cache.php:51
msgid ""
"Excludes cookiebot javascript files when the Litespeed Cache deter option is "
"enabled."
msgstr ""
"Pokud je v pluginu Litespeed Cache povolena možnost „deter“, vyloučí soubory "
"javascript pluginu Cookiebot."

#: src/addons/controller/addons/official_facebook_pixel/Official_Facebook_Pixel.php:247
msgid "Blocks Official Meta Pixel scripts"
msgstr "Blokuje skripty pluginu Official Meta Pixel"

#: src/addons/controller/addons/optinmonster/Optinmonster.php:32
msgid ""
"OptinMonster API plugin to connect your WordPress site to your OptinMonster "
"account."
msgstr ""
"Plugin OptinMonster API pro připojení webu využívajícího systém WordPress k "
"vašemu účtu OptinMonster."

#: src/addons/controller/addons/simple_share_buttons_adder/Simple_Share_Buttons_Adder.php:32
msgid "Blocks Simple Share Buttons Adder."
msgstr "Blokuje plugin Simple Share Buttons Adder."

#: src/addons/controller/addons/wpforms/Wpforms.php:61
msgid ""
"If the user gives correct consent, IP and Unique User ID will be saved on "
"form submissions, otherwise not."
msgstr ""
"Pokud uživatel udělí správný souhlas, v odeslaných formulářích se uloží "
"adresa IP a jedinečné ID uživatele, v opačném případě ne."

#: src/addons/controller/addons/wpforms/Wpforms.php:62
msgid "Increases opt-in rate compared to WPForms \"GDPR mode\"."
msgstr "Zvyšuje míru přihlášení ve srovnání s „režimem GDPR“ pluginu WPForms."

#: src/addons/controller/addons/wp_google_analytics_events/Wp_Google_Analytics_Events.php:46
msgid ""
"The plugin allows you to fire events whenever someone interacts or views "
"elements on your website."
msgstr ""
"Tento plugin vám umožňuje spouštět události, kdykoli si někdo zobrazí prvky "
"na vašem webu nebo s nimi interaguje."

#: src/addons/controller/addons/wp_rocket/Wp_Rocket.php:48
msgid ""
"Excludes cookiebot javascript files when the WP-Rocket deter option is "
"enabled."
msgstr ""
"Pokud je v pluginu WP-Rocket povolena možnost „deter“, vyloučí soubory "
"javascript pluginu Cookiebot."

#: src/addons/controller/addons/wp_seopress/Wp_Seopress.php:41
msgid "Blocks cookies from WP SEOPress' Google Analytics integration."
msgstr "Blokuje soubory cookie z integrace WP SEOPress s Google Analytics."

#: src/addons/controller/Plugin_Controller.php:50
msgid "You enabled Cookiebot™ auto blocking mode but still using addons"
msgstr ""
"Povolili jste režim automatického blokování pluginu Cookiebot™, ale nadále "
"používáte doplňky"

#: src/addons/controller/Plugin_Controller.php:55
msgid ""
"In some occasions this may cause client side errors. If you notice any "
"errors please try to disable Cookiebot™ addons or contact Cookiebot™ support."
msgstr ""
"V některých případech může dojít k chybám na straně klienta. Pokud nějaké "
"chyby zaznamenáte, zkuste zakázat doplňky Cookiebot™ nebo se obraťte na "
"podporu Cookiebot™."

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:84
msgid "Share your experience"
msgstr "Podělte se o své zkušenosti"

#: src/admin_notices/Cookiebot_Recommendation_Notice.php:88
msgid ""
"Hi there! We are thrilled you love the Cookiebot CMP plugin. Could you do us "
"a huge favor and leave a 5-star rating on WordPress? Your support will help "
"us spread the word and empower more WordPress websites to meet GDPR and CCPA "
"compliance standards effortlessly. Thank you for your support!"
msgstr ""
"Dobrý den! Jsme nadšeni, že se vám líbí plugin Cookiebot CMP. Mohl byste nám "
"udělat obrovskou laskavost a zanechat na WordPressu 5hvězdičkové hodnocení? "
"Vaše podpora nám pomůže šířit informace a umožní více webům WordPress, aby "
"splňovaly soulad s GDPR a CCPA standardy bez námahy. Děkujeme za vaši "
"podporu!"

#: src/admin_notices/Cookiebot_Temp_Notice.php:43
msgid ""
"Cookiebot CMP Plugin will soon no longer support PHP 5. If your website "
"still runs on this version we recommend upgrading so you can continue "
"enjoying the features Cookiebot CMP offers."
msgstr ""
"Plugin Cookiebot CMP přestane brzy podporovat skripty PHP 5. Pokud váš web "
"dosud funguje na této verzi, doporučujeme provést upgrade, abyste mohli "
"v používání funkcí pluginu Cookiebot CMP pokračovat."

#: src/lib/Cookiebot_Review.php:82
msgid "Sorry you are not allowed to do this."
msgstr "Tohle bohužel nemůžete."

#: src/lib/Cookiebot_Review.php:85
#: src/view/admin/common/templates/extra/review-form.php:94
msgid "Please select one option"
msgstr "Vyberte jednu možnost"

#. translators: The placeholder is for the COOKIEBOT_MIN_PHP_VERSION constant
#: src/lib/Cookiebot_WP.php:66
msgid "The Cookiebot plugin requires PHP version %s or greater."
msgstr "Plugin Cookiebot™ vyžaduje PHP verzi %s nebo vyšší."

#: src/lib/Cookiebot_WP.php:242 src/settings/pages/Dashboard_Page.php:35
#: src/view/admin/common/templates/main-tabs.php:21
msgid "Dashboard"
msgstr "Ovládací panel"

#: src/lib/helper.php:245 src/lib/helper.php:343
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:91
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:167
#: src/view/admin/cb_frame/settings/gcm-page.php:126
#: src/view/admin/cb_frame/settings/gtm-page.php:113
#: src/view/admin/common/prior-consent/available-addons/tab.php:55
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:88
msgid "marketing"
msgstr "marketing"

#: src/lib/helper.php:248 src/lib/helper.php:342
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:88
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:134
#: src/view/admin/cb_frame/settings/gcm-page.php:118
#: src/view/admin/cb_frame/settings/gtm-page.php:105
#: src/view/admin/common/prior-consent/available-addons/tab.php:46
msgid "statistics"
msgstr "statistika"

#: src/lib/helper.php:251 src/lib/helper.php:341
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:85
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:118
#: src/view/admin/cb_frame/settings/gcm-page.php:110
#: src/view/admin/cb_frame/settings/gtm-page.php:97
#: src/view/admin/common/prior-consent/available-addons/tab.php:37
msgid "preferences"
msgstr "předvolby"

#: src/lib/helper.php:254
#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:82
msgid "necessary"
msgstr "nezbytné"

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:367
msgid "Please accept [renew_consent]%cookie_types[/renew_consent] cookies."
msgstr "Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:372
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"tracking."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] soubory cookie, "
"abyste umožnili sledování."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:377
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Social Share buttons."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] soubory cookie, "
"abyste mohli povolit tlačítka pro sdílení na sociálních sítích."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:382
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to view "
"this element."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies pro "
"zobrazení tohoto prvku."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:387
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to watch "
"this video."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies pro "
"sledování tohoto videa."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:392
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Services."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies, abyste "
"mohli využívat služby Google."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:397
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"facebook shopping feature."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies, abyste "
"mohli používat funkci facebook shopping."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:402
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to track "
"for google analytics."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies pro "
"sledování pro Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:407
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Google Analytics."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies, abyste "
"mohli používat službu Google Analytics."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:412
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"instagram feed."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies pro "
"povolení instagramového feedu."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:417
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"Facebook Pixel."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies pro "
"povolení Facebook Pixel."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:422
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to Social "
"Share buttons."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies k "
"tlačítkům pro sdílení na sociálních sítích."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:427
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to allow "
"Matomo statistics."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies, abyste "
"umožnili statistiky Matomo."

#. translators: %cookie_types refers to the list of cookie types assigned to the addon placeholder
#: src/lib/helper.php:432
msgid ""
"Please accept [renew_consent]%cookie_types[/renew_consent] cookies to enable "
"saving user information."
msgstr ""
"Přijměte prosím [renew_consent]%cookie_types[/renew_consent] cookies, abyste "
"mohli ukládat informace o uživateli."

#: src/lib/Supported_Languages.php:8
msgid "Norwegian Bokmål"
msgstr "Norština Bokmål"

#: src/lib/Supported_Languages.php:9
msgid "Turkish"
msgstr "Turečtina"

#: src/lib/Supported_Languages.php:10
msgid "German"
msgstr "Němčina"

#: src/lib/Supported_Languages.php:11
msgid "Czech"
msgstr "Čeština"

#: src/lib/Supported_Languages.php:12
msgid "Danish"
msgstr "Dánština"

#: src/lib/Supported_Languages.php:13
msgid "Albanian"
msgstr "Albánština"

#: src/lib/Supported_Languages.php:14
msgid "Hebrew"
msgstr "Hebrejština"

#: src/lib/Supported_Languages.php:15
msgid "Korean"
msgstr "Korejština"

#: src/lib/Supported_Languages.php:16
msgid "Italian"
msgstr "Italština"

#: src/lib/Supported_Languages.php:17
msgid "Dutch"
msgstr "Holandština"

#: src/lib/Supported_Languages.php:18
msgid "Vietnamese"
msgstr "Vietnamština"

#: src/lib/Supported_Languages.php:19
msgid "Tamil"
msgstr "Tamilština"

#: src/lib/Supported_Languages.php:20
msgid "Icelandic"
msgstr "Islandština"

#: src/lib/Supported_Languages.php:21
msgid "Romanian"
msgstr "Rumunština"

#: src/lib/Supported_Languages.php:22
msgid "Sinhala"
msgstr "Sinhálština"

#: src/lib/Supported_Languages.php:23
msgid "Catalan"
msgstr "Katalánština"

#: src/lib/Supported_Languages.php:24
msgid "Bulgarian"
msgstr "Bulharština"

#: src/lib/Supported_Languages.php:25
msgid "Ukrainian"
msgstr "Ukrajinština"

#: src/lib/Supported_Languages.php:26
msgid "Chinese"
msgstr "Čínština"

#: src/lib/Supported_Languages.php:27
msgid "English"
msgstr "Angličtina"

#: src/lib/Supported_Languages.php:28
msgid "Arabic"
msgstr "Arabština"

#: src/lib/Supported_Languages.php:29
msgid "Croatian"
msgstr "Chorvatština"

#: src/lib/Supported_Languages.php:30
msgid "Thai"
msgstr "Thajština"

#: src/lib/Supported_Languages.php:31
msgid "Greek"
msgstr "Řečtina"

#: src/lib/Supported_Languages.php:32
msgid "Lithuanian"
msgstr "Litevština"

#: src/lib/Supported_Languages.php:33
msgid "Polish"
msgstr "Polština"

#: src/lib/Supported_Languages.php:34
msgid "Latvian"
msgstr "Lotyština"

#: src/lib/Supported_Languages.php:35
msgid "French"
msgstr "Francouzština"

#: src/lib/Supported_Languages.php:36
msgid "Indonesian"
msgstr "Indonéština"

#: src/lib/Supported_Languages.php:37
msgid "Macedonian"
msgstr "Makedonština"

#: src/lib/Supported_Languages.php:38
msgid "Estonian"
msgstr "Estonština"

#: src/lib/Supported_Languages.php:39
msgid "Portuguese"
msgstr "Portugalština"

#: src/lib/Supported_Languages.php:40
msgid "Irish"
msgstr "Irština"

#: src/lib/Supported_Languages.php:41
msgid "Malay"
msgstr "Malajština"

#: src/lib/Supported_Languages.php:42
msgid "Slovenian"
msgstr "Slovinština"

#: src/lib/Supported_Languages.php:43
msgid "Russian"
msgstr "Ruština"

#: src/lib/Supported_Languages.php:44
msgid "Japanese"
msgstr "Japonština"

#: src/lib/Supported_Languages.php:45
msgid "Hindi"
msgstr "Hindština"

#: src/lib/Supported_Languages.php:46
msgid "Slovak"
msgstr "Slovenština"

#: src/lib/Supported_Languages.php:47
msgid "Spanish"
msgstr "Španělština"

#: src/lib/Supported_Languages.php:48
msgid "Swedish"
msgstr "Švédština"

#: src/lib/Supported_Languages.php:49
msgid "Serbian"
msgstr "Srbština"

#: src/lib/Supported_Languages.php:50
msgid "Finnish"
msgstr "Finština"

#: src/lib/Supported_Languages.php:51
msgid "Basque"
msgstr "Baskičtina"

#: src/lib/Supported_Languages.php:52
msgid "Hungarian"
msgstr "Maďarština"

#: src/lib/Supported_Regions.php:7
msgid "Afghanistan"
msgstr "Afghánistán"

#: src/lib/Supported_Regions.php:8
msgid "Albania"
msgstr "Albánie"

#: src/lib/Supported_Regions.php:9
msgid "Algeria"
msgstr "Alžírsko"

#: src/lib/Supported_Regions.php:10
msgid "American Samoa"
msgstr "Americká Samoa"

#: src/lib/Supported_Regions.php:11
msgid "Andorra"
msgstr "Andorra"

#: src/lib/Supported_Regions.php:12
msgid "Angola"
msgstr "Angola"

#: src/lib/Supported_Regions.php:13
msgid "Anguilla"
msgstr "Anguilla"

#: src/lib/Supported_Regions.php:14
msgid "Antarctica"
msgstr "Antarktida"

#: src/lib/Supported_Regions.php:15
msgid "Antigua and Barbuda"
msgstr "Antigua a Barbuda"

#: src/lib/Supported_Regions.php:16
msgid "Argentina"
msgstr "Argentina"

#: src/lib/Supported_Regions.php:17
msgid "Armenia"
msgstr "Arménie"

#: src/lib/Supported_Regions.php:18
msgid "Aruba"
msgstr "Aruba"

#: src/lib/Supported_Regions.php:19
msgid "Australia"
msgstr "Austrálie"

#: src/lib/Supported_Regions.php:20
msgid "Austria"
msgstr "Rakousko"

#: src/lib/Supported_Regions.php:21
msgid "Azerbaijan"
msgstr "Ázerbájdžán"

#: src/lib/Supported_Regions.php:22
msgid "Bahamas"
msgstr "Bahamy"

#: src/lib/Supported_Regions.php:23
msgid "Bahrain"
msgstr "Bahrajn"

#: src/lib/Supported_Regions.php:24
msgid "Bangladesh"
msgstr "Bangladéš"

#: src/lib/Supported_Regions.php:25
msgid "Barbados"
msgstr "Barbados"

#: src/lib/Supported_Regions.php:26
msgid "Belarus"
msgstr "Bělorusko"

#: src/lib/Supported_Regions.php:27
msgid "Belgium"
msgstr "Belgie"

#: src/lib/Supported_Regions.php:28
msgid "Belize"
msgstr "Belize"

#: src/lib/Supported_Regions.php:29
msgid "Benin"
msgstr "Benin"

#: src/lib/Supported_Regions.php:30
msgid "Bermuda"
msgstr "Bermudy"

#: src/lib/Supported_Regions.php:31
msgid "Bhutan"
msgstr "Bhútán"

#: src/lib/Supported_Regions.php:32
msgid "Bolivia"
msgstr "Bolívie"

#: src/lib/Supported_Regions.php:33
msgid "Bonaire, Sint Eustatius and Saba"
msgstr "Bonaire, Sint Eustatius a Saba"

#: src/lib/Supported_Regions.php:34
msgid "Bosnia and Herzegovina"
msgstr "Bosna a Hercegovina"

#: src/lib/Supported_Regions.php:35
msgid "Botswana"
msgstr "Botswana"

#: src/lib/Supported_Regions.php:36
msgid "Bouvet Island"
msgstr "Bouvetův ostrov"

#: src/lib/Supported_Regions.php:37
msgid "Brazil"
msgstr "Brazílie"

#: src/lib/Supported_Regions.php:38
msgid "British Indian Ocean Territory"
msgstr "Britské indickooceánské území"

#: src/lib/Supported_Regions.php:39
msgid "Brunei "
msgstr "Brunej"

#: src/lib/Supported_Regions.php:40
msgid "Bulgaria"
msgstr "Bulharsko"

#: src/lib/Supported_Regions.php:41
msgid "Burkina Faso"
msgstr "Burkina Faso"

#: src/lib/Supported_Regions.php:42
msgid "Burundi"
msgstr "Burundi"

#: src/lib/Supported_Regions.php:43
msgid "Cambodia"
msgstr "Kambodža"

#: src/lib/Supported_Regions.php:44
msgid "Cameroon"
msgstr "Kamerun"

#: src/lib/Supported_Regions.php:45
msgid "Canada"
msgstr "Kanada"

#: src/lib/Supported_Regions.php:46
msgid "Cape Verde"
msgstr "Kapverdy"

#: src/lib/Supported_Regions.php:47
msgid "Cayman Islands"
msgstr "Kajmanské ostrovy"

#: src/lib/Supported_Regions.php:48
msgid "Central African Republic"
msgstr "Středoafrická republika"

#: src/lib/Supported_Regions.php:49
msgid "Chad"
msgstr "Čad"

#: src/lib/Supported_Regions.php:50
msgid "Chile"
msgstr "Chile"

#: src/lib/Supported_Regions.php:51
msgid "China"
msgstr "Čína"

#: src/lib/Supported_Regions.php:52
msgid "Christmas Island"
msgstr "Vánoční ostrov"

#: src/lib/Supported_Regions.php:53
msgid "Cocos (Keeling) Islands"
msgstr "Kokosové (Keelingovy) ostrovy"

#: src/lib/Supported_Regions.php:54
msgid "Colombia"
msgstr "Kolumbie"

#: src/lib/Supported_Regions.php:55
msgid "Comoros"
msgstr "Komory"

#: src/lib/Supported_Regions.php:56
msgid "Congo"
msgstr "Kongo"

#: src/lib/Supported_Regions.php:57
msgid "Congo, the Democratic Republic of the"
msgstr "Kongo, demokratická republika"

#: src/lib/Supported_Regions.php:58
msgid "Cook Islands"
msgstr "Cookovy ostrovy"

#: src/lib/Supported_Regions.php:59
msgid "Costa Rica"
msgstr "Kostarika"

#: src/lib/Supported_Regions.php:60
msgid "Croatia"
msgstr "Chorvatsko"

#: src/lib/Supported_Regions.php:61
msgid "Cuba"
msgstr "Kuba"

#: src/lib/Supported_Regions.php:62
msgid "Curaçao"
msgstr "Curaçao"

#: src/lib/Supported_Regions.php:63
msgid "Cyprus"
msgstr "Kypr"

#: src/lib/Supported_Regions.php:64
msgid "Czech Republic"
msgstr "Česká republika"

#: src/lib/Supported_Regions.php:65
msgid "Côte d'Ivoire"
msgstr "Pobřeží slonoviny"

#: src/lib/Supported_Regions.php:66
msgid "Denmark"
msgstr "Dánsko"

#: src/lib/Supported_Regions.php:67
msgid "Djibouti"
msgstr "Džibutsko"

#: src/lib/Supported_Regions.php:68
msgid "Dominica"
msgstr "Dominika"

#: src/lib/Supported_Regions.php:69
msgid "Dominican Republic"
msgstr "Dominikánská republika"

#: src/lib/Supported_Regions.php:70
msgid "Ecuador"
msgstr "Ekvádor"

#: src/lib/Supported_Regions.php:71
msgid "Egypt"
msgstr "Egypt"

#: src/lib/Supported_Regions.php:72
msgid "El Salvador"
msgstr "Salvador"

#: src/lib/Supported_Regions.php:73
msgid "Equatorial Guinea"
msgstr "Rovníková Guinea"

#: src/lib/Supported_Regions.php:74
msgid "Eritrea"
msgstr "Eritrea"

#: src/lib/Supported_Regions.php:75
msgid "Estonia"
msgstr "Estonsko"

#: src/lib/Supported_Regions.php:76
msgid "Ethiopia"
msgstr "Etiopie"

#: src/lib/Supported_Regions.php:77
msgid "Falkland Islands (Malvinas)"
msgstr "Falklandy (Malvíny)"

#: src/lib/Supported_Regions.php:78
msgid "Faroe Islands"
msgstr "Faerské ostrovy"

#: src/lib/Supported_Regions.php:79
msgid "Fiji"
msgstr "Fidži"

#: src/lib/Supported_Regions.php:80
msgid "Finland"
msgstr "Finsko"

#: src/lib/Supported_Regions.php:81
msgid "France"
msgstr "Francie"

#: src/lib/Supported_Regions.php:82
msgid "French Guiana"
msgstr "Francouzská Guyana"

#: src/lib/Supported_Regions.php:83
msgid "French Polynesia"
msgstr "Francouzská Polynésie"

#: src/lib/Supported_Regions.php:84
msgid "French Southern and Antarctic Lands"
msgstr "Francouzská jižní a antarktická území"

#: src/lib/Supported_Regions.php:85
msgid "Gabon"
msgstr "Gabon"

#: src/lib/Supported_Regions.php:86
msgid "Gambia"
msgstr "Gambie"

#: src/lib/Supported_Regions.php:87
msgid "Georgia"
msgstr "Gruzie"

#: src/lib/Supported_Regions.php:88
msgid "Germany"
msgstr "Německo"

#: src/lib/Supported_Regions.php:89
msgid "Ghana"
msgstr "Ghana"

#: src/lib/Supported_Regions.php:90
msgid "Gibraltar"
msgstr "Gibraltar"

#: src/lib/Supported_Regions.php:91
msgid "Greece"
msgstr "Řecko"

#: src/lib/Supported_Regions.php:92
msgid "Greenland"
msgstr "Grónsko"

#: src/lib/Supported_Regions.php:93
msgid "Grenada"
msgstr "Grenada"

#: src/lib/Supported_Regions.php:94
msgid "Guadeloupe"
msgstr "Guadeloupe"

#: src/lib/Supported_Regions.php:95
msgid "Guam"
msgstr "Guam"

#: src/lib/Supported_Regions.php:96
msgid "Guatemala"
msgstr "Guatemala"

#: src/lib/Supported_Regions.php:97
msgid "Guernsey"
msgstr "Guernsey"

#: src/lib/Supported_Regions.php:98
msgid "Guinea"
msgstr "Guinea"

#: src/lib/Supported_Regions.php:99
msgid "Guinea-Bissau"
msgstr "Guinea-Bissau"

#: src/lib/Supported_Regions.php:100
msgid "Guyana"
msgstr "Guyana"

#: src/lib/Supported_Regions.php:101
msgid "Haiti"
msgstr "Haiti"

#: src/lib/Supported_Regions.php:102
msgid "Heard Island and McDonald Islands"
msgstr "Heardův ostrov a McDonaldovy ostrovy"

#: src/lib/Supported_Regions.php:103
msgid "Holy See (Vatican City State)"
msgstr "Svatý stolec (Vatikán)"

#: src/lib/Supported_Regions.php:104
msgid "Honduras"
msgstr "Honduras"

#: src/lib/Supported_Regions.php:105
msgid "Hong Kong"
msgstr "Hongkong"

#: src/lib/Supported_Regions.php:106
msgid "Hungary"
msgstr "Maďarsko"

#: src/lib/Supported_Regions.php:107
msgid "Iceland"
msgstr "Island"

#: src/lib/Supported_Regions.php:108
msgid "India"
msgstr "Indie"

#: src/lib/Supported_Regions.php:109
msgid "Indonesia"
msgstr "Indonésie"

#: src/lib/Supported_Regions.php:110
msgid "Iran"
msgstr "Írán"

#: src/lib/Supported_Regions.php:111
msgid "Iraq"
msgstr "Irák"

#: src/lib/Supported_Regions.php:112
msgid "Ireland"
msgstr "Irsko"

#: src/lib/Supported_Regions.php:113
msgid "Isle of Man"
msgstr "Ostrov Man"

#: src/lib/Supported_Regions.php:114
msgid "Israel"
msgstr "Izrael"

#: src/lib/Supported_Regions.php:115
msgid "Italy"
msgstr "Itálie"

#: src/lib/Supported_Regions.php:116
msgid "Jamaica"
msgstr "Jamajka"

#: src/lib/Supported_Regions.php:117
msgid "Japan"
msgstr "Japonsko"

#: src/lib/Supported_Regions.php:118
msgid "Jersey"
msgstr "Dres"

#: src/lib/Supported_Regions.php:119
msgid "Jordan"
msgstr "Jordánsko"

#: src/lib/Supported_Regions.php:120
msgid "Kazakhstan"
msgstr "Kazachstán"

#: src/lib/Supported_Regions.php:121
msgid "Kenya"
msgstr "Keňa"

#: src/lib/Supported_Regions.php:122
msgid "Kiribati"
msgstr "Kiribati"

#: src/lib/Supported_Regions.php:123
msgid "Kuwait"
msgstr "Kuvajt"

#: src/lib/Supported_Regions.php:124
msgid "Kyrgyzstan"
msgstr "Kyrgyzstán"

#: src/lib/Supported_Regions.php:125
msgid "Laos"
msgstr "Laos"

#: src/lib/Supported_Regions.php:126
msgid "Latvia"
msgstr "Lotyšsko"

#: src/lib/Supported_Regions.php:127
msgid "Lebanon"
msgstr "Libanon"

#: src/lib/Supported_Regions.php:128
msgid "Lesotho"
msgstr "Lesotho"

#: src/lib/Supported_Regions.php:129
msgid "Liberia"
msgstr "Libérie"

#: src/lib/Supported_Regions.php:130
msgid "Libya"
msgstr "Libye"

#: src/lib/Supported_Regions.php:131
msgid "Liechtenstein"
msgstr "Lichtenštejnsko"

#: src/lib/Supported_Regions.php:132
msgid "Lithuania"
msgstr "Litva"

#: src/lib/Supported_Regions.php:133
msgid "Luxembourg"
msgstr "Lucembursko"

#: src/lib/Supported_Regions.php:134
msgid "Macao"
msgstr "Macao"

#: src/lib/Supported_Regions.php:135
msgid "North Macedonia"
msgstr "Severní Makedonie"

#: src/lib/Supported_Regions.php:136
msgid "Madagascar"
msgstr "Madagaskar"

#: src/lib/Supported_Regions.php:137
msgid "Malawi"
msgstr "Malawi"

#: src/lib/Supported_Regions.php:138
msgid "Malaysia"
msgstr "Malajsie"

#: src/lib/Supported_Regions.php:139
msgid "Maldives"
msgstr "Maledivy"

#: src/lib/Supported_Regions.php:140
msgid "Mali"
msgstr "Mali"

#: src/lib/Supported_Regions.php:141
msgid "Malta"
msgstr "Malta"

#: src/lib/Supported_Regions.php:142
msgid "Marshall Islands"
msgstr "Marshallovy ostrovy"

#: src/lib/Supported_Regions.php:143
msgid "Martinique"
msgstr "Martinik"

#: src/lib/Supported_Regions.php:144
msgid "Mauritania"
msgstr "Mauritánie"

#: src/lib/Supported_Regions.php:145
msgid "Mauritius"
msgstr "Mauricius"

#: src/lib/Supported_Regions.php:146
msgid "Mayotte"
msgstr "Mayotte"

#: src/lib/Supported_Regions.php:147
msgid "Mexico"
msgstr "Mexiko"

#: src/lib/Supported_Regions.php:148
msgid "Micronesia, Federated States of"
msgstr "Mikronésie, federativní státy"

#: src/lib/Supported_Regions.php:149
msgid "Moldova"
msgstr "Moldavsko"

#: src/lib/Supported_Regions.php:150
msgid "Monaco"
msgstr "Monako"

#: src/lib/Supported_Regions.php:151
msgid "Mongolia"
msgstr "Mongolsko"

#: src/lib/Supported_Regions.php:152
msgid "Montenegro"
msgstr "Černá Hora"

#: src/lib/Supported_Regions.php:153
msgid "Montserrat"
msgstr "Montserrat"

#: src/lib/Supported_Regions.php:154
msgid "Morocco"
msgstr "Maroko"

#: src/lib/Supported_Regions.php:155
msgid "Mozambique"
msgstr "Mosambik"

#: src/lib/Supported_Regions.php:156
msgid "Myanmar"
msgstr "Myanmar"

#: src/lib/Supported_Regions.php:157
msgid "Namibia"
msgstr "Namibie"

#: src/lib/Supported_Regions.php:158
msgid "Nauru"
msgstr "Nauru"

#: src/lib/Supported_Regions.php:159
msgid "Nepal"
msgstr "Nepál"

#: src/lib/Supported_Regions.php:160
msgid "Netherlands"
msgstr "Nizozemsko"

#: src/lib/Supported_Regions.php:161
msgid "New Caledonia"
msgstr "Nová Kaledonie"

#: src/lib/Supported_Regions.php:162
msgid "New Zealand"
msgstr "Nový Zéland"

#: src/lib/Supported_Regions.php:163
msgid "Nicaragua"
msgstr "Nikaragua"

#: src/lib/Supported_Regions.php:164
msgid "Niger"
msgstr "Niger"

#: src/lib/Supported_Regions.php:165
msgid "Nigeria"
msgstr "Nigérie"

#: src/lib/Supported_Regions.php:166
msgid "Niue"
msgstr "Niue"

#: src/lib/Supported_Regions.php:167
msgid "Norfolk Island"
msgstr "Norfolk"

#: src/lib/Supported_Regions.php:168
msgid "North Korea"
msgstr "Severní Korea"

#: src/lib/Supported_Regions.php:169
msgid "Northern Mariana Islands"
msgstr "Severní Mariany"

#: src/lib/Supported_Regions.php:170
msgid "Norway"
msgstr "Norsko"

#: src/lib/Supported_Regions.php:171
msgid "Oman"
msgstr "Omán"

#: src/lib/Supported_Regions.php:172
msgid "Pakistan"
msgstr "Pákistán"

#: src/lib/Supported_Regions.php:173
msgid "Palau"
msgstr "Palau"

#: src/lib/Supported_Regions.php:174
msgid "Palestinian Territory"
msgstr "Palestinské území"

#: src/lib/Supported_Regions.php:175
msgid "Panama"
msgstr "Panama"

#: src/lib/Supported_Regions.php:176
msgid "Papua New Guinea"
msgstr "Papua Nová Guinea"

#: src/lib/Supported_Regions.php:177
msgid "Paraguay"
msgstr "Paraguay"

#: src/lib/Supported_Regions.php:178
msgid "Peru"
msgstr "Peru"

#: src/lib/Supported_Regions.php:179
msgid "Philippines"
msgstr "Filipíny"

#: src/lib/Supported_Regions.php:180
msgid "Pitcairn"
msgstr "Pitcairnovy ostrovy"

#: src/lib/Supported_Regions.php:181
msgid "Poland"
msgstr "Polsko"

#: src/lib/Supported_Regions.php:182
msgid "Portugal"
msgstr "Portugalsko"

#: src/lib/Supported_Regions.php:183
msgid "Puerto Rico"
msgstr "Portoriko"

#: src/lib/Supported_Regions.php:184
msgid "Qatar"
msgstr "Katar"

#: src/lib/Supported_Regions.php:185
msgid "Romania"
msgstr "Rumunsko"

#: src/lib/Supported_Regions.php:186
msgid "Russia"
msgstr "Rusko"

#: src/lib/Supported_Regions.php:187
msgid "Rwanda"
msgstr "Rwanda"

#: src/lib/Supported_Regions.php:188
msgid "Réunion"
msgstr "Réunion"

#: src/lib/Supported_Regions.php:189
msgid "Saint Barthélemy"
msgstr "Svatý Bartoloměj"

#: src/lib/Supported_Regions.php:190
msgid "Saint Helena, Ascension and Tristan da Cunha"
msgstr "Svatá Helena, Ascension a Tristan da Cunha"

#: src/lib/Supported_Regions.php:191
msgid "Saint Kitts and Nevis"
msgstr "Svatý Kryštof a Nevis"

#: src/lib/Supported_Regions.php:192
msgid "Saint Lucia"
msgstr "Svatá Lucie"

#: src/lib/Supported_Regions.php:193
msgid "Saint Martin (French part)"
msgstr "Svatý Martin (francouzská část)"

#: src/lib/Supported_Regions.php:194
msgid "Saint Pierre and Miquelon"
msgstr "Saint Pierre a Miquelon"

#: src/lib/Supported_Regions.php:195
msgid "Saint Vincent and the Grenadines"
msgstr "Svatý Vincenc a Grenadiny"

#: src/lib/Supported_Regions.php:196
msgid "Samoa"
msgstr "Samoa"

#: src/lib/Supported_Regions.php:197
msgid "San Marino"
msgstr "San Marino"

#: src/lib/Supported_Regions.php:198
msgid "Sao Tome and Principe"
msgstr "Svatý Tomáš a Princův ostrov"

#: src/lib/Supported_Regions.php:199
msgid "Saudi Arabia"
msgstr "Saúdská Arábie"

#: src/lib/Supported_Regions.php:200
msgid "Senegal"
msgstr "Senegal"

#: src/lib/Supported_Regions.php:201
msgid "Serbia"
msgstr "Srbsko"

#: src/lib/Supported_Regions.php:202
msgid "Seychelles"
msgstr "Seychely"

#: src/lib/Supported_Regions.php:203
msgid "Sierra Leone"
msgstr "Sierra Leone"

#: src/lib/Supported_Regions.php:204
msgid "Singapore"
msgstr "Singapur"

#: src/lib/Supported_Regions.php:205
msgid "Sint Maarten (Dutch part)"
msgstr "Svatý Martin (nizozemská část)"

#: src/lib/Supported_Regions.php:206
msgid "Slovakia"
msgstr "Slovensko"

#: src/lib/Supported_Regions.php:207
msgid "Slovenia"
msgstr "Slovinsko"

#: src/lib/Supported_Regions.php:208
msgid "Solomon Islands"
msgstr "Šalamounovy ostrovy"

#: src/lib/Supported_Regions.php:209
msgid "Somalia"
msgstr "Somálsko"

#: src/lib/Supported_Regions.php:210
msgid "South Africa"
msgstr "Jihoafrická republika"

#: src/lib/Supported_Regions.php:211
msgid "South Georgia and the South Sandwich Islands"
msgstr "Jižní Georgie a Jižní Sandwichovy ostrovy"

#: src/lib/Supported_Regions.php:212
msgid "South Korea"
msgstr "Jižní Korea"

#: src/lib/Supported_Regions.php:213
msgid "South Sudan"
msgstr "Jižní Súdán"

#: src/lib/Supported_Regions.php:214
msgid "Spain"
msgstr "Španělsko"

#: src/lib/Supported_Regions.php:215
msgid "Sri Lanka"
msgstr "Srí Lanka"

#: src/lib/Supported_Regions.php:216
msgid "Sudan"
msgstr "Súdán"

#: src/lib/Supported_Regions.php:217
msgid "Suriname"
msgstr "Surinam"

#: src/lib/Supported_Regions.php:218
msgid "Svalbard and Jan Mayen"
msgstr "Svalbard a Jan Mayen"

#: src/lib/Supported_Regions.php:219
msgid "Eswatini"
msgstr "Svazijsko"

#: src/lib/Supported_Regions.php:220
msgid "Sweden"
msgstr "Švédsko"

#: src/lib/Supported_Regions.php:221
msgid "Switzerland"
msgstr "Švýcarsko"

#: src/lib/Supported_Regions.php:222
msgid "Syria"
msgstr "Sýrie"

#: src/lib/Supported_Regions.php:223
msgid "Taiwan"
msgstr "Tchaj-wan"

#: src/lib/Supported_Regions.php:224
msgid "Tajikistan"
msgstr "Tádžikistán"

#: src/lib/Supported_Regions.php:225
msgid "Tanzania"
msgstr "Tanzanie"

#: src/lib/Supported_Regions.php:226
msgid "Thailand"
msgstr "Thajsko"

#: src/lib/Supported_Regions.php:227
msgid "Timor-Leste"
msgstr "Východní Timor"

#: src/lib/Supported_Regions.php:228
msgid "Togo"
msgstr "Togo"

#: src/lib/Supported_Regions.php:229
msgid "Tokelau"
msgstr "Tokelau"

#: src/lib/Supported_Regions.php:230
msgid "Tonga"
msgstr "Tonga"

#: src/lib/Supported_Regions.php:231
msgid "Trinidad and Tobago"
msgstr "Trinidad a Tobago"

#: src/lib/Supported_Regions.php:232
msgid "Tunisia"
msgstr "Tunisko"

#: src/lib/Supported_Regions.php:233
msgid "Türkiye"
msgstr "Turecko"

#: src/lib/Supported_Regions.php:234
msgid "Turkmenistan"
msgstr "Turkmenistán"

#: src/lib/Supported_Regions.php:235
msgid "Turks and Caicos Islands"
msgstr "Turks a Caicos"

#: src/lib/Supported_Regions.php:236
msgid "Tuvalu"
msgstr "Tuvalu"

#: src/lib/Supported_Regions.php:237
msgid "Uganda"
msgstr "Uganda"

#: src/lib/Supported_Regions.php:238
msgid "Ukraine"
msgstr "Ukrajina"

#: src/lib/Supported_Regions.php:239
msgid "United Arab Emirates"
msgstr "Spojené arabské emiráty"

#: src/lib/Supported_Regions.php:240
msgid "United Kingdom"
msgstr "Velká Británie"

#: src/lib/Supported_Regions.php:241
msgid "United States"
msgstr "Spojené státy americké"

#: src/lib/Supported_Regions.php:242
msgid "United States - State of California"
msgstr "Spojené státy americké – Kalifornie"

#: src/lib/Supported_Regions.php:243
msgid "United States - State of Colorado"
msgstr "Spojené státy americké – Colorado"

#: src/lib/Supported_Regions.php:244
msgid "United States - State of Connecticut"
msgstr "Spojené státy americké – Connecticut"

#: src/lib/Supported_Regions.php:245
msgid "United States - State of Utah"
msgstr "Spojené státy americké – Utah"

#: src/lib/Supported_Regions.php:246
msgid "United States - State of Virginia"
msgstr "Spojené státy americké - Virginie"

#: src/lib/Supported_Regions.php:247
msgid "United States Minor Outlying Islands"
msgstr "Menší odlehlé ostrovy Spojených států amerických"

#: src/lib/Supported_Regions.php:248
msgid "Uruguay"
msgstr "Uruguay"

#: src/lib/Supported_Regions.php:249
msgid "Uzbekistan"
msgstr "Uzbekistán"

#: src/lib/Supported_Regions.php:250
msgid "Vanuatu"
msgstr "Vanuatu"

#: src/lib/Supported_Regions.php:251
msgid "Venezuela"
msgstr "Venezuela"

#: src/lib/Supported_Regions.php:252
msgid "Viet Nam"
msgstr "Vietnam"

#: src/lib/Supported_Regions.php:253
msgid "Virgin Islands, British"
msgstr "Britské Panenské ostrovy"

#: src/lib/Supported_Regions.php:254
msgid "Virgin Islands, U.S."
msgstr "Americké Panenské ostrovy"

#: src/lib/Supported_Regions.php:255
msgid "Wallis and Futuna"
msgstr "Wallis a Futuna"

#: src/lib/Supported_Regions.php:256
msgid "Western Sahara"
msgstr "Západní Sahara"

#: src/lib/Supported_Regions.php:257
msgid "Yemen"
msgstr "Jemen"

#: src/lib/Supported_Regions.php:258
msgid "Zambia"
msgstr "Zambie"

#: src/lib/Supported_Regions.php:259
msgid "Zimbabwe"
msgstr "Zimbabwe"

#: src/lib/Supported_Regions.php:260
msgid "Åland Islands"
msgstr "Ålandy"

#: src/settings/Network_Menu_Settings.php:36
#: src/settings/pages/Dashboard_Page.php:21
msgid "Cookiebot"
msgstr "Cookiebot"

#: src/settings/Network_Menu_Settings.php:44
#: src/settings/pages/Settings_Page.php:23
msgid "Cookiebot Settings"
msgstr "Nastavení Cookiebot"

#: src/settings/Network_Menu_Settings.php:45
#: src/settings/pages/Settings_Page.php:24
#: src/view/admin/common/settings-page.php:30
#: src/view/admin/common/templates/main-tabs.php:32
#: src/view/admin/uc_frame/settings-page.php:47
#: src/view/admin/cb_frame/settings-page.php:50
msgid "Settings"
msgstr "Nastavení"

#: src/settings/Network_Menu_Settings.php:52
#: src/settings/pages/Support_Page.php:18
msgid "Cookiebot Support"
msgstr "Podpora Cookiebot"

#: src/settings/Network_Menu_Settings.php:53
#: src/settings/pages/Support_Page.php:19
#: src/view/admin/cb_frame/support-page.php:19
#: src/view/admin/common/support-page.php:19
#: src/view/admin/common/templates/main-tabs.php:50
#: src/view/admin/uc_frame/support-page.php:19
msgid "Support"
msgstr "Podpora"

#: src/settings/pages/Dashboard_Page.php:34
msgid "Cookiebot Dashboard"
msgstr "Ovládací panel Cookiebot"

#: src/settings/pages/Debug_Page.php:27 src/settings/pages/Debug_Page.php:28
#: src/view/admin/cb_frame/debug-page.php:18
#: src/view/admin/common/templates/main-tabs.php:57
#: src/view/admin/uc_frame/debug-page.php:18
msgid "Debug info"
msgstr "Informace o ladění"

#: src/settings/pages/Iab_Page.php:20 src/settings/pages/Iab_Page.php:21
msgid "IAB"
msgstr "IAB"

#: src/settings/pages/Iab_Page.php:130
msgid "Purposes of data use"
msgstr "Účely použití údajů"

#: src/settings/pages/Iab_Page.php:131
msgid ""
"Inform your users how you’ll use their data. We’ll show this on the second "
"layer of your consent banner, where users interested in more granular detail "
"about data processing can view it."
msgstr ""
"Informujte uživatele, jak budete jejich data používat. Ukážeme to na druhé "
"vrstvě vašeho banneru souhlasu, který si uživatelé mohou zobrazit, pokud "
"mají zájem o podrobnější informace o zpracování dat."

#: src/settings/pages/Iab_Page.php:138
msgid "Special purposes of data use"
msgstr "Zvláštní účely použití údajů"

#: src/settings/pages/Iab_Page.php:139
msgid ""
"Inform your users about special purposes of using their data. We’ll show "
"this on the second layer of your consent banner."
msgstr ""
"informují uživatele o zvláštních účelech použití jejich údajů. Ukážeme to na "
"druhé vrstvě vašeho banneru souhlasu."

#: src/settings/pages/Iab_Page.php:146
msgid "Features required for data processing"
msgstr "Funkce potřebné pro zpracování dat"

#: src/settings/pages/Iab_Page.php:147
msgid ""
"Inform users about the features necessary for processing their personal "
"data. We’ll list the selected features on the second layer of your consent "
"banner."
msgstr ""
"Informujte uživatele o funkcích nezbytných pro zpracování jejich osobních "
"údajů. Vybrané funkce se zobrazí ve druhé vrstvě vašeho banneru souhlasu."

#: src/settings/pages/Iab_Page.php:154
msgid "Special features required for data processing"
msgstr "Speciální funkce potřebné pro zpracování dat"

#: src/settings/pages/Iab_Page.php:155
msgid ""
"Inform users about any specially categorized features required for "
"processing their personal data. We’ll list the selected features on the "
"second layer of your consent banner, offering options for users to enable or "
"disable them."
msgstr ""
"Informujte uživatele o všech speciálně kategorizovaných funkcích potřebných "
"ke zpracování jejich osobních údajů. Vybrané funkce se zobrazí ve druhé "
"vrstvě vašeho banneru souhlasu a uživatelům nabídneme možnosti jejich "
"povolení nebo zakázání."

#: src/settings/pages/Iab_Page.php:162
msgid "TCF listed vendors"
msgstr "Dodavatelé na seznamu TCF"

#: src/settings/pages/Iab_Page.php:183
msgid "Store and/or access information on a device"
msgstr "Ukládání a/nebo přístup k informacím v zařízení"

#: src/settings/pages/Iab_Page.php:187
msgid "Use limited data to select advertising"
msgstr "Používání omezených dat k výběru reklamy"

#: src/settings/pages/Iab_Page.php:191
msgid "Create profiles for personalised advertising"
msgstr "Vytváření profilů pro personalizovanou reklamu"

#: src/settings/pages/Iab_Page.php:195
msgid "Use profiles to select personalised advertising"
msgstr "Vybírání personalizované reklamy pomocí profilů"

#: src/settings/pages/Iab_Page.php:199
msgid "Create profiles to personalise content"
msgstr "Vytváření profilů pro přizpůsobení obsahu"

#: src/settings/pages/Iab_Page.php:203
msgid "Use profiles to select personalised content"
msgstr "Vybírání personalizovaného obsahu pomocí profilů"

#: src/settings/pages/Iab_Page.php:207
msgid "Measure advertising performance"
msgstr "Měření výkonu reklamy"

#: src/settings/pages/Iab_Page.php:211
msgid "Measure content performance"
msgstr "Měření výkonu obsahu"

#: src/settings/pages/Iab_Page.php:215
msgid ""
"Understand audiences through statistics or combinations of data from "
"different sources"
msgstr ""
"Porozumění publiku prostřednictvím statistik nebo kombinací dat z různých "
"zdrojů"

#: src/settings/pages/Iab_Page.php:219
msgid "Develop and improve services"
msgstr "Rozvíjení a zlepšování služeb"

#: src/settings/pages/Iab_Page.php:223
msgid "Use limited data to select content"
msgstr "Používání omezených dat k výběru obsahu"

#: src/settings/pages/Iab_Page.php:229
msgid "Ensure security, prevent and detect fraud, and fix errors"
msgstr "Zajištění bezpečnosti, zabránění a detekování podvodů a opravení chyb"

#: src/settings/pages/Iab_Page.php:233
msgid "Deliver and present advertising and content"
msgstr "Poskytování a prezentace reklamy a obsahu"

#: src/settings/pages/Iab_Page.php:239
msgid "Match and combine data from other data sources"
msgstr "Párování a kombinování dat z jiných zdrojů dat"

#: src/settings/pages/Iab_Page.php:243
msgid "Link different devices"
msgstr "Připojení různých zařízení"

#: src/settings/pages/Iab_Page.php:247
msgid "Identify devices based on information transmitted automatically"
msgstr "Identifikování zařízení na základě informací přenášených automaticky"

#: src/settings/pages/Iab_Page.php:253
msgid "Use precise geolocation data"
msgstr "Používání přesných geolokačních dat"

#: src/settings/pages/Iab_Page.php:257
msgid "Actively scan device characteristics for identification"
msgstr "Aktivní skenování vlastností zařízení za účelem identifikace"

#: src/shortcode/Cookiebot_Declaration_Shortcode.php:53
msgid "Please add your Cookiebot ID to show Cookie Declarations"
msgstr "Prohlášení o souborech cookie se zobrazí po zadání Cookiebot ID"

#: src/shortcode/Cookiebot_Embedding_Shortcode.php:28
msgid "Please add a service ID into the shortcode \"service\" parameter."
msgstr "Přidejte do parametru „služba“ ve zkráceném kódu ID služby."

#: src/view/admin/cb_frame/dashboard-page.php:33
msgid "I already have a Cookiebot CMP account"
msgstr "Už mám účet Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:37
msgid "Connect my existing account"
msgstr "Připojit stávající účet"

#: src/view/admin/cb_frame/dashboard-page.php:42
msgid "Your Cookiebot CMP for WordPress solution"
msgstr "Váš plugin Cookiebot CMP pro řešení WordPress"

#: src/view/admin/cb_frame/dashboard-page.php:47
#: src/view/admin/uc_frame/dashboard-page.php:35
msgid "Account added"
msgstr "Účet byl přidán"

#: src/view/admin/cb_frame/dashboard-page.php:50
msgid "Congratulations!"
msgstr "Gratulujeme!"

#: src/view/admin/cb_frame/dashboard-page.php:51
msgid "You have added your Domain Group ID to WordPress. You are all set!"
msgstr ""
"Do systému WordPress jste přidali ID skupiny domény. Vše je připraveno!"

#: src/view/admin/cb_frame/dashboard-page.php:62
#: src/view/admin/uc_frame/dashboard-page.php:48
msgid "Your opinion matters"
msgstr "Na vašem názoru záleží"

#: src/view/admin/cb_frame/dashboard-page.php:65
msgid ""
"Are you happy with our WordPress plugin? Your feedback will help us make our "
"product better for you."
msgstr ""
"Jste s naším pluginem pro WordPress spokojeni? Vaše zpětná vazba nám pomůže "
"tento produkt dále vylepšit."

#: src/view/admin/cb_frame/dashboard-page.php:69
msgid "Write a review"
msgstr "Napsat recenzi"

#: src/view/admin/cb_frame/dashboard-page.php:80
#: src/view/admin/common/dashboard-page.php:45
msgid "Get started"
msgstr "Začínáme"

#: src/view/admin/cb_frame/dashboard-page.php:83
msgid "Create a new Cookiebot CMP account"
msgstr "Vytvoření nového účtu Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:87
#: src/view/admin/common/dashboard-page.php:52
msgid "Create a new account"
msgstr "Vytvořit nový účet"

#: src/view/admin/cb_frame/dashboard-page.php:92
msgid "Learn more about how to optimize your Cookiebot CMP setup?"
msgstr "Další informace o optimalizaci nastavení pluginu Cookiebot"

#: src/view/admin/cb_frame/dashboard-page.php:96
msgid "Visit Help Center"
msgstr "Navštivte centrum nápovědy"

#: src/view/admin/cb_frame/dashboard-page.php:113
#: src/view/admin/cb_frame/support-page.php:43
#: src/view/admin/common/dashboard-page.php:63
#: src/view/admin/common/support-page.php:43
#: src/view/admin/uc_frame/dashboard-page.php:66
#: src/view/admin/uc_frame/support-page.php:43
msgid "Video guide"
msgstr "Videoprůvodce"

#: src/view/admin/cb_frame/dashboard-page.php:115
msgid "How to get started with Cookiebot CMP"
msgstr "Jak začít s pluginem Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:119
msgid "Learn more about Cookiebot CMP"
msgstr "Další informace o pluginu Cookiebot CMP"

#: src/view/admin/cb_frame/dashboard-page.php:131
#: src/view/admin/common/dashboard-page.php:81
#: src/view/admin/uc_frame/dashboard-page.php:84
#: src/view/admin/uc_frame/settings/embeddings-page.php:32
msgid "GDPR"
msgstr "GDPR"

#: src/view/admin/cb_frame/dashboard-page.php:134
#: src/view/admin/common/dashboard-page.php:84
#: src/view/admin/uc_frame/dashboard-page.php:87
msgid "Europe"
msgstr "Evropa"

#: src/view/admin/cb_frame/dashboard-page.php:138
#: src/view/admin/cb_frame/dashboard-page.php:140
#: src/view/admin/cb_frame/dashboard-page.php:155
#: src/view/admin/cb_frame/dashboard-page.php:157
#: src/view/admin/common/dashboard-page.php:88
#: src/view/admin/common/dashboard-page.php:90
#: src/view/admin/common/dashboard-page.php:105
#: src/view/admin/common/dashboard-page.php:107
#: src/view/admin/uc_frame/dashboard-page.php:91
#: src/view/admin/uc_frame/dashboard-page.php:93
#: src/view/admin/uc_frame/dashboard-page.php:108
#: src/view/admin/uc_frame/dashboard-page.php:110
msgid "Learn More"
msgstr "Další informace"

#: src/view/admin/cb_frame/dashboard-page.php:148
#: src/view/admin/common/dashboard-page.php:98
#: src/view/admin/uc_frame/dashboard-page.php:101
msgid "CCPA"
msgstr "CCPA"

#: src/view/admin/cb_frame/dashboard-page.php:151
#: src/view/admin/common/dashboard-page.php:101
#: src/view/admin/uc_frame/dashboard-page.php:104
msgid "North America"
msgstr "Severní Amerika"

#: src/view/admin/cb_frame/dashboard-page.php:162
#: src/view/admin/common/dashboard-page.php:112
msgid "See other legislations"
msgstr "Viz další právní předpisy"

#: src/view/admin/cb_frame/debug-page.php:22
msgid "Debug information"
msgstr "Informace o ladění"

#: src/view/admin/cb_frame/debug-page.php:25
msgid ""
"The information below is for debugging purposes. If you have any issues with "
"your Cookiebot CMP integration, this information is the best place to start."
msgstr ""
"Níže uvedené informace slouží k ladění. Pokud narazíte na problémy s "
"integrací pluginu Cookiebot CMP, doporučujeme začít od těchto informací."

#: src/view/admin/cb_frame/debug-page.php:33
#: src/view/admin/uc_frame/debug-page.php:33
msgid "Copy debug information to clipboard"
msgstr "Zkopírovat informace o ladění do schránky"

#: src/view/admin/cb_frame/debug-page.php:42
msgid ""
"If you have any issues with the implemenation of Cookiebot CMP, please visit "
"our Support Center."
msgstr ""
"Pokud máte problémy s implementací pluginu Cookiebot CMP, navštivte naše "
"centrum podpory."

#: src/view/admin/cb_frame/debug-page.php:45
#: src/view/admin/uc_frame/debug-page.php:44
msgid "Visit Support Center"
msgstr "Navštívit centrum podpory"

#: src/view/admin/cb_frame/network-settings-page.php:22
#: src/view/admin/common/network-settings-page.php:23
#: src/view/admin/uc_frame/network-settings-page.php:23
msgid "Network Settings"
msgstr "Nastavení sítě"

#: src/view/admin/cb_frame/network-settings-page.php:33
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:3
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:3
#: src/view/admin/uc_frame/network-settings-page.php:34
msgid "Are you sure?"
msgstr "Určitě?"

#: src/view/admin/cb_frame/network-settings-page.php:36
#: src/view/admin/uc_frame/network-settings-page.php:37
msgid ""
"You will need to add a new ID before updating other network settings. If any "
"subsite is using its own account disconnecting this account won’t affect it."
msgstr ""
"Před aktualizací dalších síťových nastavení budete muset přidat nové ID. "
"Pokud některý dílčí web používá vlastní účet, odpojení tohoto účtu ho nijak "
"neovlivní."

#: src/view/admin/cb_frame/network-settings-page.php:40
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:10
#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:10
#: src/view/admin/uc_frame/network-settings-page.php:41
msgid "Cancel"
msgstr "Zrušit"

#: src/view/admin/cb_frame/network-settings-page.php:43
#: src/view/admin/cb_frame/network-settings-page.php:65
#: src/view/admin/cb_frame/settings/general-page.php:52
#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:13
#: src/view/admin/uc_frame/network-settings-page.php:44
#: src/view/admin/uc_frame/network-settings-page.php:75
#: src/view/admin/uc_frame/settings/general-page.php:45
msgid "Disconnect account"
msgstr "Odpojit účet"

#: src/view/admin/cb_frame/network-settings-page.php:50
msgid "Network Domain Group ID"
msgstr "ID skupiny síťových domén"

#: src/view/admin/cb_frame/network-settings-page.php:52
msgid ""
"If added this will be the default Cookiebot ID for all subsites. Subsites "
"are able to override the Cookiebot ID."
msgstr ""
"Pokud ji přidáte, bude toto Cookiebot™ ID platit jako výchozí pro všechny "
"dílčí weby. Dílčí weby mohou Cookiebot ID přepsat."

#: src/view/admin/cb_frame/network-settings-page.php:54
#: src/view/admin/cb_frame/settings/general-page.php:37
msgid "Read more on the Domain Group ID"
msgstr "Další informace o ID skupiny domén"

#: src/view/admin/cb_frame/network-settings-page.php:58
#: src/view/admin/cb_frame/settings/general-page.php:43
msgid "Add your Domain Group ID"
msgstr "Přidat ID skupiny domén"

#: src/view/admin/cb_frame/network-settings-page.php:73
#: src/view/admin/cb_frame/settings/general-page.php:146
#: src/view/admin/uc_frame/network-settings-page.php:111
#: src/view/admin/uc_frame/settings/general-page.php:120
msgid "Cookie-blocking"
msgstr "Blokování souborů cookie"

#: src/view/admin/cb_frame/network-settings-page.php:75
msgid ""
"Select your cookie-blocking mode here. Auto cookie-blocking mode will "
"automatically block all cookies (except for ‘strictly necessary’ cookies) "
"until a user has given consent. Manual cookie-blocking mode requests manual "
"adjustments to the cookie-setting scripts. Please find our implementation "
"guides below:"
msgstr ""
"Zde vyberte režim blokování souborů cookie. Režim automatického blokování "
"souborů cookie automaticky zablokuje všechny soubory cookie (s výjimkou "
"„nezbytně nutných“ souborů cookie), dokud uživatel nevyjádří souhlas. Režim "
"ručního blokování souborů cookie vyžaduje ruční úpravy skriptů pro nastavení "
"souborů cookie. Níže naleznete naše průvodce implementací:"

#: src/view/admin/cb_frame/network-settings-page.php:80
#: src/view/admin/cb_frame/settings/general-page.php:167
#: src/view/admin/uc_frame/network-settings-page.php:118
#: src/view/admin/uc_frame/settings/general-page.php:133
msgid "Select cookie-blocking mode"
msgstr "Vyberte režim blokování souborů cookie"

#: src/view/admin/cb_frame/network-settings-page.php:87
#: src/view/admin/cb_frame/settings/general-page.php:175
#: src/view/admin/uc_frame/network-settings-page.php:125
#: src/view/admin/uc_frame/settings/general-page.php:141
msgid "Automatic"
msgstr "Automaticky"

#: src/view/admin/cb_frame/network-settings-page.php:88
#: src/view/admin/cb_frame/settings/general-page.php:176
#: src/view/admin/uc_frame/network-settings-page.php:126
#: src/view/admin/uc_frame/settings/general-page.php:142
msgid "Recommended"
msgstr "Doporučeno"

#: src/view/admin/cb_frame/network-settings-page.php:96
#: src/view/admin/cb_frame/network-settings-page.php:146
#: src/view/admin/cb_frame/network-settings-page.php:193
#: src/view/admin/uc_frame/network-settings-page.php:134
msgid "Choose per subsite"
msgstr "Vyberte možnost podle dílčího webu"

#: src/view/admin/cb_frame/network-settings-page.php:104
#: src/view/admin/cb_frame/settings/general-page.php:206
msgid "Cookiebot™ script tag"
msgstr "Značka skriptu Cookiebot™"

#: src/view/admin/cb_frame/network-settings-page.php:106
msgid ""
"Add async or defer attribute to Cookie banner script tag. Default: Choose "
"per subsite"
msgstr ""
"Přidat asynchronní nebo odložit atribut na značku skriptu pro banner o "
"souborech cookie. Výchozí: Vyberte pro jednotlivé dílčí weby"

#: src/view/admin/cb_frame/network-settings-page.php:109
msgid "This feature is only available when not using Auto Blocking"
msgstr ""
"Tato funkce je k dispozici pouze v případě, že nepoužíváte Automatické "
"blokování"

#: src/view/admin/cb_frame/network-settings-page.php:112
#: src/view/admin/cb_frame/network-settings-page.php:156
#: src/view/admin/cb_frame/network-settings-page.php:228
#: src/view/admin/uc_frame/network-settings-page.php:169
msgid ""
"Setting will apply for all subsites. Subsites will not be able to override."
msgstr ""
"Nastavení bude platit pro všechny dílčí weby. Dílčí weby ho nebudou moci "
"přepsat."

#: src/view/admin/cb_frame/network-settings-page.php:125
#: src/view/admin/cb_frame/network-settings-page.php:172
#: src/view/admin/cb_frame/settings/additional-page.php:99
#: src/view/admin/cb_frame/settings/general-page.php:222
msgid "None"
msgstr "Žádné"

#: src/view/admin/cb_frame/network-settings-page.php:154
msgid "Cookiebot declaration script tag"
msgstr "Značka skriptu Cookiebot pro prohlášení o souborech cookie"

#: src/view/admin/cb_frame/network-settings-page.php:159
msgid ""
"Add async or defer attribute to Cookie declaration script tag. Default: "
"Choose per subsite"
msgstr ""
"Přidat asynchronní nebo odložit atribut na značku skriptu pro prohlášení o "
"souborech cookie. Výchozí: Vyberte pro jednotlivé dílčí weby"

#: src/view/admin/cb_frame/network-settings-page.php:201
#: src/view/admin/cb_frame/settings/additional-page.php:10
#: src/view/admin/uc_frame/network-settings-page.php:142
#: src/view/admin/uc_frame/settings/additional-page.php:10
msgid "Automatic updates"
msgstr "Automatické aktualizace"

#: src/view/admin/cb_frame/network-settings-page.php:203
#: src/view/admin/cb_frame/settings/additional-page.php:13
#: src/view/admin/uc_frame/network-settings-page.php:144
#: src/view/admin/uc_frame/settings/additional-page.php:13
msgid ""
"Enable automatic updates whenever we release a new version of the plugin."
msgstr ""
"Povolte automatické aktualizace ve všech případech, kdy vydáme novou verzi "
"pluginu."

#: src/view/admin/cb_frame/network-settings-page.php:218
#: src/view/admin/cb_frame/settings/additional-page.php:28
#: src/view/admin/uc_frame/network-settings-page.php:159
#: src/view/admin/uc_frame/settings/additional-page.php:28
msgid "Automatically update to new version"
msgstr "Automaticky aktualizovat na novou verzi"

#: src/view/admin/cb_frame/network-settings-page.php:226
#: src/view/admin/cb_frame/settings/general-page.php:248
#: src/view/admin/uc_frame/network-settings-page.php:167
#: src/view/admin/uc_frame/settings/general-page.php:172
msgid "Hide cookie popup"
msgstr "Skrýt vyskakovací okno o souborech cookie"

#: src/view/admin/cb_frame/network-settings-page.php:231
#: src/view/admin/cb_frame/settings/general-page.php:254
#: src/view/admin/uc_frame/network-settings-page.php:172
#: src/view/admin/uc_frame/settings/general-page.php:178
msgid ""
"This will remove the cookie consent banner from your website. The cookie "
"declaration shortcode will still be available if you are using Google Tag "
"Manager (or equal), you need to add the Cookiebot script in your Tag Manager."
msgstr ""
"Tato možnost vám z webu odstraní banner souhlasu se soubory cookie. Pokud "
"používáte Správce značek Google (nebo podobnou službu), musíte do Správce "
"značek přidat skript Cookiebot; zkrácený kód s prohlášením o souborech "
"cookie budete mít nadále k dispozici."

#: src/view/admin/cb_frame/network-settings-page.php:246
#: src/view/admin/cb_frame/settings/general-page.php:280
#: src/view/admin/uc_frame/network-settings-page.php:187
#: src/view/admin/uc_frame/settings/general-page.php:204
msgid "Hide the cookie popup banner"
msgstr "Skrýt vyskakovací banner o souborech cookie"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:11
#: src/view/admin/common/prior-consent/available-addons/tab-header.php:4
#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:14
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:14
msgid "Remember to save your changes before switching tabs"
msgstr "Nezapomeňte před přepnutím karet uložit změny"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:20
msgid "Consent Level API Settings"
msgstr "Nastavení Consent Level API"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:29
msgid ""
"WP Consent Level API and Cookiebot™ categorize cookies a bit differently. "
"The default settings should fit most needs, but if you need to change the "
"mapping you can do so below."
msgstr ""
"WP Consent Level API a Cookiebot™ kategorizují soubory cookie trochu jinak. "
"Výchozí nastavení by mělo vyhovovat většině potřeb, ale pokud potřebujete "
"změnit mapování, můžete tak učinit níže."

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:79
msgid "Cookiebot™ cookie categories"
msgstr "Kategorie cookies Cookiebot™"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:96
#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:51
msgid "WP Consent API cookies categories equivalent"
msgstr "Ekvivalent kategorií souborů cookie WP Consent API"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:102
msgid "Functional"
msgstr "Funkční"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:150
msgid "Statistics Anonymous"
msgstr "Anonymní statistiky"

#: src/view/admin/cb_frame/prior-consent/consent-api/tab.php:179
msgid "Reset to default mapping"
msgstr "Obnovit výchozí mapování"

#: src/view/admin/cb_frame/settings-page.php:48
msgid "Select at least one vendor on TCF tab"
msgstr "Vyberte alespoň jednoho dodavatele na kartě TCF"

#: src/view/admin/cb_frame/settings-page.php:57
#: src/view/admin/uc_frame/settings-page.php:54
msgid "General Settings"
msgstr "Obecná nastavení"

#: src/view/admin/cb_frame/settings-page.php:61
#: src/view/admin/uc_frame/settings-page.php:58
msgid "Additional Settings"
msgstr "Další nastavení"

#: src/view/admin/cb_frame/settings-page.php:65
#: src/view/admin/cb_frame/settings/gtm-page.php:27
#: src/view/admin/uc_frame/settings-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:12
#: src/view/admin/uc_frame/settings/gtm-page.php:27
msgid "Google Tag Manager"
msgstr "Správce značek Google"

#: src/view/admin/cb_frame/settings-page.php:69
#: src/view/admin/cb_frame/settings/gcm-page.php:50
#: src/view/admin/uc_frame/settings-page.php:66
#: src/view/admin/uc_frame/settings/gcm-page.php:28
#: src/view/admin/uc_frame/settings/gcm-page.php:43
msgid "Google Consent Mode"
msgstr "Režim souhlasu Google"

#: src/view/admin/cb_frame/settings-page.php:73
#: src/view/admin/uc_frame/settings/embeddings-page.php:33
msgid "TCF"
msgstr "TCF"

#: src/view/admin/cb_frame/settings-page.php:77
msgid "Multiple Configurations"
msgstr "Více konfigurací"

#: src/view/admin/cb_frame/settings/additional-page.php:37
#: src/view/admin/uc_frame/settings/additional-page.php:37
msgid "Show the banner while logged in"
msgstr "Zobrazení banneru během přihlášení"

#: src/view/admin/cb_frame/settings/additional-page.php:40
#: src/view/admin/uc_frame/settings/additional-page.php:40
msgid ""
"You can choose to display the consent banner on your website while you’re "
"logged in and changing settings or customizing your banner."
msgstr ""
"Můžete si vybrat, jestli chcete na svém webu zobrazit banner souhlasu, když "
"jste přihlášeni a měníte nastavení nebo banner přizpůsobujete."

#: src/view/admin/cb_frame/settings/additional-page.php:66
#: src/view/admin/uc_frame/settings/additional-page.php:66
msgid "Show banner on website when logged in"
msgstr "Zobrazit banner na webu během přihlášení"

#: src/view/admin/cb_frame/settings/additional-page.php:75
msgid "Cookie declaration script tag:"
msgstr "Značka skriptu pro prohlášení o souborech cookie:"

#: src/view/admin/cb_frame/settings/additional-page.php:78
msgid ""
"If you implemented the declaration on your page through our widget in "
"WordPress, you can choose here how the script should be loaded."
msgstr ""
"Pokud jste prohlášení na své stránce implementovali prostřednictvím našeho "
"widgetu v systému WordPress, můžete si zde vybrat, jak se skript má načítat."

#: src/view/admin/cb_frame/settings/additional-page.php:84
msgid "Select the cookie declaration script loading setting"
msgstr "Vyberte nastavení načtení skriptu pro prohlášení o souborech cookie"

#: src/view/admin/cb_frame/settings/additional-page.php:116
#: src/view/admin/cb_frame/settings/general-page.php:187
#: src/view/admin/cb_frame/settings/general-page.php:239
#: src/view/admin/cb_frame/settings/general-page.php:283
#: src/view/admin/uc_frame/settings/general-page.php:153
#: src/view/admin/uc_frame/settings/general-page.php:207
msgid "Disabled by active setting in Network Settings"
msgstr "Deaktivováno aktivním nastavením v Nastavení sítě"

#: src/view/admin/cb_frame/settings/additional-page.php:125
msgid "Ignore scripts in queue from Cookiebot CMP scan:"
msgstr "Ignorovat skripty ve frontě při prohledávání pluginem Cookiebot CMP:"

#: src/view/admin/cb_frame/settings/additional-page.php:128
msgid ""
"List scripts source URL (one per line) from the queue to ignore Cookiebot "
"CMP scan. Partial source URL will also work, e.g. wp-content/plugins/"
"woocommerce will block every WooCommerce script."
msgstr ""
"Seznam zdrojových adres URL skriptů (jedna na řádek) z fronty, které se mají "
"ignorovat při prohledávání pluginem Cookiebot CMP. Postačí i částečná "
"zdrojová adresa URL: např. wp-content/plugins/woocommerce zablokuje každý "
"skript pluginu WooCommerce."

#: src/view/admin/cb_frame/settings/additional-page.php:131
msgid ""
"This feature only works for scripts loaded via wp_enqueue_script. Manually "
"added scripts must be manually edited."
msgstr ""
"Tato funkce funguje pouze u skriptů načtených prostřednictvím možnosti "
"wp_enqueue_script. Ručně přidané skripty je nutné ručně upravit."

#: src/view/admin/cb_frame/settings/additional-page.php:137
msgid "Script source URL:"
msgstr "Zdrojová adresa URL skriptu:"

#: src/view/admin/cb_frame/settings/additional-page.php:143
msgid "Add script source URL, one per line"
msgstr "Přidání zdrojové adresy URL skriptu, jedné na řádek"

#: src/view/admin/cb_frame/settings/gcm-page.php:14
msgid "What is Google Consent Mode and why should you enable it?"
msgstr "Co je Google Consent Mode a proč byste ho měli povolit?"

#: src/view/admin/cb_frame/settings/gcm-page.php:17
msgid ""
"Google Consent Mode is a way for your website to measure conversions and get "
"analytics insights while being fully GDPR-compliant when using services like "
"Google Analytics, Google Tag Manager (GTM) and Google Ads."
msgstr ""
"Režim Google Consent Mode je způsob, jak vaše webové stránky měří konverze a "
"získávají analytické statistiky, který plně odpovídá nařízení GDPR při "
"používání služeb jako Google Analytics, Správce značek Google a Google Ads."

#: src/view/admin/cb_frame/settings/gcm-page.php:20
msgid ""
"Cookiebot consent managment platform (CMP) and Google Consent Mode integrate "
"seamlessly to offer you plug-and-play compliance and streamlined use of all "
"Google's services in one easy solution."
msgstr ""
"Platforma pro správu souhlasu Cookiebot (CMP) a Google Consent Mode se "
"bezproblémově integrují, aby vám nabízely shodu se systémem plug-and-play a "
"zjednodušené používání všech služeb společnosti Google v jediném jednoduchém "
"řešení."

#: src/view/admin/cb_frame/settings/gcm-page.php:24
msgid "Read more about Cookiebot CMP and Google Consent Mode"
msgstr "Viz další informace o pluginu Cookiebot CMP a Google Consent Mode"

#: src/view/admin/cb_frame/settings/gcm-page.php:31
msgid "Google Consent Mode:"
msgstr "Google Consent Mode:"

#: src/view/admin/cb_frame/settings/gcm-page.php:34
msgid ""
"Enable Google Consent Mode with default settings on your WordPress page."
msgstr ""
"Povolte Google Consent Mode ve výchozím nastavení na stránce WordPress."

#: src/view/admin/cb_frame/settings/gcm-page.php:38
#: src/view/admin/cb_frame/settings/gcm-page.php:68
#: src/view/admin/cb_frame/settings/gtm-page.php:18
msgid "Read more"
msgstr "Další informace"

#: src/view/admin/cb_frame/settings/gcm-page.php:61
msgid "URL passthrough:"
msgstr "Passthrough adresy URL:"

#: src/view/admin/cb_frame/settings/gcm-page.php:64
msgid ""
"This feature will allow you to pass data between pages when not able to use "
"cookies without/prior consent."
msgstr ""
"Tato funkce vám umožní předávat data mezi stránkami, pokud není možné "
"používat soubory cookie bez předchozího souhlasu."

#: src/view/admin/cb_frame/settings/gcm-page.php:80
msgid "URL passthrough"
msgstr "Passthrough adresy URL"

#: src/view/admin/cb_frame/settings/gcm-page.php:89
msgid "Google Consent Mode cookies"
msgstr "Cookies Google Consent Mode"

#: src/view/admin/cb_frame/settings/gcm-page.php:91
msgid ""
"Select the cookie types that need to be consented for the Google Consent "
"Mode script"
msgstr ""
"Vyberte typy souborů cookie, které je třeba odsouhlasit pro skript Google "
"Consent Mode"

#: src/view/admin/cb_frame/settings/gcm-page.php:93
#: src/view/admin/cb_frame/settings/general-page.php:212
#: src/view/admin/cb_frame/settings/general-page.php:251
#: src/view/admin/cb_frame/settings/gtm-page.php:80
#: src/view/admin/uc_frame/settings/general-page.php:175
msgid "This feature is only available when using Manual Blocking"
msgstr "Tato funkce je k dispozici pouze při použití manuálního blokování"

#: src/view/admin/cb_frame/settings/gcm-page.php:95
#: src/view/admin/cb_frame/settings/gtm-page.php:82
msgid ""
"This option may affect the behaviour of your GTM Tags, as the script will "
"run on the selected cookies consent."
msgstr ""
"Tato možnost může ovlivnit chování vašich značek GTM, protože skript bude "
"spuštěn na základě souhlasu se zvolenými soubory cookies."

#: src/view/admin/cb_frame/settings/gcm-page.php:97
#: src/view/admin/cb_frame/settings/gtm-page.php:84
msgid ""
"Please make sure your Tags in Google Tag Manager are triggered correctly."
msgstr "Ujistěte se, že jsou značky ve Správci značek Google správně spuštěny."

#: src/view/admin/cb_frame/settings/gcm-page.php:102
#: src/view/admin/cb_frame/settings/gtm-page.php:89
#: src/view/admin/common/prior-consent/available-addons/tab.php:28
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:25
msgid "Check one or multiple cookie types:"
msgstr "Zaškrtněte jeden nebo více typů souborů cookie:"

#: src/view/admin/cb_frame/settings/general-page.php:30
msgid "Connect your Domain Group"
msgstr "Připojení skupiny domén"

#: src/view/admin/cb_frame/settings/general-page.php:33
msgid ""
"To connect your Domain Group, paste your Domain Group ID here. If you want "
"to connect a second ID for other regions, you can do this under the "
"\"Multiple Configurations\" tab."
msgstr ""
"K připojení skupiny domén je nutné vložit sem ID skupiny domén. Pokud budete "
"chtít připojit druhé ID pro další oblasti, můžete tak učinit na kartě Více "
"konfigurací)."

#: src/view/admin/cb_frame/settings/general-page.php:54
#: src/view/admin/uc_frame/settings/general-page.php:48
msgid "Using network account"
msgstr "Pomocí síťového účtu"

#: src/view/admin/cb_frame/settings/general-page.php:64
#: src/view/admin/uc_frame/settings/general-page.php:59
msgid "Do not use Network Settings ID"
msgstr "Nepoužívejte ID účtu nastavení sítě"

#: src/view/admin/cb_frame/settings/general-page.php:75
msgid "Language:"
msgstr "Jazyk:"

#: src/view/admin/cb_frame/settings/general-page.php:77
msgid "Select your main language here."
msgstr "Zde vyberte svůj hlavní jazyk."

#: src/view/admin/cb_frame/settings/general-page.php:82
msgid "Select the language"
msgstr "Vyberte jazyk"

#: src/view/admin/cb_frame/settings/general-page.php:86
msgid "Default (Autodetect)"
msgstr "Výchozí (automatické rozpoznání)"

#: src/view/admin/cb_frame/settings/general-page.php:90
msgid "Use WordPress Language"
msgstr "Použít jazyk WordPress"

#: src/view/admin/cb_frame/settings/general-page.php:111
msgid ""
"If enabled, Cookiebot™ will use the current location to set the banner and "
"cookie declaration language."
msgstr ""
"Pokud je tato volba povolena, Cookiebot™ použije aktuální lokalizaci pro "
"nastavení jazyka banneru a deklarace cookies."

#: src/view/admin/cb_frame/settings/general-page.php:114
msgid ""
"Please make sure that all languages in use have been added in the Cookiebot™ "
"Manager."
msgstr ""
"Ujistěte se prosím, že všechny používané jazyky byly přidány také do "
"Cookiebot™ Managera."

#: src/view/admin/cb_frame/settings/general-page.php:116
msgid "This feature disables the main language selector."
msgstr "Tato funkce deaktivuje hlavní výběr jazyka."

#: src/view/admin/cb_frame/settings/general-page.php:117
msgid ""
"If you have already set a language in the cookie declaration shortcode, this "
"feature will not change it."
msgstr ""
"Pokud jste již nastavili jazyk ve shortcode deklarace souborů cookie, tato "
"funkce jej nezmění."

#: src/view/admin/cb_frame/settings/general-page.php:120
msgid "Read more on how to add languages"
msgstr "Další informace o přidávání jazyků"

#: src/view/admin/cb_frame/settings/general-page.php:135
msgid "Use website location to set language"
msgstr "Použijte lokalizaci webových stránek k nastavení jazyka"

#: src/view/admin/cb_frame/settings/general-page.php:149
#: src/view/admin/uc_frame/network-settings-page.php:113
#: src/view/admin/uc_frame/settings/general-page.php:123
msgid ""
"Choose the type of your cookie-blocking mode. Select automatic to "
"automatically block all cookies except those strictly necessary to use "
"before user gives consent. Manual mode lets you adjust your cookie settings "
"within your website’s HTML."
msgstr ""
"Vyberte typ režimu blokování souborů cookie. Vyberte automatický, pokud "
"chcete automaticky blokovat všechny soubory cookie kromě těch, které jsou "
"nezbytně nutné k použití před udělením souhlasu uživatelem. Ruční režim "
"umožňuje upravit nastavení souborů cookie v rámci HTML vašeho webu."

#: src/view/admin/cb_frame/settings/general-page.php:154
msgid "Guide to auto cookie-blocking"
msgstr "Průvodce automatickým blokováním souborů cookie"

#: src/view/admin/cb_frame/settings/general-page.php:160
msgid "Guide to manual cookie-blocking"
msgstr "Průvodce ručním blokováním souborů cookie"

#: src/view/admin/cb_frame/settings/general-page.php:184
#: src/view/admin/uc_frame/settings/general-page.php:150
msgid "Manual"
msgstr "Ruční"

#: src/view/admin/cb_frame/settings/general-page.php:209
msgid "Add async or defer attribute to cookie declaration script tag"
msgstr ""
"Přidat asynchronní nebo odložit atribut na značku skriptu pro prohlášení o "
"souborech cookie"

#: src/view/admin/cb_frame/settings/gtm-page.php:12
msgid "Google Tag Manager:"
msgstr "Správce značek Google:"

#: src/view/admin/cb_frame/settings/gtm-page.php:14
msgid "For more details about Cookiebot CMP and Google Tag Manager."
msgstr "Další podrobnosti o pluginu Cookiebot CMP a Správci značek Google."

#: src/view/admin/cb_frame/settings/gtm-page.php:35
#: src/view/admin/cb_frame/settings/gtm-page.php:46
#: src/view/admin/uc_frame/settings/gtm-page.php:35
#: src/view/admin/uc_frame/settings/gtm-page.php:46
msgid "Google Tag Manager ID"
msgstr "ID Správce značek Google"

#: src/view/admin/cb_frame/settings/gtm-page.php:37
msgid "Paste your Tag Manager ID into the field on the right."
msgstr "Vložte ID Správce značek do pole vpravo."

#: src/view/admin/cb_frame/settings/gtm-page.php:41
#: src/view/admin/uc_frame/settings/gtm-page.php:41
msgid "How to find the GTM ID"
msgstr "Jak najít ID Správce značek"

#: src/view/admin/cb_frame/settings/gtm-page.php:48
msgid "Enter GTM ID"
msgstr "Zadejte ID Správce značek"

#: src/view/admin/cb_frame/settings/gtm-page.php:56
msgid "Data Layer Name (optional)"
msgstr "Název datové vrstvy (volitelné)"

#: src/view/admin/cb_frame/settings/gtm-page.php:58
msgid ""
"You can also paste your Data Layer Name here. This is optional information."
msgstr "Název datové vrstvy můžete také vložit sem. Tyto údaje jsou volitelné."

#: src/view/admin/cb_frame/settings/gtm-page.php:62
#: src/view/admin/uc_frame/settings/gtm-page.php:62
msgid "How to find the Data Layer Name"
msgstr "Jak najít název datové vrstvy"

#: src/view/admin/cb_frame/settings/gtm-page.php:67
#: src/view/admin/uc_frame/settings/gtm-page.php:67
msgid "Name of your Data Layer"
msgstr "Název datové vrstvy"

#: src/view/admin/cb_frame/settings/gtm-page.php:76
msgid "Google Tag Manager cookies"
msgstr "Cookies Google Tag Manager"

#: src/view/admin/cb_frame/settings/gtm-page.php:78
msgid ""
"Select the cookie types that need to be consented for the Google Tag Manager "
"script"
msgstr ""
"Vyberte typy souborů cookie, které je třeba odsouhlasit pro skript Google "
"Tag Manager"

#: src/view/admin/cb_frame/settings/iab-page.php:26
msgid "IAB Integration:"
msgstr "Integrace řešení IAB:"

#: src/view/admin/cb_frame/settings/iab-page.php:28
msgid ""
"If you want to use the IAB Framework TCF within your Consent Management "
"Platform (CMP) you can enable it on the right. Be aware that activating this "
"could override some of the configurations you made with the default setup "
"defined by the IAB."
msgstr ""
"Pokud chcete použít řešení Transparency & Consent Framework organizace IAB "
"(TCF) v rámci platformy pro správu souhlasu (Consent Management Platform, "
"CMP), můžete ho povolit vpravo. Mějte přitom na paměti, že aktivací této "
"funkce se mohou některé konfigurace, které jste provedli, přepsat výchozím "
"nastavením definovaným organizací IAB."

#: src/view/admin/cb_frame/settings/iab-page.php:32
msgid "Read more on IAB with Cookiebot CMP here"
msgstr ""
"Další informace o řešení IAB v rámci pluginu Cookiebot CMP naleznete zde"

#: src/view/admin/cb_frame/settings/iab-page.php:41
msgid "IAB TCF V2.2 integration"
msgstr "Integrace řešení TCF 2.2 organizace IAB"

#: src/view/admin/cb_frame/settings/iab-page.php:83
msgid ""
"IAB vendor list is temporarily offline. Please try refreshing the page after "
"a couple of minutes."
msgstr ""
"Seznam dodavatelů IAB je dočasně offline. Zkuste stránku za několik minut "
"aktualizovat."

#: src/view/admin/cb_frame/settings/iab-page.php:85
msgid ""
"If you had previously saved configurations, don’t worry, they will continue "
"to work."
msgstr "Pokud máte uložené konfigurace, nebojte se. Budou i nadále fungovat."

#: src/view/admin/cb_frame/settings/iab-page.php:108
msgid "Sharing data with third-party vendors"
msgstr "Sdílení dat s dodavateli třetích stran"

#: src/view/admin/cb_frame/settings/iab-page.php:112
msgid ""
"Select vendors with whom you’ll share users’ data. We’ll include this "
"information on the second layer of your consent banner, where users "
"interested in more granular detail about who will access their data can view "
"it."
msgstr ""
"Vyberte dodavatele, se kterými budete sdílet data uživatelů. Tyto informace "
"zahrneme do druhé vrstvy vašeho banneru souhlasu, který si uživatelé mohou "
"zobrazit, pokud mají zájem o podrobnější informace o tom, kdo bude mít k "
"jejich datům přístup."

#: src/view/admin/cb_frame/settings/iab-page.php:130
#: src/view/admin/cb_frame/settings/iab-page.php:162
#: src/view/admin/cb_frame/settings/iab-page.php:218
msgid "Search"
msgstr "Vyhledávání"

#: src/view/admin/cb_frame/settings/iab-page.php:131
#: src/view/admin/cb_frame/settings/iab-page.php:163
msgid "Select All"
msgstr "Vybrat vše"

#: src/view/admin/cb_frame/settings/iab-page.php:132
#: src/view/admin/cb_frame/settings/iab-page.php:164
msgid "Deselect All"
msgstr "Zrušit výběr všeho"

#: src/view/admin/cb_frame/settings/iab-page.php:133
msgid "Select at least one vendor"
msgstr "Vyberte alespoň jednoho dodavatele"

#: src/view/admin/cb_frame/settings/iab-page.php:157
msgid "Google Ads certified external vendors"
msgstr "Google Ads certifikuje externí dodavatele"

#: src/view/admin/cb_frame/settings/iab-page.php:188
msgid "Restrictions of data use purposes for vendors"
msgstr "Omezení použití dat pro dodavatele"

#: src/view/admin/cb_frame/settings/iab-page.php:192
msgid ""
"Set restrictions on data use purposes for specific vendors. Add vendors and "
"the data use purposes that each vendor is allowed. We’ll share this "
"information with users within your consent banner."
msgstr ""
"Nastavit omezení pro účely použití dat pro konkrétní dodavatele. Přidejte "
"dodavatele a účely použití dat, které jsou pro jednotlivé dodavatele "
"povoleny. Tyto informace budeme s uživateli sdílet na vašem banneru souhlasu."

#: src/view/admin/cb_frame/settings/iab-page.php:198
msgid "Add Vendor"
msgstr "Přidat dodavatele"

#: src/view/admin/cb_frame/settings/iab-page.php:204
#: src/view/admin/cb_frame/settings/iab-page.php:215
msgid "Select Vendor"
msgstr "Vyberte dodavatele"

#: src/view/admin/cb_frame/settings/iab-page.php:228
msgid "Set Purposes"
msgstr "Nastavit účely"

#: src/view/admin/cb_frame/settings/multiple-configuration/extra/region-item.php:18
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:75
#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:101
msgid "Select region"
msgstr "Vyberte oblast"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:16
msgid "Additional configurations:"
msgstr "Další konfigurace:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:18
msgid ""
"You can add a second alternative banner or configuration to your website by "
"creating a second Domain Group and specify it on a region."
msgstr ""
"Vytvořením druhé skupiny domén a jejím určením pro region si na web můžete "
"přidat druhý alternativní banner nebo konfiguraci."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:22
msgid "Read more about multiple configurations here"
msgstr "Další informace o možnosti více konfigurací naleznete zde"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:34
msgid "Multiple configurations"
msgstr "Více konfigurací"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:44
msgid "Set up your additional banner configuration:"
msgstr "Nastavte další konfiguraci banneru:"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:47
msgid ""
"To enable a different configuration, create a separate DomainGroup without "
"adding the domain to it and paste the ID below. Then select the countries in "
"which you want to show this configuration. For example, if your main Domain "
"Group is defined as a banner matching GDPR requirements, you might want to "
"add another Domain Group for visitors from California. The number of "
"additional configurations is restricted to one at the moment."
msgstr ""
"Jinou konfiguraci povolíte tak, že vytvoříte samostatnou skupinu domén bez "
"přidání domény do této skupiny a její ID vložíte níže. Poté vyberete země, "
"ve kterých chcete tuto konfiguraci zobrazovat. Pokud je například vaše "
"hlavní skupina domén definována jako banner odpovídající požadavkům GDPR, "
"můžete přidat další skupinu domén pro návštěvníky z Kalifornie. Počet "
"dalších konfigurací je momentálně omezen na jednu."

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:57
msgid "Domain Group ID"
msgstr "ID skupiny domén"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:61
msgid "Region"
msgstr "Oblast"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:71
msgid "Primary domain group"
msgstr "Primární skupina domén"

#: src/view/admin/cb_frame/settings/multiple-configuration/page.php:138
msgid "Add banner"
msgstr "Přidat banner"

#: src/view/admin/cb_frame/support-page.php:23
msgid "Need help with your configuration?"
msgstr "Potřebujete s konfigurací poradit?"

#: src/view/admin/cb_frame/support-page.php:26
msgid ""
"In our Help Center you find all the answers to your questions. If you have "
"additional questions, create a support request and our Support Team will "
"help out as soon as possible."
msgstr ""
"V našem centru nápovědy naleznete všechny odpovědi na vaše dotazy. Pokud vás "
"bude zajímat něco dalšího, vyplňte formulář podpory a náš tým podpory vám co "
"nejdříve pokusí pomoci."

#: src/view/admin/cb_frame/support-page.php:34
msgid "Visit Cookiebot CMP Help Center"
msgstr "Navštívit centrum nápovědy Cookiebot CMP"

#: src/view/admin/cb_frame/support-page.php:58
msgid "How to find my Cookiebot™ ID"
msgstr "Jak najdu Cookiebot™ ID"

#. translators: the first placeholder string will be replaced with a html anchor open tag and the second placeholder string will be replaced by the html anchor closing tag
#: src/view/admin/cb_frame/support-page.php:66
msgid "Log in to your %1$sCookiebot CMP account%2$s."
msgstr "Přihlaste se %1$sk účtu Cookiebot CMP%2$s."

#. translators: the placeholder strings denote the positions of <b>, </b>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:76
msgid "Go to %1$s\"Settings\"%2$s and setup your Cookiebot CMP"
msgstr "Přejděte %1$sdo části Nastavení%2$s a nastavte plugin Cookiebot CMP"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:86
msgid "Go to the %1$s\"Your scripts\"%2$s tab"
msgstr "Přejděte %1$sna kartu Vaše skripty%2$s"

#: src/view/admin/cb_frame/support-page.php:92
msgid ""
"Copy the value inside the data-cid parameter - eg.: abcdef12-3456-7890-abcd-"
"ef1234567890"
msgstr ""
"Zkopírujte hodnotu do parametru data-cid – např.: abcdef12-3456-7890-abcd-"
"ef1234567890"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:97
msgid ""
"Add %1$s[cookie_declaration]%2$s shortcode to a page to show the declaration"
msgstr ""
"Pokud chcete zobrazit prohlášení, přidejte do stránky zkrácený kód "
"%1$s[cookie_declaration]%2$s"

#: src/view/admin/cb_frame/support-page.php:103
msgid "Remember to change your scripts as described below"
msgstr "Nezapomeňte změnit skripty podle níže uvedeného postupu"

#: src/view/admin/cb_frame/support-page.php:110
msgid "Add the Cookie Declaration to your website"
msgstr "Přidejte si na web prohlášení o souborech cookie"

#. translators: the placeholder strings denote the positions of <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:116
msgid ""
"Use the shortcode %1$s[cookie_declaration]%2$s to add the cookie declaration "
"to a page or post. The cookie declaration will always show the latest "
"version from Cookiebot CMP."
msgstr ""
"Použijte zkrácený kód %1$s[cookie_declaration]%2$spro přidání prohlášení o "
"souborech cookie na stránku nebo příspěvek. Prohlášení o souborech cookie "
"bude vždy zobrazovat nejnovější verzi z platformy pro správu souhlasu "
"Cookiebot CMP."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:126
msgid ""
"If you want to show the cookie declaration in a specific language, you can "
"add the %1$s\"lang\"%2$s attribute, e.g. %3$s[cookie_declaration "
"lang=\"de\"]%4$s."
msgstr ""
"Pokud budete chtít zobrazit prohlášení o souborech cookie v určitém jazyce, "
"můžete přidat atribut %1$s”lang”%2$s, např. %3$s[cookie_declaration "
"lang=“de”]%4$s."

#: src/view/admin/cb_frame/support-page.php:138
msgid "Update your script tags"
msgstr "Aktualizujte značky skriptu"

#: src/view/admin/cb_frame/support-page.php:142
msgid ""
"To enable prior consent, apply the attribute \"data-cookieconsent\" to "
"cookie-setting script tags on your website. Set the comma-separated value to "
"one or more of the cookie categories \"preferences\", \"statistics\" and/or "
"\"marketing\" in accordance with the types of cookies being set by each "
"script. Finally, change the attribute \"type\" from \"text/javascript\" to "
"\"text/plain\"."
msgstr ""
"Pokud budete chtít povolit předchozí souhlas, použijte atribut “data-"
"cookieconsent” pro značky skriptu pro nastavení souborů cookie na vašem "
"webu. Nastavte hodnotu oddělenou čárkou pro jednu nebo více kategorií "
"souborů cookie „preference“, „statistika“ nebo „marketing“ v souladu s typy "
"souborů cookie nastavených jednotlivými skripty. Nakonec změňte atribut "
"“type” z “text/javascript” na “text/plain”."

#. translators: the placeholder strings denote the positions of <i>, </i>, <b> and </b> HTML tags
#: src/view/admin/cb_frame/support-page.php:152
msgid ""
"Example on modifying an existing Google Analytics Universal script tag can "
"be found %1$shere in step 4%2$s."
msgstr ""
"Příklad úpravy stávající značky skriptu pro Google Analytics naleznete "
"%1$szde v kroku 4%2$s."

#: src/view/admin/cb_frame/support-page.php:172
msgid "Helper function to update your scripts"
msgstr "Pomocná funkce pro aktualizaci skriptů"

#: src/view/admin/cb_frame/support-page.php:176
msgid ""
"You can update your scripts yourself. However, Cookiebot CMP also offers a "
"small helper function that can make the work easier."
msgstr ""
"Skripty můžete aktualizovat sami. Plugin Cookiebot CMP však také nabízí "
"malou pomocnou funkci, která vám to usnadní."

#: src/view/admin/cb_frame/support-page.php:183
msgid "Update your script tags this way:"
msgstr "Značky skriptu aktualizujete takto:"

#. translators: %1$s refers to the original script tag HTML, and %2$s refers to its replacement
#: src/view/admin/cb_frame/support-page.php:188
msgid "%1$s to %2$s"
msgstr "%1$s až %2$s"

#: src/view/admin/common/dashboard-page.php:31
msgid "I already have an account"
msgstr "Už mám účet"

#: src/view/admin/common/dashboard-page.php:35
msgid "Connect my account"
msgstr "Chci účet připojit"

#: src/view/admin/common/dashboard-page.php:48
#: src/view/admin/common/network-settings-page.php:33
#: src/view/admin/common/settings-page.php:37
msgid "New to our solutions? Create your account. "
msgstr "Teprve s našimi řešeními začínáte? Vytvořte si účet."

#: src/view/admin/common/dashboard-page.php:65
msgid "How to get started"
msgstr "Jak začít"

#: src/view/admin/common/dashboard-page.php:69
msgid "Learn more about your CMP"
msgstr "Další informace o vaší platformě pro správu souhlasů"

#: src/view/admin/common/network-settings-page.php:35
#: src/view/admin/common/settings-page.php:39
msgid ""
"If you’re new to our solutions, create an account first to obtain your "
"settings ID."
msgstr ""
"Pokud s našimi řešeními teprve začínáte, vytvořte si nejdřív účet, abyste "
"získali ID účtu."

#: src/view/admin/common/network-settings-page.php:40
#: src/view/admin/common/settings-page.php:44
msgid "Create your account"
msgstr "Vytvořit účet"

#: src/view/admin/common/network-settings-page.php:49
#: src/view/admin/common/settings-page.php:52
#: src/view/admin/uc_frame/network-settings-page.php:51
msgid "Connect your account"
msgstr "Připojit účet"

#: src/view/admin/common/network-settings-page.php:52
#: src/view/admin/common/settings-page.php:55
msgid "Enter the ID of your account to quickly connect it with the plugin."
msgstr "Zadejte ID svého účtu pro rychlé připojení k pluginu."

#: src/view/admin/uc_frame/network-settings-page.php:54
msgid "Enter your settings ID to quickly link your account with the plugin."
msgstr "Zadáním ID účtu rychle propojíte svůj účet s pluginem."

#: src/view/admin/common/network-settings-page.php:55
#: src/view/admin/uc_frame/network-settings-page.php:57
msgid ""
"If added this will be the default account for all subsites. Subsites are "
"able to override this and use their own account."
msgstr ""
"Pokud ID přidáte, bude platit jako výchozí pro všechny dílčí weby. Dílčí "
"weby ho můžou přepsat a použít vlastní účet."

#: src/view/admin/uc_frame/network-settings-page.php:61
#: src/view/admin/uc_frame/settings/general-page.php:29
msgid "Where to find settings ID"
msgstr "Kde najít ID účtu"


#: src/view/admin/common/network-settings-page.php:63
#: src/view/admin/common/settings-page.php:63
msgid "How to find your Usercentrics Settings ID"
msgstr "Jak najít své ID nastavení Usercentrics"

#: src/view/admin/common/network-settings-page.php:70
#: src/view/admin/common/settings-page.php:70
msgid "How to find your Cookiebot CMP Domain Group ID"
msgstr "Jak najít své ID skupiny domén CMP Cookiebot"

#: src/view/admin/common/network-settings-page.php:65
#: src/view/admin/common/settings-page.php:65
msgid "Settings ID or Domain Group ID"
msgstr "ID nastavení nebo ID skupiny domén"

#: src/view/admin/uc_frame/network-settings-page.php:67
#: src/view/admin/uc_frame/settings/general-page.php:35
msgid "Your settings ID"
msgstr "ID vašeho účtu"

#: src/view/admin/common/network-settings-page.php:82
#: src/view/admin/common/settings-page.php:82
#: src/view/admin/uc_frame/network-settings-page.php:83
#: src/view/admin/uc_frame/settings/general-page.php:70
msgid ""
"Let us know if your account is set for compliance with a single privacy law "
"(e.g. GDPR) or multiple laws (e.g. GDPR and CCPA) based on user’s location. "
"The default is a single privacy law, so this is likely your setting unless "
"modified."
msgstr ""
"Dejte nám vědět, jestli máte účet nastavený tak, aby byl v souladu s jedním "
"zákonem o ochraně osobních údajů (např. GDPR) nebo několika zákony (např. "
"GDPR a CCPA) na základě polohy uživatele. Výchozí nastavení je jeden zákon "
"o ochraně osobních údajů, takže nejspíš platí i pro vás, pokud jste nic "
"neměnili."

#: src/view/admin/common/network-settings-page.php:88
#: src/view/admin/common/settings-page.php:88
#: src/view/admin/uc_frame/network-settings-page.php:89
#: src/view/admin/uc_frame/settings/general-page.php:76
msgid "Your current account setup:"
msgstr "Vaše aktuální nastavení účtu:"

#: src/view/admin/common/network-settings-page.php:95
#: src/view/admin/common/settings-page.php:95
#: src/view/admin/uc_frame/network-settings-page.php:96
#: src/view/admin/uc_frame/settings/general-page.php:83
msgid "Compliance with one privacy law"
msgstr "Soulad s jedním zákonem o ochraně osobních údajů"

#: src/view/admin/common/network-settings-page.php:102
#: src/view/admin/common/settings-page.php:102
#: src/view/admin/uc_frame/network-settings-page.php:103
#: src/view/admin/uc_frame/settings/general-page.php:90
msgid "Compliance with multiple privacy laws (geolocation)"
msgstr "Soulad s několika zákony o ochraně osobních údajů (geolokace)"

#: src/view/admin/common/prior-consent/available-addons/tab.php:25
msgid "Plugin enabled"
msgstr "Plugin povolen"

#: src/view/admin/common/prior-consent/available-addons/tab.php:58
msgid "Placeholder text:"
msgstr "Zástupný text:"

#: src/view/admin/common/prior-consent/available-addons/tab.php:67
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:53
msgid "Display a placeholder"
msgstr "Zobrazí zástupný text"

#: src/view/admin/common/prior-consent/available-addons/tab.php:78
#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:71
msgid "+ Add language"
msgstr "+ Přidat jazyk"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:7
msgid "Jetpack settings"
msgstr "Nastavení Jetpack"

#: src/view/admin/common/prior-consent/jetpack-widgets/tab-header.php:8
msgid "Enable Jetpack on \"Available Addons\" to see this page options."
msgstr ""
"Pokud budete chtít zobrazit tyto možnosti stránky, povolte v části Dostupné "
"doplňky službu Jetpack."

#: src/view/admin/common/prior-consent/jetpack-widgets/tab.php:15
msgid "Enable"
msgstr "Povolit"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:11
msgid "Show advanced options"
msgstr "Zobrazit rozšířené možnosti"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:17
msgid "This is for more advanced users."
msgstr "Určeno pokročilejším uživatelům."

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:23
msgid "Regex:"
msgstr "Regulární výrazy:"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:35
msgid "Edit regex"
msgstr "Upravit regulární výraz"

#: src/view/admin/common/prior-consent/other-addons/embed-autocorrect-extra-addon-options.php:49
msgid "Reset to default regex"
msgstr "Obnovit výchozí nastavení"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:4
msgid "Information"
msgstr "Informace"

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:5
msgid ""
"These add-ons are created by a dedicated open-source community to make it "
"easier for you to manage cookie and tracker consent on your WordPress site. "
"They’re designed to help you ensure ‘prior consent’ even for plugins that "
"don’t include this feature."
msgstr ""
"Tyto doplňky vytváří komunita nadšených open-source vývojářů, aby vám "
"usnadnila správu souhlasu se soubory cookie a sledováním na vašem webu "
"WordPress. Jsou navržené tak, aby vám pomohly zajistit „předchozí souhlas“ "
"i pro pluginy, které tuto funkci neobsahují."

#: src/view/admin/common/prior-consent/partials/info-tab-header.php:6
msgid ""
"Right now, these add-ons are the best way for you to signal user consent to "
"other plugins. While we don’t know if or when WordPress Core will add this "
"functionality, these tools are here to support you and work seamlessly with "
"Usercentrics solution."
msgstr ""
"Tyto doplňky jsou momentálně nejlepším způsobem, jak signalizovat souhlas "
"uživatele s jinými pluginy. I když nevíme, jestli nebo kdy tuto funkci "
"přidají vývojáři samotného WordPressu, tyto nástroje máte k dispozici už "
"teď, aby vám pomáhaly a bezproblémově spolupracovaly s řešením Usercentrics."

#: src/view/admin/common/prior-consent/partials/placeholder-submitbox-default.php:10
#: src/view/admin/common/prior-consent/partials/placeholder-submitboxes.php:19
#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:26
msgid "Language"
msgstr "Jazyk"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:4
msgid "Unavailable plugins"
msgstr "Nedostupné pluginy"

#: src/view/admin/common/prior-consent/unavailable-addons/tab-header.php:5
msgid ""
"The following addons are unavailable. This is because the corresponding "
"plugin is not installed or activated."
msgstr ""
"Následující doplňky nejsou k dispozici. Není totiž nainstalován nebo "
"aktivován odpovídající plugin."

#: src/view/admin/common/support-page.php:23
#: src/view/admin/uc_frame/support-page.php:23
msgid "Need help?"
msgstr "Potřebujete poradit?"

#: src/view/admin/common/support-page.php:26
#: src/view/admin/uc_frame/support-page.php:26
msgid ""
"Visit our Support Center to find answers to your questions or get help with "
"configuration. If you need further assistance, use the Contact Support "
"button in the top navigation to create a support request. We’ll respond as "
"soon as possible."
msgstr ""
"Navštivte naše Centrum podpory, kde najdete odpovědi na dotazy nebo získáte "
"pomoc s konfigurací. Pokud byste tam nenašli, co hledáte, vytvořte pomocí "
"tlačítka Kontaktovat podporu v horní části navigační nabídky vlastní "
"požadavek na podporu. Odpovíme co nejdříve."

#: src/view/admin/common/support-page.php:34
#: src/view/admin/uc_frame/support-page.php:34
msgid "Go to Support Center"
msgstr "Přejít do Centra podpory"

#: src/view/admin/common/templates/extra/cbid-disconnect-alert.php:6
msgid "You will need to add a new ID before updating other settings"
msgstr "Před aktualizací dalších nastavení budete muset přidat nové ID"

#: src/view/admin/common/templates/extra/review-form.php:10
msgid "Cookiebot CMP Deactivation"
msgstr "Deaktivace pluginu Cookiebot CMP"

#: src/view/admin/common/templates/extra/review-form.php:14
msgid "We are sad to lose you. Take a moment to help us improve?"
msgstr ""
"Mrzí nás, že o vás přicházíme. Věnujte nám chvilku, abychom se mohli zlepšit?"

#: src/view/admin/common/templates/extra/review-form.php:19
msgid "The installation is too complicated"
msgstr "Instalace je příliš složitá"

#: src/view/admin/common/templates/extra/review-form.php:26
msgid "I found a plugin that better serves my needs"
msgstr "Mým potřebám lépe vyhovuje jiný plugin"

#: src/view/admin/common/templates/extra/review-form.php:33
msgid "Missing features / did not meet my expectations"
msgstr "Chybí mi funkce / stávající nenaplnily moje očekávání"

#: src/view/admin/common/templates/extra/review-form.php:40
msgid "I need more customization options"
msgstr "Potřebuji více možností přizpůsobení"

#: src/view/admin/common/templates/extra/review-form.php:47
msgid "The premium plan is too expensive"
msgstr "Prémiový tarif je příliš drahý"

#: src/view/admin/common/templates/extra/review-form.php:54
msgid "I’m only deactivating the plugin temporarily"
msgstr "Plugin chci deaktivovat pouze dočasně"

#: src/view/admin/common/templates/extra/review-form.php:61
msgid "Other"
msgstr "Jiné"

#: src/view/admin/common/templates/extra/review-form.php:65
msgid "Please specify here"
msgstr "Uveďte zde"

#: src/lib/Cookiebot_Review.php:105
msgid "(Optional)"
msgstr "(volitelně)"

#: src/lib/Cookiebot_Review.php:106
msgid " By checking this box, you agree to submit troubleshooting information and allow us to contact you regarding the problem if necessary."
msgstr " Zaškrtnutím tohoto políčka souhlasíte s odesláním informací o odstraňování problému a umožníte nám, abychom vás v případě potřeby ohledně odstranění problému kontaktovali."

#: src/lib/Cookiebot_Review.php:110
msgid "The information will be kept for no longer than 90 days. You may revoke this consent at any time, e.g. by sending an email to "
msgstr "Informace budou uchovávány po dobu maximálně 90 dnů. Tento souhlas můžete kdykoli odvolat, např. zasláním e-mailu na adresu "

#: src/lib/Cookiebot_Review.php:117
msgid "Skip and Deactivate"
msgstr "Přeskočit a deaktivovat"

#: src/lib/Cookiebot_Review.php:118
msgid "Submit and Deactivate"
msgstr "Odeslání a deaktivace"

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:6
msgid ""
"If there is a network settings ID connected it will be used for this subsite, "
"if not you will need to add a new ID before updating other settings"
msgstr ""
"Pokud je připojeno ID síťového účtu, bude použito pro tento dílčí web; pokud "
"ne, budete muset před aktualizací dalších nastavení přidat nové ID"

#: src/view/admin/common/templates/extra/subsite-disconnect-alert.php:13
msgid "Disconnect subsite account"
msgstr "Odpojit účet dílčího webu"

#: src/view/admin/common/templates/header.php:10
msgid "Changes has been saved"
msgstr "Změny byly uloženy"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:13
msgid "Absolutely, you deserve it!"
msgstr "Rozhodně, zasloužíte si to"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:24
msgid "Maybe later?"
msgstr "Možná později?"

#: src/view/admin/notices/cookiebot-recommendation-notice-links.php:35
msgid "I`ve already left my feedback"
msgstr "Už jsem zanechal zpětnou vazbu"

#: src/view/admin/uc_frame/dashboard-page.php:30
msgid "Welcome to Usercentrics Cookiebot WordPress Plugin"
msgstr "Vítejte v pluginu Usercentrics Cookiebot pro WordPress"

#: src/view/admin/uc_frame/dashboard-page.php:38
msgid ""
"You’ve added your settings ID to your Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Přidali jste ID svého účtu do pluginu Usercentrics Cookiebot pro WordPress."

#: src/view/admin/uc_frame/dashboard-page.php:51
msgid ""
"Are you happy with Usercentrics Cookiebot WordPress Plugin? Your feedback "
"helps us improve it."
msgstr ""
"Jste s pluginem Usercentrics Cookiebot pro WordPress spokojeni? Vaše zpětná "
"vazba nám ho pomáhá vylepšovat."

#: src/view/admin/uc_frame/dashboard-page.php:55
msgid "Share feedback"
msgstr "Poskytnout zpětnou vazbu"

#: src/view/admin/uc_frame/dashboard-page.php:68
msgid "How to set up Usercentrics Cookiebot WordPress Plugin"
msgstr "Jak plugin Usercentrics Cookiebot pro WordPress nastavit"

#: src/view/admin/uc_frame/dashboard-page.php:72
#: src/view/admin/uc_frame/settings/gcm-page.php:21
#: src/view/admin/uc_frame/settings/general-page.php:127
#: src/view/admin/uc_frame/settings/gtm-page.php:18
msgid "Learn more"
msgstr "Zjistěte více"

#: src/view/admin/uc_frame/dashboard-page.php:115
msgid "Visit our blog to learn about other legislations"
msgstr "Informace o dalších právních předpisech najdete na našem blogu"

#: src/view/admin/uc_frame/debug-page.php:22
msgid "Debug your plugin"
msgstr "Ladění pluginu"

#: src/view/admin/uc_frame/debug-page.php:25
msgid ""
"If you encounter any issues with your Usercentrics Cookiebot WordPress "
"Plugin, provide the information below to help us assist you. Visit our "
"Support Center and send us a copy of what is displayed in the window below."
msgstr ""
"Pokud u pluginu Usercentrics Cookiebot pro WordPress narazíte na problém, "
"poskytněte nám níže uvedené informace, abychom vám s ním mohli pomoci. "
"Navštivte naše Centrum podpory a pošlete nám kopii toho, co se zobrazuje "
"v okně níže."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:23
msgid "WP Consent API Settings"
msgstr "Nastavení pluginu WP Consent API"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:32
msgid ""
"WP Consent API and Usercentrics Cookiebot WordPress Plugin categorize "
"cookies a bit differently. The default settings should fit most needs, but "
"if you need to change the mapping you can do so below."
msgstr ""
"Pluginy WP Consent API a Usercentrics Cookiebot pro WordPress kategorizují "
"soubory cookie každý trochu jinak. Výchozí nastavení by mělo odpovídat "
"většině potřeb, ale pokud potřebujete mapování změnit, můžete tak učinit "
"níže."

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:48
msgid "Usercentrics Cookiebot cookie categories"
msgstr "Kategorie souborů cookie v pluginu Usercentrics Cookiebot"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:58
msgid "essential"
msgstr "nezbytné"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:72
msgid "functional"
msgstr "funkční"

#: src/view/admin/uc_frame/prior-consent/consent-api/tab.php:106
msgid "Reset to default categories"
msgstr "Obnovit výchozí nastavení"

#: src/view/admin/uc_frame/settings-page.php:48
msgid "Save changes"
msgstr "Uložit změny"

#: src/view/admin/uc_frame/settings-page.php:70
#: src/view/admin/uc_frame/settings/embeddings-page.php:4
msgid "Privacy Policy Sync"
msgstr "Synchronizace zásad ochrany osobních údajů"

#: src/view/admin/uc_frame/settings/embeddings-page.php:7
msgid ""
"Use our pre-defined, automatically generated embeddings to help you keep "
"your Privacy Policy page in sync with your consent banner settings. This "
"feature saves you time by automatically updating legally required "
"information, so you don’t need to manually copy data into your Privacy "
"Policy page. Once you’re done setting the options below, simply copy the "
"code and paste it into your Privacy Policy page."
msgstr ""
"Použijte naše předdefinované, automaticky generované vkládací prvky, které "
"vám pomohou udržet vaši stránku se zásadami ochrany osobních údajů "
"synchronizovanou s nastavením banneru souhlasu. Tato funkce vám ušetří čas – "
"automaticky totiž aktualizuje zákonem požadované informace, takže údaje "
"nemusíte na stránku se zásadami ochrany osobních údajů kopírovat ručně. Až "
"dokončíte nastavení níže uvedených možností, jednoduše zkopírujte kód "
"a vložte ho na stránku se zásadami ochrany osobních údajů."

#: src/view/admin/uc_frame/settings/embeddings-page.php:12
msgid "Copy shortcode"
msgstr "Zkopírovat zkrácený kód"

#: src/view/admin/uc_frame/settings/embeddings-page.php:23
msgid "Sync options for privacy legislations"
msgstr "Možnosti synchronizace pro legislativu týkající se ochrany soukromí"

#: src/view/admin/uc_frame/settings/embeddings-page.php:26
msgid ""
"Select the legislation you want to automatically sync with your Privacy "
"Policy page."
msgstr ""
"Vyberte legislativu, kterou chcete automaticky synchronizovat se stránkou "
"Zásady ochrany osobních údajů."

#: src/view/admin/uc_frame/settings/embeddings-page.php:41
msgid "Sync options for data processing services (DPS) "
msgstr "Možnosti synchronizace pro služby zpracování dat (DPS)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:44
msgid ""
"Define what to include on your Privacy Policy page: DPS categories only, "
"categories with their services, a single service, or detailed information on "
"both categories and services. Choose based on the level of detail you want "
"to display."
msgstr ""
"Definujte, co budete na stránce se zásadami ochrany osobních údajů uvádět: "
"Pouze kategorie služeb zpracování dat s příslušnými službami, jen jednu "
"službu nebo podrobné informace o kategoriích i službách. Vyberte podle míry "
"podrobností, které chcete zobrazit."

#: src/view/admin/uc_frame/settings/embeddings-page.php:50
msgid "Services (Default)"
msgstr "Služby (výchozí)"

#: src/view/admin/uc_frame/settings/embeddings-page.php:51
msgid "Categories and services"
msgstr "Kategorie a služby"

#: src/view/admin/uc_frame/settings/embeddings-page.php:52
msgid "Categories only"
msgstr "Pouze kategorie"

#: src/view/admin/uc_frame/settings/embeddings-page.php:53
msgid "Single service"
msgstr "Jedna služba"

#: src/view/admin/uc_frame/settings/embeddings-page.php:54
msgid "Purposes"
msgstr "Účely"

#: src/view/admin/uc_frame/settings/embeddings-page.php:55
msgid "Vendors"
msgstr "Dodavatelé"

#: src/view/admin/uc_frame/settings/embeddings-page.php:63
msgid "Single Service ID"
msgstr "ID jedné služby"

#: src/view/admin/uc_frame/settings/embeddings-page.php:66
msgid "Add the service ID that you want to display."
msgstr "Přidejte ID služby, kterou chcete zobrazit."

#: src/view/admin/uc_frame/settings/embeddings-page.php:69
msgid "This feature is required."
msgstr "Tato funkce je povinná."

#: src/view/admin/uc_frame/settings/embeddings-page.php:81
msgid "Privacy toggles"
msgstr "Přepínače ochrany osobních údajů"

#: src/view/admin/uc_frame/settings/embeddings-page.php:84
msgid ""
"Define whether you want the privacy toggles to be enabled and displayed on "
"your Privacy Policy page."
msgstr ""
"Definujte, jestli chcete na stránce Zásady ochrany osobních údajů povolit "
"a zobrazit přepínače ochrany osobních údajů."

#: src/view/admin/uc_frame/settings/embeddings-page.php:92
msgid "Enable privacy toggles"
msgstr "Povolit přepínače ochrany osobních údajů"

#: src/view/admin/uc_frame/settings/gcm-page.php:14
msgid "Integration with Google Consent Mode"
msgstr "Integrace s režimem souhlasu Google"

#: src/view/admin/uc_frame/settings/gcm-page.php:17
msgid ""
"The Usercentrics Cookiebot WordPress Plugin and Google Consent Mode "
"integrate seamlessly, providing plug-and-play privacy compliance and "
"effortless use of all Google services in one solution."
msgstr ""
"Plugin Usercentrics Cookiebot pro WordPress a režim souhlasu Google se "
"bezproblémově integrují a v jednom řešení zajišťují okamžitý soulad "
"s ochranou osobních údajů i snadné používání všech služeb Google."

#: src/view/admin/uc_frame/settings/gcm-page.php:31
msgid ""
"Enable Google Consent Mode integration within your Usercentrics Cookiebot "
"WordPress Plugin."
msgstr ""
"Povolte v pluginu Usercentrics Cookiebot pro WordPress integraci režimu "
"souhlasu Google."

#: src/view/admin/uc_frame/settings/general-page.php:22
msgid "Settings ID"
msgstr "ID účtu"

#: src/view/admin/uc_frame/settings/general-page.php:25
msgid ""
"To disconnect your account, enter your settings ID into the field and confirm "
"with the button."
msgstr ""
"Pokud chcete účet odpojit, zadejte do pole ID účtu a potvrďte ho tlačítkem."

#: src/view/admin/uc_frame/settings/general-page.php:99
msgid "TCF integration"
msgstr "Integrace řešení TCF"

#: src/view/admin/uc_frame/settings/general-page.php:102
msgid "Enable the integration with the latest version of IAB TCF."
msgstr "Povolte integraci s nejnovější verzí řešení TCF organizace IAB."

#: src/view/admin/uc_frame/settings/general-page.php:111
msgid "IAB TCF integration"
msgstr "Integrace řešení TCF organizace IAB"

#: src/view/admin/uc_frame/settings/gtm-page.php:14
msgid ""
"Enable Google Tag Manager integration to streamline tracking tags with your "
"Usercentrics Cookiebot WordPress Plugin."
msgstr ""
"Povolte integraci Správce značek Google – zjednodušíte tak používání značek "
"pro sledování s pluginem Usercentrics Cookiebot pro WordPress."

#: src/view/admin/uc_frame/settings/gtm-page.php:37
msgid "Enter your Google Tag Manager ID for seamless integration."
msgstr "Pro bezproblémovou integraci zadejte ID Správce značek Google."

#: src/view/admin/uc_frame/settings/gtm-page.php:48
msgid "GTM-XXXXXXX"
msgstr "GTM-XXXXXXX"

#: src/view/admin/uc_frame/settings/gtm-page.php:56
msgid "Data layer name (only if changed)"
msgstr "Název datové vrstvy (pouze pokud byl změněn)"

#: src/view/admin/uc_frame/settings/gtm-page.php:58
msgid ""
"The default name for the data layer in Google Tag Manager is ‘dataLayer’. If "
"you renamed it, enter the new name. Otherwise, leave this field empty."
msgstr ""
"Výchozí název datové vrstvy ve Správci značek Google je „dataLayer“. Pokud "
"jste ji přejmenovali, zadejte nový název. V opačném případě nechte toto pole "
"prázdné."

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:14
msgid "Title"
msgstr "Název"

#: src/view/admin/widgets/cookiebot-declaration-widget-form.php:33
msgid "- Default -"
msgstr "- Výchozí -"

#: src/widgets/Cookiebot_Declaration_Widget.php:17
msgid "Cookiebot - Cookie Declaration"
msgstr "Cookiebot™ - prohlášení o souborech cookie"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:23
msgid "Cookiebot Status"
msgstr "Stav pluginu Cookiebot™"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:39
msgid "You need to enter your Cookiebot ID."
msgstr "Je nutné zadat Cookiebot™ ID."

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:41
msgid "Update your Cookiebot ID"
msgstr "Aktualizujte si Cookiebot™ ID"

#: src/widgets/Dashboard_Widget_Cookiebot_Status.php:44
msgid "Your Cookiebot is working!"
msgstr "Nástroj Cookiebot™ funguje!"

#~ msgid ""
#~ "We hope you enjoy using WordPress Cookiebot! Would you consider leaving "
#~ "us a review on WordPress.org?"
#~ msgstr ""
#~ "Doufáme, že se vám s pluginem WordPress Cookiebot™ dobře pracuje! Mohli "
#~ "bychom vás poprosit o napsání recenze na webu WordPress.org?"

#~ msgid "Legislations"
#~ msgstr "Právní předpisy"

#~ msgid "Sure! I'd love to!"
#~ msgstr "Určitě Rád(a)"

#~ msgid "I've already left a review"
#~ msgstr "Recenzi jsem už napsal(a)"

#~ msgid "Maybe Later"
#~ msgstr "Možná později"

#~ msgid "Never show again"
#~ msgstr "Dál nezobrazovat"

#~ msgid "TCF version:"
#~ msgstr "TCF verze:"

#~ msgid ""
#~ "In May 2023 The Interactive Advertising Bureau (IAB) announced the latest "
#~ "version of its Transparency and Consent Framework (TCF), or TCF v2.2, "
#~ "which must be implemented by all consent management platforms (CMPs) by "
#~ "November 20, 2023. We will migrate you automatically on November 20,2023, "
#~ "but we recommend to do it manually before. To manually switch the version "
#~ "before please select it on the right."
#~ msgstr ""
#~ "V květnu 2023 The Interactive Advertising Bureau (IAB) oznámila "
#~ "nejnovější verzi svého rámce Transparency and Consent Framework (TCF) "
#~ "neboli TCF v2.2, kterou musí implementovat všechny platformy pro správu "
#~ "souhlasu (CMP) do 20. listopadu 2023. Migraci provedeme automaticky k 20. "
#~ "listopadu 2023, ale doporučujeme ji provést ručně ještě předtím. Chcete-"
#~ "li ručně přepnout verzi dříve, vyberte ji vpravo."

#~ msgid "Select the TCF Version below"
#~ msgstr "Vyberte verzi TCF níže"

#~ msgid "New"
#~ msgstr "Nová"

#~ msgid "Create a new Account"
#~ msgstr "Vytvořit nový účet"

#~ msgid "Get help with connecting your account"
#~ msgstr "Pomoc s připojením účtu"

#~ msgid "Select the Cookie-blocking mode"
#~ msgstr "Vyberte režim blokování souborů cookie"

#~ msgid "Automatic cookie-blocking mode"
#~ msgstr "Automatický režim blokování souborů cookie"

#~ msgid "Manual cookie-blocking mode"
#~ msgstr "Režim ručního blokování souborů cookie"

#~ msgid "Depending on Cookie-blocking mode"
#~ msgstr "V závislosti na režimu blokování souborů cookie"

#~ msgid "Auto-update Cookiebot™ Plugin:"
#~ msgstr "Automaticky aktualizovat plugin Cookiebot™:"

#~ msgid ""
#~ "Automatically update your Cookiebot™ plugin when new releases becomes "
#~ "available."
#~ msgstr ""
#~ "Automaticky aktualizuje plugin Cookiebot™, jakmile budou k dispozici nové "
#~ "verze."

#~ msgid ""
#~ "These add-ons are produced by an open-source community of developers. "
#~ "This is done to help make it easier for WordPress users to implement "
#~ "‘prior consent’ for cookies and trackers set by plugins that do not offer "
#~ "this as a built-in function. The add-ons are currently the best "
#~ "alternative to a WordPress Core framework that can signal the user’s "
#~ "consent state to other plugins (if and when this will be implemented is "
#~ "unknown) and to those plugins that do not yet offer native support for "
#~ "Cookiebot CMP. "
#~ msgstr ""
#~ "Tyto doplňky vyrábí komunita vývojářů s otevřeným zdrojovým kódem. Cílem "
#~ "je usnadnit uživatelům systému WordPress zavést „předchozí souhlas“ se "
#~ "soubory cookie a měřiči u pluginů, které tuto funkci nemají vestavěnou. "
#~ "Doplňky jsou v současné době nejlepší alternativou k rámci přímo v "
#~ "systému WordPress, který bude umět signalizovat stav uživatelova souhlasu "
#~ "jiným pluginům (jestli a kdy bude zaveden, není známo) a k těm pluginům, "
#~ "které dosud nenabízejí nativní podporu pro plugin Cookiebot CMP. "

#~ msgid "Do you not have an account yet?"
#~ msgstr "Ještě nemáte účet?"

#~ msgid ""
#~ "Before you can get started with Cookiebot CMP for WordPress, you need to "
#~ "create an account on our website by clicking on \"Create a new account\" "
#~ "below. After you have signed up, you can configure your banner in the "
#~ "Cookiebot Manager and then place the Cookiebot Domain Group ID in the "
#~ "designated field below. You can find your ID in the Cookiebot Manager by "
#~ "navigating to \"Settings\" and \"Your Scripts\"."
#~ msgstr ""
#~ "Než budete moci s pluginem Cookiebot CMP pro WordPress začít, je nutné "
#~ "zřídit si účet na našich webových stránkách kliknutím na níže uvedenou "
#~ "možnost Vytvořit nový účet. Po registraci můžete ve Správci pluginu "
#~ "Cookiebot™ nakonfigurovat banner a poté do určeného pole vložit ID "
#~ "skupiny domén Cookiebot™. Své ID naleznete ve Správci pluginu Cookiebot™ "
#~ "v části Nastavení a Skripty."

#~ msgid "Depending on cookie-blocking mode"
#~ msgstr "V závislosti na režimu blokování souborů cookie"

#~ msgid "Cookiebot CMP in WP Admin:"
#~ msgstr "Plugin Cookiebot CMP v oblasti pro správu WordPress:"

#~ msgid ""
#~ "This checkbox will disable Cookiebot CMP to act within the WordPress "
#~ "Admin area"
#~ msgstr ""
#~ "Toto zaškrtávací políčko zakáže pluginu Cookiebot CMP působit v oblasti "
#~ "pro správu WordPress"

#~ msgid "Disable Cookiebot CMP in the WordPress Admin area"
#~ msgstr "Zakázat Cookiebot CMP v oblasti pro správu WordPress"

#~ msgid "Cookiebot CMP on front-end while logged in:"
#~ msgstr "Cookiebot CMP ve frontendu po dobu přihlášení:"

#~ msgid ""
#~ "This setting will enable Cookiebot CMP on the front-end while you're "
#~ "logged in."
#~ msgstr ""
#~ "Toto nastavení povolí plugin Cookiebot CMP ve frontendu po dobu vašeho "
#~ "přihlášení."

#~ msgid "Render Cookiebot CMP on front-end while logged in"
#~ msgstr "Vykreslovat Cookiebot CMP ve frontendu po dobu přihlášení"

#~ msgid "Watch video demonstration"
#~ msgstr "Podívejte se na videoukázku"

#~ msgid ""
#~ "Cookiebot is a cloud-driven solution that automatically controls cookies "
#~ "and trackers, enabling full GDPR/ePrivacy and CCPA compliance for "
#~ "websites."
#~ msgstr ""
#~ "Platforma pro správu souhlasu Cookiebot CMP je cloudové řešení, které "
#~ "automaticky řídí soubory cookie a sledovače a umožňuje tak úplné "
#~ "dodržování obecného nařízení o ochraně osobních údajů (GDPR), směrnice o "
#~ "soukromí a elektronických komunikacích a zákona o ochraně soukromí "
#~ "spotřebitelů v Kalifornii (CCPA) pro webové stránky."
