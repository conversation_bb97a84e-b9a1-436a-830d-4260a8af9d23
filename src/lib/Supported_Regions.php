<?php

namespace cybot\cookiebot\lib;

class Supported_Regions {

	public static function get() {
		$regions = array(
			'AF'    => __( 'Afghanistan', 'cookiebot' ),
			'AL'    => __( 'Albania', 'cookiebot' ),
			'DZ'    => __( 'Algeria', 'cookiebot' ),
			'AS'    => __( 'American Samoa', 'cookiebot' ),
			'AD'    => __( 'Andorra', 'cookiebot' ),
			'AO'    => __( 'Angola', 'cookiebot' ),
			'AI'    => __( 'Anguilla', 'cookiebot' ),
			'AQ'    => __( 'Antarctica', 'cookiebot' ),
			'AG'    => __( 'Antigua and Barbuda', 'cookiebot' ),
			'AR'    => __( 'Argentina', 'cookiebot' ),
			'AM'    => __( 'Armenia', 'cookiebot' ),
			'AW'    => __( 'Aruba', 'cookiebot' ),
			'AU'    => __( 'Australia', 'cookiebot' ),
			'AT'    => __( 'Austria', 'cookiebot' ),
			'AZ'    => __( 'Azerbaijan', 'cookiebot' ),
			'BS'    => __( 'Bahamas', 'cookiebot' ),
			'BH'    => __( 'Bahrain', 'cookiebot' ),
			'BD'    => __( 'Bangladesh', 'cookiebot' ),
			'BB'    => __( 'Barbados', 'cookiebot' ),
			'BY'    => __( 'Belarus', 'cookiebot' ),
			'BE'    => __( 'Belgium', 'cookiebot' ),
			'BZ'    => __( 'Belize', 'cookiebot' ),
			'BJ'    => __( 'Benin', 'cookiebot' ),
			'BM'    => __( 'Bermuda', 'cookiebot' ),
			'BT'    => __( 'Bhutan', 'cookiebot' ),
			'BO'    => __( 'Bolivia', 'cookiebot' ),
			'BQ'    => __( 'Bonaire, Sint Eustatius and Saba', 'cookiebot' ),
			'BA'    => __( 'Bosnia and Herzegovina', 'cookiebot' ),
			'BW'    => __( 'Botswana', 'cookiebot' ),
			'BV'    => __( 'Bouvet Island', 'cookiebot' ),
			'BR'    => __( 'Brazil', 'cookiebot' ),
			'IO'    => __( 'British Indian Ocean Territory', 'cookiebot' ),
			'BN'    => __( 'Brunei ', 'cookiebot' ),
			'BG'    => __( 'Bulgaria', 'cookiebot' ),
			'BF'    => __( 'Burkina Faso', 'cookiebot' ),
			'BI'    => __( 'Burundi', 'cookiebot' ),
			'KH'    => __( 'Cambodia', 'cookiebot' ),
			'CM'    => __( 'Cameroon', 'cookiebot' ),
			'CA'    => __( 'Canada', 'cookiebot' ),
			'CV'    => __( 'Cape Verde', 'cookiebot' ),
			'KY'    => __( 'Cayman Islands', 'cookiebot' ),
			'CF'    => __( 'Central African Republic', 'cookiebot' ),
			'TD'    => __( 'Chad', 'cookiebot' ),
			'CL'    => __( 'Chile', 'cookiebot' ),
			'CN'    => __( 'China', 'cookiebot' ),
			'CX'    => __( 'Christmas Island', 'cookiebot' ),
			'CC'    => __( 'Cocos (Keeling) Islands', 'cookiebot' ),
			'CO'    => __( 'Colombia', 'cookiebot' ),
			'KM'    => __( 'Comoros', 'cookiebot' ),
			'CG'    => __( 'Congo', 'cookiebot' ),
			'CD'    => __( 'Congo, the Democratic Republic of the', 'cookiebot' ),
			'CK'    => __( 'Cook Islands', 'cookiebot' ),
			'CR'    => __( 'Costa Rica', 'cookiebot' ),
			'HR'    => __( 'Croatia', 'cookiebot' ),
			'CU'    => __( 'Cuba', 'cookiebot' ),
			'CW'    => __( 'Curaçao', 'cookiebot' ),
			'CY'    => __( 'Cyprus', 'cookiebot' ),
			'CZ'    => __( 'Czech Republic', 'cookiebot' ),
			'CI'    => __( 'Côte d\'Ivoire', 'cookiebot' ),
			'DK'    => __( 'Denmark', 'cookiebot' ),
			'DJ'    => __( 'Djibouti', 'cookiebot' ),
			'DM'    => __( 'Dominica', 'cookiebot' ),
			'DO'    => __( 'Dominican Republic', 'cookiebot' ),
			'EC'    => __( 'Ecuador', 'cookiebot' ),
			'EG'    => __( 'Egypt', 'cookiebot' ),
			'SV'    => __( 'El Salvador', 'cookiebot' ),
			'GQ'    => __( 'Equatorial Guinea', 'cookiebot' ),
			'ER'    => __( 'Eritrea', 'cookiebot' ),
			'EE'    => __( 'Estonia', 'cookiebot' ),
			'ET'    => __( 'Ethiopia', 'cookiebot' ),
			'FK'    => __( 'Falkland Islands (Malvinas)', 'cookiebot' ),
			'FO'    => __( 'Faroe Islands', 'cookiebot' ),
			'FJ'    => __( 'Fiji', 'cookiebot' ),
			'FI'    => __( 'Finland', 'cookiebot' ),
			'FR'    => __( 'France', 'cookiebot' ),
			'GF'    => __( 'French Guiana', 'cookiebot' ),
			'PF'    => __( 'French Polynesia', 'cookiebot' ),
			'TF'    => __( 'French Southern and Antarctic Lands', 'cookiebot' ),
			'GA'    => __( 'Gabon', 'cookiebot' ),
			'GM'    => __( 'Gambia', 'cookiebot' ),
			'GE'    => __( 'Georgia', 'cookiebot' ),
			'DE'    => __( 'Germany', 'cookiebot' ),
			'GH'    => __( 'Ghana', 'cookiebot' ),
			'GI'    => __( 'Gibraltar', 'cookiebot' ),
			'GR'    => __( 'Greece', 'cookiebot' ),
			'GL'    => __( 'Greenland', 'cookiebot' ),
			'GD'    => __( 'Grenada', 'cookiebot' ),
			'GP'    => __( 'Guadeloupe', 'cookiebot' ),
			'GU'    => __( 'Guam', 'cookiebot' ),
			'GT'    => __( 'Guatemala', 'cookiebot' ),
			'GG'    => __( 'Guernsey', 'cookiebot' ),
			'GN'    => __( 'Guinea', 'cookiebot' ),
			'GW'    => __( 'Guinea-Bissau', 'cookiebot' ),
			'GY'    => __( 'Guyana', 'cookiebot' ),
			'HT'    => __( 'Haiti', 'cookiebot' ),
			'HM'    => __( 'Heard Island and McDonald Islands', 'cookiebot' ),
			'VA'    => __( 'Holy See (Vatican City State)', 'cookiebot' ),
			'HN'    => __( 'Honduras', 'cookiebot' ),
			'HK'    => __( 'Hong Kong', 'cookiebot' ),
			'HU'    => __( 'Hungary', 'cookiebot' ),
			'IS'    => __( 'Iceland', 'cookiebot' ),
			'IN'    => __( 'India', 'cookiebot' ),
			'ID'    => __( 'Indonesia', 'cookiebot' ),
			'IR'    => __( 'Iran', 'cookiebot' ),
			'IQ'    => __( 'Iraq', 'cookiebot' ),
			'IE'    => __( 'Ireland', 'cookiebot' ),
			'IM'    => __( 'Isle of Man', 'cookiebot' ),
			'IL'    => __( 'Israel', 'cookiebot' ),
			'IT'    => __( 'Italy', 'cookiebot' ),
			'JM'    => __( 'Jamaica', 'cookiebot' ),
			'JP'    => __( 'Japan', 'cookiebot' ),
			'JE'    => __( 'Jersey', 'cookiebot' ),
			'JO'    => __( 'Jordan', 'cookiebot' ),
			'KZ'    => __( 'Kazakhstan', 'cookiebot' ),
			'KE'    => __( 'Kenya', 'cookiebot' ),
			'KI'    => __( 'Kiribati', 'cookiebot' ),
			'KW'    => __( 'Kuwait', 'cookiebot' ),
			'KG'    => __( 'Kyrgyzstan', 'cookiebot' ),
			'LA'    => __( 'Laos', 'cookiebot' ),
			'LV'    => __( 'Latvia', 'cookiebot' ),
			'LB'    => __( 'Lebanon', 'cookiebot' ),
			'LS'    => __( 'Lesotho', 'cookiebot' ),
			'LR'    => __( 'Liberia', 'cookiebot' ),
			'LY'    => __( 'Libya', 'cookiebot' ),
			'LI'    => __( 'Liechtenstein', 'cookiebot' ),
			'LT'    => __( 'Lithuania', 'cookiebot' ),
			'LU'    => __( 'Luxembourg', 'cookiebot' ),
			'MO'    => __( 'Macao', 'cookiebot' ),
			'MK'    => __( 'North Macedonia', 'cookiebot' ),
			'MG'    => __( 'Madagascar', 'cookiebot' ),
			'MW'    => __( 'Malawi', 'cookiebot' ),
			'MY'    => __( 'Malaysia', 'cookiebot' ),
			'MV'    => __( 'Maldives', 'cookiebot' ),
			'ML'    => __( 'Mali', 'cookiebot' ),
			'MT'    => __( 'Malta', 'cookiebot' ),
			'MH'    => __( 'Marshall Islands', 'cookiebot' ),
			'MQ'    => __( 'Martinique', 'cookiebot' ),
			'MR'    => __( 'Mauritania', 'cookiebot' ),
			'MU'    => __( 'Mauritius', 'cookiebot' ),
			'YT'    => __( 'Mayotte', 'cookiebot' ),
			'MX'    => __( 'Mexico', 'cookiebot' ),
			'FM'    => __( 'Micronesia, Federated States of', 'cookiebot' ),
			'MD'    => __( 'Moldova', 'cookiebot' ),
			'MC'    => __( 'Monaco', 'cookiebot' ),
			'MN'    => __( 'Mongolia', 'cookiebot' ),
			'ME'    => __( 'Montenegro', 'cookiebot' ),
			'MS'    => __( 'Montserrat', 'cookiebot' ),
			'MA'    => __( 'Morocco', 'cookiebot' ),
			'MZ'    => __( 'Mozambique', 'cookiebot' ),
			'MM'    => __( 'Myanmar', 'cookiebot' ),
			'NA'    => __( 'Namibia', 'cookiebot' ),
			'NR'    => __( 'Nauru', 'cookiebot' ),
			'NP'    => __( 'Nepal', 'cookiebot' ),
			'NL'    => __( 'Netherlands', 'cookiebot' ),
			'NC'    => __( 'New Caledonia', 'cookiebot' ),
			'NZ'    => __( 'New Zealand', 'cookiebot' ),
			'NI'    => __( 'Nicaragua', 'cookiebot' ),
			'NE'    => __( 'Niger', 'cookiebot' ),
			'NG'    => __( 'Nigeria', 'cookiebot' ),
			'NU'    => __( 'Niue', 'cookiebot' ),
			'NF'    => __( 'Norfolk Island', 'cookiebot' ),
			'KR'    => __( 'North Korea', 'cookiebot' ),
			'MP'    => __( 'Northern Mariana Islands', 'cookiebot' ),
			'NO'    => __( 'Norway', 'cookiebot' ),
			'OM'    => __( 'Oman', 'cookiebot' ),
			'PK'    => __( 'Pakistan', 'cookiebot' ),
			'PW'    => __( 'Palau', 'cookiebot' ),
			'PS'    => __( 'Palestinian Territory', 'cookiebot' ),
			'PA'    => __( 'Panama', 'cookiebot' ),
			'PG'    => __( 'Papua New Guinea', 'cookiebot' ),
			'PY'    => __( 'Paraguay', 'cookiebot' ),
			'PE'    => __( 'Peru', 'cookiebot' ),
			'PH'    => __( 'Philippines', 'cookiebot' ),
			'PN'    => __( 'Pitcairn', 'cookiebot' ),
			'PL'    => __( 'Poland', 'cookiebot' ),
			'PT'    => __( 'Portugal', 'cookiebot' ),
			'PR'    => __( 'Puerto Rico', 'cookiebot' ),
			'QA'    => __( 'Qatar', 'cookiebot' ),
			'RO'    => __( 'Romania', 'cookiebot' ),
			'RU'    => __( 'Russia', 'cookiebot' ),
			'RW'    => __( 'Rwanda', 'cookiebot' ),
			'RE'    => __( 'Réunion', 'cookiebot' ),
			'BL'    => __( 'Saint Barthélemy', 'cookiebot' ),
			'SH'    => __( 'Saint Helena, Ascension and Tristan da Cunha', 'cookiebot' ),
			'KN'    => __( 'Saint Kitts and Nevis', 'cookiebot' ),
			'LC'    => __( 'Saint Lucia', 'cookiebot' ),
			'MF'    => __( 'Saint Martin (French part)', 'cookiebot' ),
			'PM'    => __( 'Saint Pierre and Miquelon', 'cookiebot' ),
			'VC'    => __( 'Saint Vincent and the Grenadines', 'cookiebot' ),
			'WS'    => __( 'Samoa', 'cookiebot' ),
			'SM'    => __( 'San Marino', 'cookiebot' ),
			'ST'    => __( 'Sao Tome and Principe', 'cookiebot' ),
			'SA'    => __( 'Saudi Arabia', 'cookiebot' ),
			'SN'    => __( 'Senegal', 'cookiebot' ),
			'RS'    => __( 'Serbia', 'cookiebot' ),
			'SC'    => __( 'Seychelles', 'cookiebot' ),
			'SL'    => __( 'Sierra Leone', 'cookiebot' ),
			'SG'    => __( 'Singapore', 'cookiebot' ),
			'SX'    => __( 'Sint Maarten (Dutch part)', 'cookiebot' ),
			'SK'    => __( 'Slovakia', 'cookiebot' ),
			'SI'    => __( 'Slovenia', 'cookiebot' ),
			'SB'    => __( 'Solomon Islands', 'cookiebot' ),
			'SO'    => __( 'Somalia', 'cookiebot' ),
			'ZA'    => __( 'South Africa', 'cookiebot' ),
			'GS'    => __( 'South Georgia and the South Sandwich Islands', 'cookiebot' ),
			'KP'    => __( 'South Korea', 'cookiebot' ),
			'SS'    => __( 'South Sudan', 'cookiebot' ),
			'ES'    => __( 'Spain', 'cookiebot' ),
			'LK'    => __( 'Sri Lanka', 'cookiebot' ),
			'SD'    => __( 'Sudan', 'cookiebot' ),
			'SR'    => __( 'Suriname', 'cookiebot' ),
			'SJ'    => __( 'Svalbard and Jan Mayen', 'cookiebot' ),
			'SZ'    => __( 'Eswatini', 'cookiebot' ),
			'SE'    => __( 'Sweden', 'cookiebot' ),
			'CH'    => __( 'Switzerland', 'cookiebot' ),
			'SY'    => __( 'Syria', 'cookiebot' ),
			'TW'    => __( 'Taiwan', 'cookiebot' ),
			'TJ'    => __( 'Tajikistan', 'cookiebot' ),
			'TZ'    => __( 'Tanzania', 'cookiebot' ),
			'TH'    => __( 'Thailand', 'cookiebot' ),
			'TL'    => __( 'Timor-Leste', 'cookiebot' ),
			'TG'    => __( 'Togo', 'cookiebot' ),
			'TK'    => __( 'Tokelau', 'cookiebot' ),
			'TO'    => __( 'Tonga', 'cookiebot' ),
			'TT'    => __( 'Trinidad and Tobago', 'cookiebot' ),
			'TN'    => __( 'Tunisia', 'cookiebot' ),
			'TR'    => __( 'Türkiye', 'cookiebot' ),
			'TM'    => __( 'Turkmenistan', 'cookiebot' ),
			'TC'    => __( 'Turks and Caicos Islands', 'cookiebot' ),
			'TV'    => __( 'Tuvalu', 'cookiebot' ),
			'UG'    => __( 'Uganda', 'cookiebot' ),
			'UA'    => __( 'Ukraine', 'cookiebot' ),
			'AE'    => __( 'United Arab Emirates', 'cookiebot' ),
			'GB'    => __( 'United Kingdom', 'cookiebot' ),
			'US'    => __( 'United States', 'cookiebot' ),
			'US-06' => __( 'United States - State of California', 'cookiebot' ),
			'US-08' => __( 'United States - State of Colorado', 'cookiebot' ),
			'US-09' => __( 'United States - State of Connecticut', 'cookiebot' ),
			'US-49' => __( 'United States - State of Utah', 'cookiebot' ),
			'US-51' => __( 'United States - State of Virginia', 'cookiebot' ),
			'UM'    => __( 'United States Minor Outlying Islands', 'cookiebot' ),
			'UY'    => __( 'Uruguay', 'cookiebot' ),
			'UZ'    => __( 'Uzbekistan', 'cookiebot' ),
			'VU'    => __( 'Vanuatu', 'cookiebot' ),
			'VE'    => __( 'Venezuela', 'cookiebot' ),
			'VN'    => __( 'Viet Nam', 'cookiebot' ),
			'VG'    => __( 'Virgin Islands, British', 'cookiebot' ),
			'VI'    => __( 'Virgin Islands, U.S.', 'cookiebot' ),
			'WF'    => __( 'Wallis and Futuna', 'cookiebot' ),
			'EH'    => __( 'Western Sahara', 'cookiebot' ),
			'YE'    => __( 'Yemen', 'cookiebot' ),
			'ZM'    => __( 'Zambia', 'cookiebot' ),
			'ZW'    => __( 'Zimbabwe', 'cookiebot' ),
			'AX'    => __( 'Åland Islands', 'cookiebot' ),
		);

		asort( $regions );

		return $regions;
	}

	const OPTOUT_REGIONS = array(
		'US-06',
		'US-08',
		'US-09',
		'US-49',
		'US-51',
		'US',
		'AU',
		'CA',
	);
}
