<div class="cb-addons__tab__header">
	<div class="cb-addons__header__column--inner">
		<div class="cb-addons__header__column">
			<h2 class="cb-addons__tab__title"><?php esc_html_e( 'Information', 'cookiebot' ); ?></h2>
			<p class="cb-addons__tab__text"><?php esc_html_e( 'These add-ons are created by a dedicated open-source community to make it easier for you to manage cookie and tracker consent on your WordPress site. They’re designed to help you ensure ‘prior consent’ even for plugins that don’t include this feature.', 'cookiebot' ); ?></p>
			<p class="cb-addons__tab__text"><?php esc_html_e( 'Right now, these add-ons are the best way for you to signal user consent to other plugins. While we don’t know if or when WordPress Core will add this functionality, these tools are here to support you and work seamlessly with Usercentrics solution.', 'cookiebot' ); ?></p>
		</div>
	</div>
</div>
