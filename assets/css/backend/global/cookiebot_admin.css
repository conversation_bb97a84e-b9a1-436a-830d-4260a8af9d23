.cookiebot-popup-container {
    position: fixed;
    display: none;
    width: 100vw;
    height: 100vh;
    top: 0;
    right: 0;
    background: rgba(0,0,0,.5);
    z-index: 9999;
}

.cookiebot-popup-container.cb-opened {
    display: grid;
    justify-content: center;
    align-items: center;
}

#cookiebot-popup {
    box-sizing: border-box;
    background: #ffffff;
    width: 565px;
    padding: 20px;
    border-radius: 25px;
}

#cookiebot-popup *{
    box-sizing: border-box;
}

#cookiebot-popup label span {
    font-size: 15px;
}

#cookiebot-popup .cb-review__logo img {
    width: 100%;
    height: auto;
}

.cb-review__header {
    display: grid;
    grid-template-columns: 50px 1fr 22px;
    grid-column-gap: 5px;
}

#cb-review__close {
    color: #424242;
    width: 20px;
    height: 20px;
    text-align: center;
    border-radius: 20px;
    font-weight: 700;
    margin-right: 0;
    margin-left: auto;
    cursor: pointer;
}

#cb-review__form p {
    font-weight: normal;
    font-size: 16px;
    line-height: 24px;
    color: #141414;
}

#cb-review__form > div {
    padding: 0 20px;
}

.cb-review__form--item {
    display: inline-block;
    font-size: 15px;
    margin-bottom: 15px;
}

#cb-review__other-description {
    width: 100%;
}

#cb-review__form #cb-review__alert {
    display: none;
    padding: 15px 15px;
    border-left: 5px solid #ffcc5f;
    background: #f2f2f2;
    margin: 15px 0;
    color: #d69c20;
    font-weight: bold;
}

#cb-review__form #cb-review__alert.show-alert {
    display: block;
}

#cb-review__form .cb-review__actions {
    display: grid;
    padding: 15px;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 25px;
}

#cb-review__skip {
    background-color: rgba(0, 0, 0, 0.16);
    color: #333;
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    border-radius: 7px;
    text-decoration: none;
    text-align: center;
}

#cb-review__submit {
    padding: 10px 20px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 7px;
    border: none;
    cursor: pointer;
    background-color: #1032cf;
    color: #ffffff;
    text-align: center;
}

#cb-review__submit.disabled {
    background-color: lightsteelblue;
}

#cb-review__form .cb-review__policy {
    font-size: 14px;
    text-align: right;
    margin: 5px 0;
    padding-right: 20px;
}

#cb-review__form .cb-review__policy a {
    color: blue;
    text-decoration: none;
}

.cb-review__form--item__custom {
    margin-bottom: 20px;
}


.consent-item {
    display: none;
}

.consent-item.show-consent {
    display: block;
}

.consent-item .cb-review__form--item {
    font-size: 13px;
    margin-bottom: 0;
}

.consent-item .cb-review__form--item span b{
    color: #1032cf;
}

.consent-item .cb-review__form--item span a{
    color: #1032cf;
    text-decoration: none;
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    background: white;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-content {
    text-align: center;
    padding: 0 20px;
    max-width: 100%;
    box-sizing: border-box;
}

.loading-spinner {
    width: 48px;
    height: 48px;
    margin: 0 auto 32px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #0047FF;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.loading-content h2 {
    font-size: 24px;
    color: #111827;
    margin: 0 0 16px 0;
    font-weight: 500;
}

.loading-content p {
    font-size: 16px;
    color: #6B7280;
    margin: 0;
    line-height: 1.5;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

body.has-loading-overlay {
    overflow: hidden;
}
