.cookiebot_fieldset_header {
    cursor: pointer;
}

.cookiebot_fieldset_header::after {
    content: "\f140";
    font: normal 24px/1 dashicons;
    position: relative;
    top: 5px;
}

.cookiebot_fieldset_header.active::after {
    content: "\f142";
}

.cb-settings__header {
    display: grid;
    grid-template-columns: 3fr 1fr;
    align-items: start;
}

.cb-settings__header .cb-main__page_title {
    margin: 0;
}

.cb-settings__header p.submit {
    text-align: right;
    margin: 0;
    padding: 0;
}

.cb-settings__header p.submit #submit {
    background-color: rgba(0, 0, 0, 0.16);
    color: #666666;
    padding: 10px 20px;
    font-size: 16px;
    border: none;
    pointer-events: none;
}

.cb-settings__header p.submit #submit.enabled{
    background-color: #076646;
    color: #ffffff;
    pointer-events: initial;
}

.cb-settings__notabs {
    margin-bottom: 60px;
}

.cb-settings__tabs {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    margin: 25px 0 50px;
    border-bottom: 1px solid #e1e1e1;
}

.cb-settings__tabs.cb-settings__tabs--uc {
    grid-template-columns: repeat(5, 1fr);
}

.cb-settings__tabs__item {
    display: grid;
    padding: 15px 10px;
    align-items: center;
    border-bottom: 3px solid transparent;
    color: #141414;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    cursor: pointer;
}

.cb-settings__tabs__item.active-item, .cb-settings__tabs__item:hover {
    border-color: #1032cf;
}

.cb-settings__tabs__content--item {
    display: none;
}

.cb-settings__tabs__content--item.active-item {
    display: block;
}

.cb-general__new__account, .cb-general__consent__mode, .cb-general__prior__consent {
    background-color: #f1f1f1;
    border-radius: 15px;
    padding: 40px;
    margin-bottom: 50px;
}

.cb-general__new__account {
    margin-top: 50px;
}

.cb-general__info__title {
    font-size: 27px;
    font-weight: 700;
    line-height: 30px;
    margin: 0;
}

.cb-general__info__text {
    width: 100%;
    max-width: 620px;
    font-size: 16px;
    line-height: 24px;
}

.new-account-actions .cb-btn:first-of-type {
    margin-right: 20px;
}

.cb-settings__config__item {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 24px;
    margin-bottom: 50px;
}

.cb-settings__config__content {
    padding-right: 40px;
}

.cb-settings__config__subtitle {
    font-size: 20px;
    line-height: 25px;
    margin: 0;
}

.cb-settings__config__data__inner {
    background-color: #f1f1f1;
    border-radius: 15px;
    padding: 30px;
}

.cb-settings__data__subtitle {
    font-size: 15px;
    line-height: 20px;
    margin: 0 0 15px;
}

.cookiebot-cbid-container {
    display: grid;
    grid-template-columns: 1fr auto;
}

.cookiebot-cbid-input {
    position: relative;
}

.cookiebot-cbid-container p.submit {
    margin: 0;
    padding: 0;
}

.cookiebot-cbid-container p.submit #submit {
    background-color: #1032cf;
    color: #ffffff;
    margin-top: 0;
    font-size: 15px;
    line-height: 2;
    padding: 8px 15px;
    font-weight: 500;
    border-radius: 7px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
}

.cookiebot-cbid-container p.submit #submit.disabled {
    background: gray;
    pointer-events: none;
}

.cookiebot-cbid-input #cookiebot-cbid
{
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cookiebot-cbid-input #cookiebot-cbid.cbid-active
{
    pointer-events: none;
    background: rgba(255, 255, 255, .5);
    color: rgba(44, 51, 56, .5);
}

.cookiebot-cbid-input #cookiebot-cbid.check-progress
{
    padding-right: 55px;
}

.cookiebot-cbid-check {
    position: absolute;
    top: 50%;
    right: 10px;
    width: 35px;
    height: 35px;
    transform: translateY(-50%);
}

.cookiebot-cbid-check.check-progress {
    background-image: url(../../img/icons/loader.svg);

}

.cookiebot-cbid-check.check-pass {
    background-color: #076646;
    background-image: url(../../img/icons/check.svg);
    border-radius: 50%;
    background-size: auto;
    background-position: center;
    background-repeat: no-repeat;
}

#cookiebot-cbid-reset-dialog {
    margin-top: 0;
    line-height: 2;
    padding: 8px 15px;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    cursor: pointer;
}

#cookiebot-cbid-reset-dialog.disabled, #cookiebot-cbid-network-dialog {
    background-color: #e1e1e1;
    color: #666;
    margin-top: 0;
    pointer-events: none;
}

#cb-network-id-override {
    margin-top: 24px;
}

#cookiebot-ruleset-id-selector.hidden {
    display: none;
}

.cb-settings__config__head-section {
    margin-bottom: 50px;
}

#embedding-shortcode {
    display: inline-block;
    width: 100%;
    border: 2px solid #d3d3d3;
    border-radius: 7px;
    padding: 16px;
    max-width: initial;
    box-shadow: none;
    background: #ffffff;
    color: #242424;
    font-weight: 600;
    pointer-events: none;
}

input#cookiebot-embedding {
    width: 100%;
    border: 2px solid #d3d3d3;
    border-radius: 7px;
    padding: 8px 15px;
    max-width: initial;
}

select#cookiebot-embedding, select#cookiebot-embedding-type {
    width: 100%;
    border: 2px solid #d3d3d3;
    border-radius: 7px;
    padding: 8px 15px;
    max-width: initial;
}

#cookiebot-embedding-type option.hide-option,
#cookiebot-embedding-single-service-container.hide-container,
#cookiebot-tcf-toggle-container.hide-container
{
    display:none;
}

#show_add_language_guide {
    display: block;
}

.cb-settings__config__data__inner label {
    display: block;
    font-size: 16px;
    line-height: 20px;
    font-weight: 500;
    color: #141414;
}

.cb-settings__config__data__inner label:not(.switch-checkbox) {
    padding-left: 30px;
}

.cb-settings__config__data__inner label.cb-settings__cookie-types {
    display: inline-block;
    padding: 0;
    text-transform: capitalize;
    line-height: 32px;
}

.cb-settings__config__data__inner label.switch-checkbox {
    display: flex;
}

.cb-settings__config__data__inner label:not(:first-of-type) {
    margin-top: 20px;
}

.recommended-tag {
    font-size: 13px;
    background-color: #0e1848;
    color: #ffffff;
    padding: 3px 7px;
    border-radius: 5px;
}

.cb-settings__config__item.secondary__item {
    border-left: 4px solid #1032cf;
}

.cb-settings__config__item.secondary__item .cb-settings__config__content {
    padding-left: 90px;
}

.cb-general__info__note {
    font-size: 13px;
    font-style: oblique;
}

.disabled__item {
    display: none;
    filter: grayscale(1);
    opacity: .5;
    pointer-events: none;
}

.cb-prior__tab__selector {
    position: relative;
    display: grid;
    grid-template-columns: 1fr 1fr;
    align-items: center;
    justify-items: center;
    width: 100%;
    max-width: 360px;
    margin: 0 auto;
    border: 2px solid #e3e3e3;
    border-radius: 150px;
}

.cb-prior__tab__selector:before {
    position: absolute;
    content: '';
    width: calc(50% - 6px);
    height: calc(100% - 6px);
    background: #1032cf;
    border-radius: 150px;
    left: 3px;
    z-index: 0;
}

.cb-prior__tab__item {
    width: 100%;
    text-align: center;
    padding: 15px;
    font-size: 15px;
    font-weight: 500;
    z-index: 1;
}

.cb-prior__tab__item.active-item {
    color: #ffffff;
}

.cb-prior__tab__content__item {
    display: none;
}

.cb-prior__tab__content__item.active-item {
    display: block;
}

.cb-settings__vendor__config__item {
    margin-bottom: 50px;
}

.cb-settings__vendor__config__item.vendor-list-offline {
    background-color: #f1f1f1;
    padding: 24px;
    border-radius: 16px;
    border: 2px solid #de8e09;
}

.cb-settings__vendor__config__item.vendor-list-offline .cb-general__info__text {
    margin-bottom: 0;
}

.cb-settings__vendor__config__item .cb-general__info__text {
    max-width: initial;
}
.cb-settings__vendor__config__item .only-title {
    margin-bottom: 24px;
}


.cb-vendor__setting--item {
    position: relative;
    width: 100%;
    background-color: #ffffff;
    color: #141414;
    border: 2px solid #d3d3d3;
    border-radius: 7px;
    padding: 8px 15px;
    box-sizing: border-box;
    font-size: 15px;
    font-weight: 600;
    line-height: 27px;
    min-height: 30px;
    cursor: pointer;
}

.cb-settings__config__data__inner.cb-vendor-settings {
    display: flex;
    flex-wrap: wrap;
    column-gap: 16px;
}

.cb-vendor-settings .checkbox-vendor-search {
    width: auto;
    flex: 6 1 auto;
}

.cb-vendor-settings .cb-btn.cb-settings__selector-all {
    margin: 0;
    flex: 0 1 auto;
}

.cb-vendor-settings .cb-btn.cb-settings__selector-none {
    margin: 0;
    flex: 0 1 auto;
}

.vendor-selected-items-message {
    flex: 1 1 100%;
    margin-top: 20px;
}

.vendor-selected-items-message span {
    display: inline-block;
    border-radius: 6px;
    font-weight: 700;
    color: #d63638;
}

.cb-vendor-alert__msg, .cb-cbid-alert__msg, .cb-cbid-subsite-alert__msg {
    box-sizing: border-box;
    padding: 24px 34px;
    font-size: 15px;
    line-height: 24px;
    font-weight: 600;
    border-radius: 8px;
    margin-bottom: 20px;
}

.cb-vendor-alert__msg {
    background-color: #de8e09;
    color: #ffffff;
}

.cb-cbid-alert__msg, .cb-cbid-subsite-alert__msg {
    background-color: #fdedd3;
    border: 4px solid #de8e09;
}

.vendor-selected-items {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-row-gap: 5px;
    grid-column-gap: 5px;
    grid-auto-rows: minmax(30px, 1fr);
    max-height: 400px;
    overflow-y: auto;
    margin-top: 32px;
}

.vendor-selected-items label.switch-checkbox {
    margin-top: 0;
    align-items: flex-start;
}

.vendor-selected-items label.switch-checkbox.hidden {
    display: none;
}

.vendor-selected-items .switch-checkbox .switcher {
    width: 30px;
    min-width: 30px;
    height: 18px;
}

.vendor-selected-items .switch-checkbox .switcher:after {
    top: 2px;
    left: 2px;
    width: 14px;
    height: 14px;
}

.vendor-selected-items .switch-checkbox .switcher:active:after {
    width: 14px;
}

.vendor-selected-items .switch-checkbox input[type=checkbox]:checked + .switcher:after {
    left: calc(100% - 2px);
    transform: translateX(-100%);
}

.vendor-selected-item {
    display: inline-block;
    padding: 1px 5px;
    font-size: 13px;
    line-height: initial;
    background: blue;
    color: #ffffff;
    border-radius: 15px;
    margin-right: 4px;
}

.vendor-name-list {
    max-height: 400px;
    overflow-y: scroll;
}

.cb-settings__vendor__restrictions {
    margin-top: 24px;
}

.cb-settings__vendor__restrictions > .cb-settings__config__data__inner {
    position: relative;
    display: grid;
    grid-template-columns: 1fr auto auto;
    grid-column-gap: 16px;
    align-items: center;
}

.cb-settings__vendor__restrictions .cb-main-btn {
    margin-top: 0;
}

.vendor-purposes-restrictions > .cb-settings__config__data__inner {
    margin-top: 16px;
}

.cb-settings__vendor__restrictions .remove__restriction {
    cursor: pointer;
}

@media (max-width: 1414px) {
    .recommended-tag {
        display: block;
        width: -webkit-fit-content;
        width: fit-content;
        margin-top: 5px;
        margin-bottom: 10px;
    }
}

.cb-general__new__account--double {
    display: grid;
    grid-template-columns: 1fr 1fr;
    grid-column-gap: 24px;
    margin-top: -26px;
    margin-bottom: 50px;
}

.cb-general__new__account--double .cb-main__card__inner.new_card {
    display: grid;
    align-self: stretch;
    position: relative;
    padding: 25px;
    overflow: hidden;
    background-color: #00C6FB;
    color: #141414;
    border-radius: 10px;
}

.cb-main__card__inner.new_card .cb-main__card__content {
    display: grid;
    grid-template-columns: 1fr minmax(86px, auto);
    grid-template-rows: minmax(120px, auto) auto;
    grid-column-gap: 25px;
    align-items: end;
}

.cb-main__card__inner.new_card .cb-main__card__title  {
    font-size: 28px;
    line-height: 30px;
    margin-bottom: 25px;
}

.cb-main__card__inner.new_card .cb-btn.cb-main-btn  {
    background-color: #000000;
    color: #ffffff;
    grid-row: 2;
    grid-column: span 2;
    text-align: center;
    text-transform: uppercase;
    margin-top: 0;
}

.cb-main__card__inner.new_card .cb-bf-counter {
    background-color: #000000;
    border-bottom-left-radius: 500px;
    border-bottom-right-radius: 500px;
    text-align: center;
    padding: 6px 6px 12px;
    width: 74px;
    position: relative;
    top: -25px;
    align-self: start;
}

.cb-main__card__inner.new_card .cb-bf-counter-label {
    color: #ffffff;
    font-weight: 700;
}

.cb-main__card__inner.new_card .cb-bf-counter-number {
    color: #00a2ff;
    font-size: 50px;
    line-height: 50px;
    font-weight: 700;
}

.cb-main__card__inner.new_card .cb-bf-counter-last {
    color: #00a2ff;
    font-weight: 700;
    text-transform: uppercase;
    line-height: normal;
    padding: 0 10px;
}

/* Banner Container Meeting cb-wrapper */
.banner-container {
    max-width: 1366px;
    width: 100%;
    margin: -1.5rem auto 1.5rem;
}

.header-top-banners {
    display: flex;
    background-position: center;
    border-radius: 0.5rem;
    align-items: center; 
    width: 100%;
    margin-bottom: 1rem;
}

.header-top-banners.trial-expired-banner {
    background-color: #FDECCF;
    justify-content: space-between;
}

.header-top-banners.connected-banner {
    background-color: rgba(245, 245, 247, 1);
}

.header-top-banners.banner-live-banner {
    background-color: #F5F5F7;
}

.header-top-banners.banner-live-banner h3 {
    color: black;
}

.header-top-banners.banner-live-banner p {
    color: black;
    text-wrap: nowrap;
}

.header-top-banners img {
    width: 24px;
    height: 24px;
    flex-shrink: 0;
}

.header-top-banners .banner-content {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1.5rem 2rem;
    flex: 1;
}

.header-top-banners h3 {
    margin: 0 0 0.25rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1D2327;
    line-height: 1.3;
}

.header-top-banners p {
    margin: 0;
    font-size: 0.875rem;
    line-height: 1.4;
    color: #4B5563;
    max-width: 90%;
}

.header-top-banners .upgrade-expired-trial {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 1.5rem 2rem;
}


/* Banner Close Button */
.banner-close-btn {
    background: none;
    border: none;
    color: black;
    font-size: 24px;
    line-height: 1;
    padding: 0 20px;
    cursor: pointer;
    opacity: 0.8;
    transition: opacity 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.banner-close-btn:hover {
    opacity: 1;
}

.banner-close-btn span {
    font-weight: lighter;
    font-size: 28px;
}
