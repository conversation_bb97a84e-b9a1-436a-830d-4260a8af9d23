.cookiebot-admin-notice-container {
    background: #efefef;
    padding: 40px 25px 25px;
}
.cookiebot-admin-notice {
    display: block;
    min-height: 68px;
    position: relative;
    background: #ffffff;
    padding: 20px 60px 20px 160px;
    border-radius: 25px;
    border: none;
    margin: 0;
}
.cookiebot-admin-notice a:not(.cb-btn) {
    color: #1032cf;
    font-size: 16px;
    text-decoration: none;
}

.cookiebot-admin-notice a.cb-btn {
    display: inline-block;
    margin-top: 18px;
    text-decoration: none;
    padding: 15px;
    font-size: 15px;
    line-height: 20px;
    font-weight: 500;
    border-radius: 7px;
    border: none;
    cursor: pointer;
    box-shadow: unset;
    outline: unset;
    background-color: #1032cf;
    color: #ffffff;
}

.cookiebot-notice-icon {
    clear: both;
    content: "";
    display: block;
    background-size: 108px auto;
    background-repeat: no-repeat;
    background-position: 0 45%;
    width: 125px;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 25px;
}

.cookiebot-notice-logo {
    background-image: url("../img/icon.svg");
}

.cookiebot-notice-review {
    background-image: url("../img/icons/rating.png");
}

.cookiebot-notice-bf {
    background-image: url("../img/icons/cb-bf-notice.svg");
}

.cb-bf-links a.cb-btn{
    margin: 0;
}

.cookiebot-admin-notice > .dashicons {
    color: #424242;
    position: absolute;
    right: 20px;
    top: 50%;
    font-size: 20px;
    transform: translateY(-50%);
}
.cookiebot-notice-title {
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    color: #141414;
}
.cookiebot-notice-body {
    font-weight: normal;
    font-size: 16px;
    line-height: 24px;
    margin: 18px 0 0;
    color: #141414;
}
.cookiebot-notice-body:after {
    clear: both;
    content: "";
    display: block;
}
.cookiebot-notice-body li {
    float: left;
    margin-right: 20px;
}
.cookiebot-notice-body .dashicons {
    font-size: 20px;
}
.cookiebot-blue {
    color: #10738B;
}

.admin-notice-promo {
    background: #fff;
    border-top: 4px;
    display: block;
    min-height: 68px;
    padding: 10px 40px 0px 10px;
    margin: 10px 0;
    position: relative;
}

.admin-notice-promo:after {
    content: '';
    display: table;
    clear: both;
}

.close-promo {
    position: absolute;
    top: 5px;
    right: 5px;
}

.promo {
    background-position: center;
    background-repeat: no-repeat;
    background-size: 972px 200px;
    height: 200px;
    max-width: 100%;
}

.promo a {
    display: block;
    height: 100%;
}